const mongoose = require('mongoose');

const jobCategorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true
    },
    icon: {
        type: String
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Index for faster queries
jobCategorySchema.index({ name: 1 }, { unique: true });

module.exports = mongoose.model('JobCategory', jobCategorySchema);
