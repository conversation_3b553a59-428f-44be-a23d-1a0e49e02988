/* Job Card Compact Style */

:root {
    --primary-color: #3b82f6;
    --primary-light: #e6f7ff;
    --primary-dark: #1d4ed8;
    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #777777;
    --border-color: #e0e0e0;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-lighter: #f2f2f2;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
    --radius-sm: 4px;
    --radius-md: 6px;
}

/* Job Card */
.job-card-compact {
    display: flex;
    align-items: center;
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    position: relative;
    transition: all 0.2s ease;
}

.job-card-compact:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.job-card-logo {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
    object-fit: contain;
    border: 1px solid var(--border-color);
    padding: 0.2rem;
    background-color: var(--bg-white);
    margin-right: 0.75rem;
}

.job-card-content {
    flex: 1;
    min-width: 0;
}

.job-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.2rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.job-card-company {
    font-size: 0.8rem;
    color: var(--text-medium);
    margin-bottom: 0.3rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.job-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.job-card-tag {
    font-size: 0.7rem;
    color: var(--text-medium);
    background-color: var(--bg-lighter);
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
}

.job-card-info {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-left: 0.75rem;
}

.job-card-location {
    font-size: 0.75rem;
    color: #00a550;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.job-card-location i {
    font-size: 0.7rem;
    margin-right: 0.2rem;
    color: #00a550;
}

.job-card-days {
    font-size: 0.75rem;
    color: #00a550;
    white-space: nowrap;
    margin-left: 0.75rem;
    display: flex;
    align-items: center;
}

.job-card-days i {
    font-size: 0.7rem;
    margin-right: 0.2rem;
    color: #00a550;
}

.job-card-actions {
    display: flex;
    align-items: center;
    margin-left: 0.75rem;
}

.job-card-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-card-btn:hover {
    background-color: var(--primary-dark);
}

.job-card-save {
    margin-left: 0.5rem;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    border: 1px solid var(--border-color);
    background-color: var(--bg-white);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
}

.job-card-save:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.job-card-save.saved {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

/* Responsive */
@media (max-width: 768px) {
    .job-card-compact {
        flex-wrap: wrap;
    }

    .job-card-info {
        width: 100%;
        margin-left: 0;
        padding-left: 0;
        margin-top: 0.5rem;
        justify-content: space-between;
    }
}
