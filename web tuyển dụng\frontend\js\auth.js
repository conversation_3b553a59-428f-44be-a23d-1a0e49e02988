// Hiển thị lỗi
function showError(message) {
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    } else {
        alert(message);
    }
}

// X<PERSON> lý đăng ký
async function handleRegister(event) {
    event.preventDefault();
    
    // Ẩn thông báo lỗi cũ nếu có
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
    
    const fullName = document.getElementById('fullName').value.trim();
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const phone = document.getElementById('phone')?.value.trim() || '';

    // Validate input
    if (!fullName || !email || !password) {
        showError('<PERSON>ui lòng điền đầy đủ thông tin bắt buộc');
        return false;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showError('Email không hợp lệ');
        return false;
    }

    // Validate password length
    if (password.length < 6) {
        showError('Mật khẩu phải có ít nhất 6 ký tự');
        return false;
    }

    // Kiểm tra mật khẩu xác nhận
    if (password !== confirmPassword) {
        showError('Mật khẩu xác nhận không khớp!');
        return false;
    }

    try {
        console.log('Sending registration request...');
        const data = await apiRequest(API_CONFIG.ENDPOINTS.AUTH.REGISTER, {
            method: 'POST',
            skipAuth: true,
            body: JSON.stringify({
                fullName,
                email,
                password,
                phone
            })
        });

        // Lưu token vào localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));

        // Chuyển hướng sau khi đăng ký thành công
        window.location.href = '/dashboard.html';
    } catch (error) {
        console.error('Registration error:', error);
        showError(error.message || 'Có lỗi xảy ra khi đăng ký');
    }
    return false;
}

// Xử lý đăng nhập
async function handleLogin(event) {
    event.preventDefault();
    
    // Ẩn thông báo lỗi cũ nếu có
    const errorDiv = document.getElementById('error-message');
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
    
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;

    // Validate input
    if (!email || !password) {
        showError('Vui lòng điền đầy đủ email và mật khẩu');
        return false;
    }

    try {
        console.log('Sending login request...');
        const data = await apiRequest(API_CONFIG.ENDPOINTS.AUTH.LOGIN, {
            method: 'POST',
            skipAuth: true,
            body: JSON.stringify({
                email,
                password
            })
        });

        // Lưu token vào localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));

        // Chuyển hướng sau khi đăng nhập thành công
        window.location.href = '/dashboard.html';
    } catch (error) {
        console.error('Login error:', error);
        showError(error.message || 'Có lỗi xảy ra khi đăng nhập');
    }
    return false;
}

// Kiểm tra đã đăng nhập chưa
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login.html';
    }
}

// Đăng xuất
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = '/login.html';
} 