import pandas as pd
import glob
import os

# Code từ hình ảnh
path = "."
all_files = glob.glob(os.path.join(path, "Sales_*.csv"))
print(f"Found files: {all_files}")

# Merge all CSV files into one DataFrame
df = pd.concat([pd.read_csv(f) for f in all_files], ignore_index=True)

# Drop rows with any missing values
df.dropna(how='any', inplace=True)

# Remove rows that are likely duplicate headers
df = df[~df['Order Date'].str.contains("Order Date", na=False)]
print(f"Shape after cleaning: {df.shape}")
print(df.head())

# Test data processing
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']

print(f"✅ Success! Total records: {len(df):,}")
print(f"💰 Total Sales: ${df['Sales'].sum():,.0f}")
