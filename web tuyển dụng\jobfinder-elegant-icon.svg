<?xml version="1.0" encoding="UTF-8"?>
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient background -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#0277BD"/>
      <stop offset="100%" stop-color="#01579B"/>
    </linearGradient>
    <filter id="shadow" x="-2" y="0" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
  
  <!-- Main circle with gradient -->
  <circle cx="20" cy="20" r="20" fill="url(#paint0_linear)"/>
  
  <!-- Stylized J and F letters -->
  <path d="M14 10V22C14 24.2091 15.7909 26 18 26H22" stroke="#FFFFFF" stroke-width="2.5" stroke-linecap="round"/>
  <path d="M18 10H26" stroke="#FFFFFF" stroke-width="2.5" stroke-linecap="round"/>
  <path d="M18 17H24" stroke="#FFFFFF" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Magnifying glass -->
  <circle cx="27" cy="22" r="5" stroke="#FFCC00" stroke-width="2"/>
  <path d="M30.5 25.5L34 29" stroke="#FFCC00" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Decorative elements -->
  <circle cx="27" cy="22" r="2" fill="#FFFFFF" fill-opacity="0.3"/>
</svg>
