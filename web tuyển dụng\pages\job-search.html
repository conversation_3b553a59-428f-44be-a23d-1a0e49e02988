<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tìm kiếm việc làm - <PERSON>Finder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/utilities.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/modern-header.css">
    <link rel="stylesheet" href="css/job-search.css">
    <link rel="stylesheet" href="css/job-card-horizontal.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Job Search Page -->
    <main class="section">
        <div class="container container-xl">
            <header class="text-center mb-8">
                <h1 class="text-3xl font-bold mb-3">Tìm kiếm việc làm</h1>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Khám phá hàng ngàn cơ hội việc làm phù hợp với kỹ năng và mong muốn của bạn</p>
            </header>

            <div class="card mb-8">
                <div class="card-body">
                    <div class="flex flex-col md:flex-row gap-4 mb-6">
                        <div class="relative flex-1">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" class="search-input w-full pl-10" id="search-input" placeholder="Nhập từ khóa, vị trí, công ty...">
                        </div>
                        <button type="button" class="btn btn-primary" id="search-button">
                            <i class="fas fa-search mr-2"></i>
                            <span>Tìm kiếm</span>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="filter-select">
                            <select id="location-filter" class="w-full">
                                <option value="">Địa điểm</option>
                                <option value="Hà Nội">Hà Nội</option>
                                <option value="Hồ Chí Minh">Hồ Chí Minh</option>
                                <option value="Đà Nẵng">Đà Nẵng</option>
                                <option value="Hải Phòng">Hải Phòng</option>
                                <option value="Cần Thơ">Cần Thơ</option>
                            </select>
                        </div>

                        <div class="filter-select">
                            <select id="salary-filter" class="w-full">
                                <option value="">Mức lương</option>
                                <option value="0-5000000">Dưới 5 triệu</option>
                                <option value="5000000-10000000">5 - 10 triệu</option>
                                <option value="10000000-20000000">10 - 20 triệu</option>
                                <option value="20000000-30000000">20 - 30 triệu</option>
                                <option value="30000000">Trên 30 triệu</option>
                            </select>
                        </div>

                        <div class="filter-select">
                            <select id="experience-filter" class="w-full">
                                <option value="">Kinh nghiệm</option>
                                <option value="0">Chưa có kinh nghiệm</option>
                                <option value="1">1 năm</option>
                                <option value="2">2 năm</option>
                                <option value="3-5">3 - 5 năm</option>
                                <option value="5">Trên 5 năm</option>
                            </select>
                        </div>

                        <div class="filter-select">
                            <select id="job-type-filter" class="w-full">
                                <option value="">Loại hình</option>
                                <option value="Toàn thời gian">Toàn thời gian</option>
                                <option value="Bán thời gian">Bán thời gian</option>
                                <option value="Thực tập">Thực tập</option>
                                <option value="Freelance">Freelance</option>
                                <option value="Remote">Remote</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-end mt-4">
                        <button type="button" class="btn btn-outline" id="reset-filters">Đặt lại</button>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Advanced Filters Sidebar -->
                <aside class="md:col-span-1">
                    <div class="card sticky top-4">
                        <div class="card-header">
                            <h2 class="text-xl font-semibold">Bộ lọc nâng cao</h2>
                        </div>
                        <div class="card-body">
                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-3">Ngành nghề</h3>
                                <div class="flex flex-col gap-3">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="category-it" name="category" value="IT">
                                        <label for="category-it">Công nghệ thông tin</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="category-finance" name="category" value="Finance">
                                        <label for="category-finance">Tài chính - Ngân hàng</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="category-marketing" name="category" value="Marketing">
                                        <label for="category-marketing">Marketing</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="category-sales" name="category" value="Sales">
                                        <label for="category-sales">Bán hàng</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="category-engineering" name="category" value="Engineering">
                                        <label for="category-engineering">Kỹ thuật</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-3">Cấp bậc</h3>
                                <div class="flex flex-col gap-3">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="level-intern" name="level" value="Intern">
                                        <label for="level-intern">Thực tập sinh</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="level-fresher" name="level" value="Fresher">
                                        <label for="level-fresher">Mới tốt nghiệp</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="level-junior" name="level" value="Junior">
                                        <label for="level-junior">Nhân viên</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="level-senior" name="level" value="Senior">
                                        <label for="level-senior">Trưởng nhóm / Quản lý</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="level-manager" name="level" value="Manager">
                                        <label for="level-manager">Giám đốc</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-3">Kỹ năng</h3>
                                <div class="flex flex-col gap-3">
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="skill-javascript" name="skill" value="JavaScript">
                                        <label for="skill-javascript">JavaScript</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="skill-java" name="skill" value="Java">
                                        <label for="skill-java">Java</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="skill-python" name="skill" value="Python">
                                        <label for="skill-python">Python</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="skill-react" name="skill" value="React">
                                        <label for="skill-react">React</label>
                                    </div>
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="skill-nodejs" name="skill" value="Node.js">
                                        <label for="skill-nodejs">Node.js</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="flex gap-3 justify-between">
                                <button type="button" class="btn btn-outline" id="clear-advanced-filters">Xóa bộ lọc</button>
                                <button type="button" class="btn btn-primary" id="apply-advanced-filters">Áp dụng</button>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Job Results -->
                <div class="md:col-span-3">
                    <div class="card">
                        <div class="card-header flex justify-between items-center">
                            <h2 class="text-xl font-semibold">Kết quả tìm kiếm</h2>
                            <div class="text-sm text-gray-500">Tìm thấy <span id="job-count">0</span> việc làm</div>
                        </div>
                        <div class="card-body">
                            <div id="job-results" class="space-y-4">
                                <!-- Job results will be loaded here -->
                                <div class="text-center py-8 text-gray-500">
                                    <i class="fas fa-search text-4xl mb-3"></i>
                                    <p>Hãy nhập từ khóa và bấm tìm kiếm để xem kết quả</p>
                                </div>

                                <!-- Example Job Card Horizontal 1 -->
                                <div class="job-card-horizontal">
                                    <div class="company-logo-container">
                                        <img src="images/fpt.webp" alt="FPT Software" class="company-logo">
                                    </div>
                                    <div class="job-content">
                                        <div>
                                            <h3 class="job-title">Senior Frontend Developer</h3>
                                            <div class="company-name">
                                                <i class="fas fa-building"></i>
                                                FPT Software
                                            </div>
                                            <div class="job-details">
                                                <div class="job-detail-item">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    Hà Nội
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-briefcase"></i>
                                                    Toàn thời gian
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-graduation-cap"></i>
                                                    3 năm kinh nghiệm
                                                </div>
                                            </div>
                                            <div class="job-skills">
                                                <span class="job-skill">JavaScript</span>
                                                <span class="job-skill">React</span>
                                                <span class="job-skill">Vue.js</span>
                                                <span class="job-skill">HTML/CSS</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-actions">
                                        <div class="job-actions-content">
                                            <div class="job-salary">20 - 30 triệu</div>
                                            <a href="#" class="job-apply-btn">Ứng tuyển</a>
                                            <button class="job-save-btn">
                                                <i class="far fa-heart"></i>
                                                Lưu tin
                                            </button>
                                        </div>
                                    </div>
                                    <div class="job-deadline">
                                        <i class="fas fa-clock"></i>
                                        Còn 15 ngày
                                    </div>
                                </div>

                                <!-- Example Job Card Horizontal 2 -->
                                <div class="job-card-horizontal">
                                    <div class="company-logo-container">
                                        <img src="images/vetc.webp" alt="VETC" class="company-logo">
                                    </div>
                                    <div class="job-content">
                                        <div>
                                            <h3 class="job-title">Backend Developer (Java)</h3>
                                            <div class="company-name">
                                                <i class="fas fa-building"></i>
                                                VETC
                                            </div>
                                            <div class="job-details">
                                                <div class="job-detail-item">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    Hồ Chí Minh
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-briefcase"></i>
                                                    Toàn thời gian
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-graduation-cap"></i>
                                                    2 năm kinh nghiệm
                                                </div>
                                            </div>
                                            <div class="job-skills">
                                                <span class="job-skill">Java</span>
                                                <span class="job-skill">Spring Boot</span>
                                                <span class="job-skill">Microservices</span>
                                                <span class="job-skill">SQL</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-actions">
                                        <div class="job-actions-content">
                                            <div class="job-salary">25 - 35 triệu</div>
                                            <a href="#" class="job-apply-btn">Ứng tuyển</a>
                                            <button class="job-save-btn">
                                                <i class="far fa-heart"></i>
                                                Lưu tin
                                            </button>
                                        </div>
                                    </div>
                                    <div class="job-deadline">
                                        <i class="fas fa-clock"></i>
                                        Còn 10 ngày
                                    </div>
                                </div>

                                <!-- Example Job Card Horizontal 3 -->
                                <div class="job-card-horizontal">
                                    <div class="company-logo-container">
                                        <img src="images/kone.webp" alt="KONE" class="company-logo">
                                    </div>
                                    <div class="job-content">
                                        <div>
                                            <h3 class="job-title">Kỹ sư Cơ khí</h3>
                                            <div class="company-name">
                                                <i class="fas fa-building"></i>
                                                KONE
                                            </div>
                                            <div class="job-details">
                                                <div class="job-detail-item">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    Đà Nẵng
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-briefcase"></i>
                                                    Toàn thời gian
                                                </div>
                                                <div class="job-detail-item">
                                                    <i class="fas fa-graduation-cap"></i>
                                                    1 năm kinh nghiệm
                                                </div>
                                            </div>
                                            <div class="job-skills">
                                                <span class="job-skill">AutoCAD</span>
                                                <span class="job-skill">SolidWorks</span>
                                                <span class="job-skill">Thiết kế cơ khí</span>
                                                <span class="job-skill">Tiếng Anh</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-actions">
                                        <div class="job-actions-content">
                                            <div class="job-salary">15 - 25 triệu</div>
                                            <a href="#" class="job-apply-btn">Ứng tuyển</a>
                                            <button class="job-save-btn">
                                                <i class="far fa-heart"></i>
                                                Lưu tin
                                            </button>
                                        </div>
                                    </div>
                                    <div class="job-deadline">
                                        <i class="fas fa-clock"></i>
                                        Còn 20 ngày
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Modals -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/jobs.js"></script>
    <script src="js/job-card-horizontal.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Reset basic filters
            document.getElementById('reset-filters').addEventListener('click', function() {
                document.getElementById('search-input').value = '';
                document.getElementById('location-filter').selectedIndex = 0;
                document.getElementById('salary-filter').selectedIndex = 0;
                document.getElementById('experience-filter').selectedIndex = 0;
                document.getElementById('job-type-filter').selectedIndex = 0;
            });

            // Clear advanced filters
            document.getElementById('clear-advanced-filters').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('.advanced-filters input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            });

            // Apply advanced filters
            document.getElementById('apply-advanced-filters').addEventListener('click', function() {
                // Get all checked values
                const categories = Array.from(document.querySelectorAll('input[name="category"]:checked')).map(el => el.value);
                const levels = Array.from(document.querySelectorAll('input[name="level"]:checked')).map(el => el.value);
                const skills = Array.from(document.querySelectorAll('input[name="skill"]:checked')).map(el => el.value);

                console.log('Applied filters:', { categories, levels, skills });
                // Here you would typically filter the job results based on these values
            });

            // Search button
            document.getElementById('search-button').addEventListener('click', function() {
                const keyword = document.getElementById('search-input').value;
                const location = document.getElementById('location-filter').value;
                const salary = document.getElementById('salary-filter').value;
                const experience = document.getElementById('experience-filter').value;
                const jobType = document.getElementById('job-type-filter').value;

                console.log('Search criteria:', { keyword, location, salary, experience, jobType });
                // Here you would typically perform the search based on these criteria
            });
        });
    </script>
</body>
</html>
