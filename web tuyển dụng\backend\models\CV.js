const { pool, sql } = require('../config/db');

class CV {
    // Tạo CV mới từ mẫu
    static async create(userId, cvData) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('user_id', sql.Int, userId);
            request.input('profile_id', sql.Int, cvData.profileId || null);
            request.input('template_id', sql.Int, cvData.templateId);
            request.input('title', sql.NVarChar(100), cvData.title);
            request.input('content', sql.NVarChar(sql.MAX), cvData.content || null);
            request.input('cv_path', sql.NVarChar(255), cvData.cvPath || null);
            request.input('thumbnail_path', sql.NVarChar(255), cvData.thumbnailPath || null);
            request.input('is_primary', sql.Bit, cvData.isPrimary || 0);
            request.input('is_public', sql.Bit, cvData.isPublic || 0);
            
            const result = await request.execute('sp_CreateUserCV');
            
            if (result.recordset && result.recordset.length > 0) {
                return this.formatCV(result.recordset[0]);
            }
            
            throw new Error('Không thể tạo CV');
        } catch (error) {
            console.error('CV create error:', error);
            throw error;
        }
    }
    
    // Cập nhật CV
    static async update(cvId, userId, cvData) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('cv_id', sql.Int, cvId);
            request.input('user_id', sql.Int, userId);
            request.input('profile_id', sql.Int, cvData.profileId || null);
            request.input('title', sql.NVarChar(100), cvData.title || null);
            request.input('content', sql.NVarChar(sql.MAX), cvData.content || null);
            request.input('cv_path', sql.NVarChar(255), cvData.cvPath || null);
            request.input('thumbnail_path', sql.NVarChar(255), cvData.thumbnailPath || null);
            request.input('is_primary', sql.Bit, cvData.isPrimary !== undefined ? cvData.isPrimary : null);
            request.input('is_public', sql.Bit, cvData.isPublic !== undefined ? cvData.isPublic : null);
            
            const result = await request.execute('sp_UpdateUserCV');
            
            if (result.recordset && result.recordset.length > 0) {
                return this.formatCV(result.recordset[0]);
            }
            
            throw new Error('Không thể cập nhật CV');
        } catch (error) {
            console.error('CV update error:', error);
            throw error;
        }
    }
    
    // Lấy danh sách CV của người dùng
    static async getByUserId(userId) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('user_id', sql.Int, userId);
            
            const result = await request.execute('sp_GetUserCVs');
            
            return result.recordset ? result.recordset.map(cv => this.formatCV(cv)) : [];
        } catch (error) {
            console.error('CV getByUserId error:', error);
            throw error;
        }
    }
    
    // Lấy thông tin chi tiết CV
    static async getDetail(cvId, userId) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('cv_id', sql.Int, cvId);
            request.input('user_id', sql.Int, userId);
            
            const result = await request.execute('sp_GetUserCVDetail');
            
            if (result.recordset && result.recordset.length > 0) {
                return this.formatCVDetail(result.recordset[0]);
            }
            
            return null;
        } catch (error) {
            console.error('CV getDetail error:', error);
            throw error;
        }
    }
    
    // Xóa CV
    static async delete(cvId, userId) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('cv_id', sql.Int, cvId);
            request.input('user_id', sql.Int, userId);
            
            const result = await request.execute('sp_DeleteUserCV');
            
            return result.recordset && result.recordset.length > 0 ? result.recordset[0] : { message: 'CV đã được xóa' };
        } catch (error) {
            console.error('CV delete error:', error);
            throw error;
        }
    }
    
    // Tìm kiếm CV
    static async search(userId, keyword) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('user_id', sql.Int, userId);
            request.input('keyword', sql.NVarChar(100), keyword || null);
            
            const result = await request.execute('sp_SearchUserCVs');
            
            return result.recordset ? result.recordset.map(cv => this.formatCV(cv)) : [];
        } catch (error) {
            console.error('CV search error:', error);
            throw error;
        }
    }
    
    // Tạo liên kết chia sẻ CV
    static async createShareLink(userId, cvId, expiryDays = 7, isPasswordProtected = false, passwordHash = null) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('user_id', sql.Int, userId);
            request.input('cv_id', sql.Int, cvId);
            request.input('expiry_days', sql.Int, expiryDays);
            request.input('is_password_protected', sql.Bit, isPasswordProtected ? 1 : 0);
            request.input('password_hash', sql.NVarChar(255), passwordHash);
            
            const result = await request.execute('sp_CreateCVShareLink');
            
            if (result.recordset && result.recordset.length > 0) {
                return {
                    id: result.recordset[0].id,
                    userId: result.recordset[0].user_id,
                    cvId: result.recordset[0].cv_id,
                    shareToken: result.recordset[0].share_token,
                    expiryDate: result.recordset[0].expiry_date,
                    isPasswordProtected: result.recordset[0].is_password_protected === 1,
                    createdAt: result.recordset[0].created_at
                };
            }
            
            throw new Error('Không thể tạo liên kết chia sẻ');
        } catch (error) {
            console.error('CV createShareLink error:', error);
            throw error;
        }
    }
    
    // Lấy CV từ liên kết chia sẻ
    static async getFromShareLink(shareToken, passwordHash = null) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('share_token', sql.NVarChar(100), shareToken);
            request.input('password_hash', sql.NVarChar(255), passwordHash);
            
            const result = await request.execute('sp_GetSharedCV');
            
            if (result.recordset && result.recordset.length > 0) {
                return this.formatCVDetail(result.recordset[0]);
            }
            
            return null;
        } catch (error) {
            console.error('CV getFromShareLink error:', error);
            throw error;
        }
    }
    
    // Ghi lại lịch sử tải xuống CV
    static async logDownload(userId, cvId, downloadFormat) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('user_id', sql.Int, userId);
            request.input('cv_id', sql.Int, cvId);
            request.input('download_format', sql.NVarChar(10), downloadFormat);
            
            const result = await request.execute('sp_LogCVDownload');
            
            return result.recordset && result.recordset.length > 0 ? result.recordset[0] : { message: 'Đã ghi lại lịch sử tải xuống' };
        } catch (error) {
            console.error('CV logDownload error:', error);
            throw error;
        }
    }
    
    // Định dạng dữ liệu CV
    static formatCV(cv) {
        return {
            id: cv.id,
            userId: cv.user_id,
            profileId: cv.profile_id,
            templateId: cv.template_id,
            title: cv.title,
            content: cv.content,
            cvPath: cv.cv_path,
            thumbnailPath: cv.thumbnail_path,
            isPrimary: cv.is_primary === 1,
            isPublic: cv.is_public === 1,
            templateName: cv.template_name,
            previewImage: cv.preview_image,
            category: cv.category,
            createdAt: cv.created_at,
            updatedAt: cv.updated_at
        };
    }
    
    // Định dạng dữ liệu chi tiết CV
    static formatCVDetail(cv) {
        return {
            id: cv.id,
            userId: cv.user_id,
            profileId: cv.profile_id,
            templateId: cv.template_id,
            title: cv.title,
            content: cv.content,
            cvPath: cv.cv_path,
            thumbnailPath: cv.thumbnail_path,
            isPrimary: cv.is_primary === 1,
            isPublic: cv.is_public === 1,
            templateName: cv.template_name,
            previewImage: cv.preview_image,
            category: cv.category,
            htmlTemplate: cv.html_template,
            cssTemplate: cv.css_template,
            profile: {
                fullName: cv.full_name,
                email: cv.email,
                phone: cv.phone,
                address: cv.address,
                introduction: cv.introduction,
                skills: cv.skills,
                experience: cv.experience,
                education: cv.education,
                certifications: cv.certifications,
                languages: cv.languages
            },
            createdAt: cv.created_at,
            updatedAt: cv.updated_at
        };
    }
}

module.exports = CV;
