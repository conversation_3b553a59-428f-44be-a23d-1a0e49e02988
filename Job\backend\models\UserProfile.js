const mongoose = require('mongoose');

const userProfileSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    title: {
        type: String,
        required: true,
        trim: true
    },
    fullName: {
        type: String,
        required: true,
        trim: true
    },
    email: {
        type: String,
        required: true,
        trim: true,
        lowercase: true
    },
    phone: {
        type: String,
        trim: true
    },
    address: {
        type: String,
        trim: true
    },
    dateOfBirth: {
        type: Date
    },
    gender: {
        type: String,
        enum: ['Nam', 'Nữ', 'Khác']
    },
    introduction: {
        type: String
    },
    skills: [{
        type: String,
        trim: true
    }],
    experience: {
        type: String
    },
    education: {
        type: String
    },
    certifications: {
        type: String
    },
    languages: {
        type: String
    },
    avatarPath: {
        type: String
    },
    isPublic: {
        type: Boolean,
        default: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Cập nhật thời gian khi cập nhật hồ sơ
userProfileSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Tìm kiếm hồ sơ theo userId
userProfileSchema.statics.findByUserId = async function(userId) {
    try {
        return await this.findOne({ userId });
    } catch (error) {
        console.error('Lỗi khi tìm hồ sơ theo userId:', error);
        throw error;
    }
};

// Tìm kiếm hồ sơ theo kỹ năng
userProfileSchema.statics.findBySkills = async function(skills) {
    try {
        return await this.find({ skills: { $in: skills } });
    } catch (error) {
        console.error('Lỗi khi tìm hồ sơ theo kỹ năng:', error);
        throw error;
    }
};

// Tìm kiếm hồ sơ theo từ khóa
userProfileSchema.statics.search = async function(keyword) {
    try {
        const regex = new RegExp(keyword, 'i');
        return await this.find({
            $or: [
                { title: regex },
                { fullName: regex },
                { introduction: regex },
                { skills: regex },
                { experience: regex },
                { education: regex }
            ]
        });
    } catch (error) {
        console.error('Lỗi khi tìm kiếm hồ sơ:', error);
        throw error;
    }
};

module.exports = mongoose.model('UserProfile', userProfileSchema, 'user_profiles');
