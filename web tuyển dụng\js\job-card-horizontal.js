// JavaScript for horizontal job cards

document.addEventListener('DOMContentLoaded', function() {
    // Get job results container
    const jobResults = document.getElementById('job-results');
    
    // Function to create a horizontal job card
    function createHorizontalJobCard(job) {
        // Create skills HTML
        const skillsHTML = job.skills.map(skill => 
            `<span class="job-skill">${skill}</span>`
        ).join('');
        
        // Format deadline
        const deadline = new Date(job.deadline);
        const today = new Date();
        const daysLeft = Math.ceil((deadline - today) / (1000 * 60 * 60 * 24));
        
        // Create job card HTML
        const jobCardHTML = `
            <div class="job-card-horizontal">
                <div class="company-logo-container">
                    <img src="${job.company.logo || 'images/company-placeholder.png'}" alt="${job.company.name}" class="company-logo">
                </div>
                <div class="job-content">
                    <div>
                        <h3 class="job-title">${job.title}</h3>
                        <div class="company-name">
                            <i class="fas fa-building"></i>
                            ${job.company.name}
                        </div>
                        <div class="job-details">
                            <div class="job-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                ${job.location}
                            </div>
                            <div class="job-detail-item">
                                <i class="fas fa-briefcase"></i>
                                ${job.type}
                            </div>
                            <div class="job-detail-item">
                                <i class="fas fa-graduation-cap"></i>
                                ${job.experience}
                            </div>
                        </div>
                        <div class="job-skills">
                            ${skillsHTML}
                        </div>
                    </div>
                </div>
                <div class="job-actions">
                    <div class="job-actions-content">
                        <div class="job-salary">${job.salaryHidden ? 'Thỏa thuận' : `${job.salaryMin.toLocaleString()} - ${job.salaryMax.toLocaleString()}`}</div>
                        <a href="#" class="job-apply-btn" data-job-id="${job._id}">Ứng tuyển</a>
                        <button class="job-save-btn" data-job-id="${job._id}">
                            <i class="far fa-heart"></i>
                            Lưu tin
                        </button>
                    </div>
                </div>
                <div class="job-deadline">
                    <i class="fas fa-clock"></i>
                    ${daysLeft > 0 ? `Còn ${daysLeft} ngày` : 'Hết hạn'}
                </div>
            </div>
        `;
        
        return jobCardHTML;
    }
    
    // Function to render job results
    function renderJobResults(jobs) {
        if (!jobResults) return;
        
        // Clear job results
        jobResults.innerHTML = '';
        
        // Update job count
        const jobCount = document.getElementById('job-count');
        if (jobCount) {
            jobCount.textContent = jobs.length;
        }
        
        if (jobs.length === 0) {
            jobResults.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-search text-4xl mb-3"></i>
                    <p>Không tìm thấy việc làm phù hợp</p>
                </div>
            `;
            return;
        }
        
        // Create job cards
        jobs.forEach(job => {
            const jobCardHTML = createHorizontalJobCard(job);
            jobResults.innerHTML += jobCardHTML;
        });
        
        // Add event listeners to job cards
        addJobCardEventListeners();
    }
    
    // Function to add event listeners to job cards
    function addJobCardEventListeners() {
        // Apply buttons
        const applyButtons = document.querySelectorAll('.job-apply-btn');
        applyButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const jobId = this.getAttribute('data-job-id');
                console.log('Apply for job:', jobId);
                // Here you would typically show a job application modal
            });
        });
        
        // Save buttons
        const saveButtons = document.querySelectorAll('.job-save-btn');
        saveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const jobId = this.getAttribute('data-job-id');
                const heartIcon = this.querySelector('i');
                
                // Toggle heart icon
                heartIcon.classList.toggle('far');
                heartIcon.classList.toggle('fas');
                
                console.log('Save job:', jobId);
                // Here you would typically call an API to save the job
            });
        });
    }
    
    // Example jobs data for testing
    const exampleJobs = [
        {
            _id: '1',
            title: 'Senior Frontend Developer',
            company: {
                name: 'FPT Software',
                logo: 'images/fpt.webp'
            },
            location: 'Hà Nội',
            type: 'Toàn thời gian',
            experience: '3 năm kinh nghiệm',
            skills: ['JavaScript', 'React', 'Vue.js', 'HTML/CSS'],
            salaryMin: 20000000,
            salaryMax: 30000000,
            salaryHidden: false,
            deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000) // 15 days from now
        },
        {
            _id: '2',
            title: 'Backend Developer (Java)',
            company: {
                name: 'VETC',
                logo: 'images/vetc.webp'
            },
            location: 'Hồ Chí Minh',
            type: 'Toàn thời gian',
            experience: '2 năm kinh nghiệm',
            skills: ['Java', 'Spring Boot', 'Microservices', 'SQL'],
            salaryMin: 25000000,
            salaryMax: 35000000,
            salaryHidden: false,
            deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000) // 10 days from now
        },
        {
            _id: '3',
            title: 'Kỹ sư Cơ khí',
            company: {
                name: 'KONE',
                logo: 'images/kone.webp'
            },
            location: 'Đà Nẵng',
            type: 'Toàn thời gian',
            experience: '1 năm kinh nghiệm',
            skills: ['AutoCAD', 'SolidWorks', 'Thiết kế cơ khí', 'Tiếng Anh'],
            salaryMin: 15000000,
            salaryMax: 25000000,
            salaryHidden: false,
            deadline: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000) // 20 days from now
        }
    ];
    
    // Initialize search functionality
    function initSearch() {
        const searchButton = document.getElementById('search-button');
        const searchInput = document.getElementById('search-input');
        const locationFilter = document.getElementById('location-filter');
        const salaryFilter = document.getElementById('salary-filter');
        const experienceFilter = document.getElementById('experience-filter');
        const jobTypeFilter = document.getElementById('job-type-filter');
        
        if (searchButton) {
            searchButton.addEventListener('click', function() {
                // In a real application, you would filter the jobs based on the search criteria
                // For now, we'll just log the search criteria and render the example jobs
                console.log('Search criteria:', {
                    keyword: searchInput.value,
                    location: locationFilter.value,
                    salary: salaryFilter.value,
                    experience: experienceFilter.value,
                    jobType: jobTypeFilter.value
                });
                
                renderJobResults(exampleJobs);
            });
        }
    }
    
    // Initialize the page
    function init() {
        // Add event listeners to job cards
        addJobCardEventListeners();
        
        // Initialize search functionality
        initSearch();
    }
    
    // Call init function
    init();
});
