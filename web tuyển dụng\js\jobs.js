// Jobs JavaScript file

// API URL
const API_URL = 'http://localhost:5000/api';

// DOM Elements
const jobList = document.getElementById('job-list');
const searchForm = document.getElementById('search-form');
const searchInput = document.getElementById('search-input');
const locationFilter = document.getElementById('location-filter');
const salaryFilter = document.getElementById('salary-filter');
const experienceFilter = document.getElementById('experience-filter');
const jobTypeFilter = document.getElementById('job-type-filter');
const resetFilterBtn = document.getElementById('reset-filter');
const jobDetailModal = document.getElementById('job-detail-modal');
const jobDetailContent = document.getElementById('job-detail-content');
const applyJobForm = document.getElementById('apply-job-form');

// Get all jobs
async function getJobs(filters = {}) {
    try {
        // Build query string from filters
        const queryParams = new URLSearchParams();
        
        if (filters.search) {
            queryParams.append('search', filters.search);
        }
        
        if (filters.location) {
            queryParams.append('location', filters.location);
        }
        
        if (filters.salary) {
            queryParams.append('salary', filters.salary);
        }
        
        if (filters.experience) {
            queryParams.append('experience', filters.experience);
        }
        
        if (filters.jobType) {
            queryParams.append('jobType', filters.jobType);
        }
        
        const queryString = queryParams.toString();
        const url = queryString ? `${API_URL}/jobs?${queryString}` : `${API_URL}/jobs`;
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch jobs');
        }
    } catch (error) {
        console.error('Error fetching jobs:', error);
        return [];
    }
}

// Get job by ID
async function getJobById(jobId) {
    try {
        const response = await fetch(`${API_URL}/jobs/${jobId}`);
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch job details');
        }
    } catch (error) {
        console.error('Error fetching job details:', error);
        return null;
    }
}

// Apply for a job
async function applyForJob(jobId, cvData) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để ứng tuyển công việc này');
            return false;
        }
        
        const response = await fetch(`${API_URL}/jobs/${jobId}/apply`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(cvData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Ứng tuyển thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Ứng tuyển thất bại');
        }
    } catch (error) {
        console.error('Error applying for job:', error);
        alert(error.message);
        return false;
    }
}

// Render job list
function renderJobs(jobs) {
    if (!jobList) return;
    
    // Clear job list
    jobList.innerHTML = '';
    
    if (jobs.length === 0) {
        jobList.innerHTML = '<div class="no-jobs">Không tìm thấy công việc phù hợp</div>';
        return;
    }
    
    // Create job items
    jobs.forEach(job => {
        const jobItem = document.createElement('div');
        jobItem.className = 'job-item';
        
        // Format salary
        const formattedSalary = job.salaryHidden 
            ? 'Thỏa thuận' 
            : `${job.salaryMin.toLocaleString()} - ${job.salaryMax.toLocaleString()} VNĐ`;
        
        // Calculate days left
        const deadline = new Date(job.deadline);
        const today = new Date();
        const daysLeft = Math.ceil((deadline - today) / (1000 * 60 * 60 * 24));
        
        // Create skills HTML
        const skillsHTML = job.skills.map(skill => `<span>${skill}</span>`).join('');
        
        jobItem.innerHTML = `
            <img src="${job.company.logo || 'images/company-placeholder.png'}" alt="${job.company.name}">
            <div class="job-info">
                <h3>${job.title}</h3>
                <p class="company">${job.company.name}</p>
                <p class="level">${job.level}</p>
                <div class="skills">
                    ${skillsHTML}
                </div>
                <div class="details">
                    <span><i class="fas fa-map-marker-alt"></i> ${job.location}</span>
                    <span><i class="fas fa-briefcase"></i> ${job.type}</span>
                    <span><i class="fas fa-calendar-alt"></i> ${daysLeft > 0 ? `Còn ${daysLeft} ngày` : 'Hết hạn'}</span>
                </div>
            </div>
            <div class="job-actions">
                <div class="salary">${formattedSalary}</div>
                <button class="apply-btn" data-job-id="${job._id}">Ứng tuyển</button>
                <i class="far fa-heart favorite" data-job-id="${job._id}"></i>
            </div>
            ${job.isHot ? '<div class="job-tag">Hot</div>' : ''}
        `;
        
        // Add event listener to apply button
        const applyBtn = jobItem.querySelector('.apply-btn');
        applyBtn.addEventListener('click', () => {
            showJobDetail(job._id);
        });
        
        // Add event listener to favorite button
        const favoriteBtn = jobItem.querySelector('.favorite');
        favoriteBtn.addEventListener('click', () => {
            toggleFavorite(job._id, favoriteBtn);
        });
        
        jobList.appendChild(jobItem);
    });
}

// Show job detail
async function showJobDetail(jobId) {
    try {
        // Get job details
        const job = await getJobById(jobId);
        
        if (!job) {
            alert('Không thể tải thông tin công việc');
            return;
        }
        
        // Format salary
        const formattedSalary = job.salaryHidden 
            ? 'Thỏa thuận' 
            : `${job.salaryMin.toLocaleString()} - ${job.salaryMax.toLocaleString()} VNĐ`;
        
        // Create skills HTML
        const skillsHTML = job.skills.map(skill => `<span>${skill}</span>`).join('');
        
        // Format deadline
        const deadline = new Date(job.deadline);
        const formattedDeadline = deadline.toLocaleDateString('vi-VN');
        
        // Update modal content
        jobDetailContent.innerHTML = `
            <div class="job-detail-header">
                <img src="${job.company.logo || 'images/company-placeholder.png'}" alt="${job.company.name}" class="job-detail-logo">
                <div class="job-detail-title">
                    <h2>${job.title}</h2>
                    <p>${job.company.name}</p>
                </div>
            </div>
            <div class="job-detail-body">
                <div class="job-detail-section">
                    <h3>Thông tin công việc</h3>
                    <div class="job-detail-info">
                        <div class="job-detail-info-item">
                            <span>Địa điểm</span>
                            <span>${job.location}</span>
                        </div>
                        <div class="job-detail-info-item">
                            <span>Loại hình</span>
                            <span>${job.type}</span>
                        </div>
                        <div class="job-detail-info-item">
                            <span>Cấp bậc</span>
                            <span>${job.level}</span>
                        </div>
                        <div class="job-detail-info-item">
                            <span>Kinh nghiệm</span>
                            <span>${job.experience}</span>
                        </div>
                        <div class="job-detail-info-item">
                            <span>Hạn nộp hồ sơ</span>
                            <span>${formattedDeadline}</span>
                        </div>
                    </div>
                </div>
                <div class="job-detail-section">
                    <h3>Mô tả công việc</h3>
                    <div class="job-detail-description">
                        ${job.description}
                    </div>
                </div>
                <div class="job-detail-section">
                    <h3>Yêu cầu</h3>
                    <div class="job-detail-description">
                        ${job.requirements}
                    </div>
                </div>
                <div class="job-detail-section">
                    <h3>Quyền lợi</h3>
                    <div class="job-detail-description">
                        ${job.benefits}
                    </div>
                </div>
                <div class="job-detail-section">
                    <h3>Kỹ năng</h3>
                    <div class="job-detail-skills">
                        ${skillsHTML}
                    </div>
                </div>
            </div>
            <div class="job-detail-footer">
                <div class="salary">${formattedSalary}</div>
                <div class="job-detail-actions">
                    <button class="job-detail-apply-btn" data-job-id="${job._id}">Ứng tuyển ngay</button>
                    <button class="job-detail-save-btn" data-job-id="${job._id}">Lưu công việc</button>
                </div>
            </div>
        `;
        
        // Add event listener to apply button
        const applyBtn = jobDetailContent.querySelector('.job-detail-apply-btn');
        applyBtn.addEventListener('click', () => {
            showApplyForm(job._id);
        });
        
        // Add event listener to save button
        const saveBtn = jobDetailContent.querySelector('.job-detail-save-btn');
        saveBtn.addEventListener('click', () => {
            toggleFavorite(job._id, null);
        });
        
        // Show modal
        jobDetailModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    } catch (error) {
        console.error('Error showing job detail:', error);
        alert('Không thể tải thông tin công việc');
    }
}

// Show apply form
function showApplyForm(jobId) {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Vui lòng đăng nhập để ứng tuyển công việc này');
        return;
    }
    
    // Hide job detail modal
    jobDetailModal.style.display = 'none';
    
    // Show apply form modal
    const applyModal = document.getElementById('apply-modal');
    if (applyModal) {
        // Set job ID in form
        const jobIdInput = document.getElementById('apply-job-id');
        if (jobIdInput) {
            jobIdInput.value = jobId;
        }
        
        applyModal.style.display = 'flex';
    }
}

// Toggle favorite job
async function toggleFavorite(jobId, favoriteBtn) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để lưu công việc này');
            return;
        }
        
        const response = await fetch(`${API_URL}/jobs/${jobId}/favorite`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Update UI if button exists
            if (favoriteBtn) {
                favoriteBtn.classList.toggle('far');
                favoriteBtn.classList.toggle('fas');
                favoriteBtn.classList.toggle('active');
            }
            
            alert(data.message);
        } else {
            throw new Error(data.message || 'Không thể lưu công việc');
        }
    } catch (error) {
        console.error('Error toggling favorite:', error);
        alert(error.message);
    }
}

// Initialize jobs page
async function initJobsPage() {
    try {
        // Get all jobs
        const jobs = await getJobs();
        
        // Render jobs
        renderJobs(jobs);
        
        // Initialize filters
        initFilters();
    } catch (error) {
        console.error('Error initializing jobs page:', error);
    }
}

// Initialize filters
function initFilters() {
    // Search form submission
    if (searchForm) {
        searchForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const filters = {
                search: searchInput.value,
                location: locationFilter.value,
                salary: salaryFilter.value,
                experience: experienceFilter.value,
                jobType: jobTypeFilter.value
            };
            
            const jobs = await getJobs(filters);
            renderJobs(jobs);
        });
    }
    
    // Filter change events
    const filterElements = [locationFilter, salaryFilter, experienceFilter, jobTypeFilter];
    filterElements.forEach(filter => {
        if (filter) {
            filter.addEventListener('change', async () => {
                const filters = {
                    search: searchInput.value,
                    location: locationFilter.value,
                    salary: salaryFilter.value,
                    experience: experienceFilter.value,
                    jobType: jobTypeFilter.value
                };
                
                const jobs = await getJobs(filters);
                renderJobs(jobs);
            });
        }
    });
    
    // Reset filters
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', async () => {
            // Reset form inputs
            if (searchInput) searchInput.value = '';
            if (locationFilter) locationFilter.value = '';
            if (salaryFilter) salaryFilter.value = '';
            if (experienceFilter) experienceFilter.value = '';
            if (jobTypeFilter) jobTypeFilter.value = '';
            
            // Get all jobs
            const jobs = await getJobs();
            renderJobs(jobs);
        });
    }
}

// Apply job form submission
if (applyJobForm) {
    applyJobForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const jobId = document.getElementById('apply-job-id').value;
        const cvId = document.getElementById('apply-cv').value;
        const coverLetter = document.getElementById('apply-cover-letter').value;
        
        const cvData = {
            cvId,
            coverLetter
        };
        
        const success = await applyForJob(jobId, cvData);
        
        if (success) {
            // Close modal
            const applyModal = document.getElementById('apply-modal');
            if (applyModal) {
                applyModal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Check if we're on the jobs page
    if (jobList) {
        initJobsPage();
    }
    
    // Close job detail modal
    const closeJobDetail = document.querySelector('#job-detail-modal .close');
    if (closeJobDetail) {
        closeJobDetail.addEventListener('click', () => {
            jobDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === jobDetailModal) {
            jobDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
});
