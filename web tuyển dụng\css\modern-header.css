/* Modern Header & Footer Styles */
:root {
    /* Primary Colors */
    --primary-green: #00a550;
    --primary-dark-green: #008040;
    --primary-light-green: #e6f7ee;

    /* Neutral Colors */
    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #777777;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-lighter: #f2f2f2;
    --border-color: #e0e0e0;

    /* Accent Colors */
    --hot-color: #ff4d4d;
    --top-color: #00a550;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-full: 9999px;
}

/* Header Styles */
body {
    font-family: 'Inter', sans-serif;
    margin: 0;
    padding: 0;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header {
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo img {
    height: 40px;
    width: auto;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-text span {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-green);
}

.nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
}

.nav-links a {
    color: var(--text-medium);
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-green);
}

.nav-buttons {
    display: flex;
    gap: 0.75rem;
}

.nav-buttons button {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn {
    background-color: var(--bg-white);
    color: var(--primary-green);
    border: 1px solid var(--primary-green);
}

.login-btn:hover {
    background-color: var(--primary-light-green);
}

.register-btn {
    background-color: var(--primary-green);
    color: white;
    border: 1px solid var(--primary-green);
}

.register-btn:hover {
    background-color: var(--primary-dark-green);
}

/* Footer Styles */
.footer {
    background-color: var(--text-dark);
    color: white;
    padding: 3rem 0;
    margin-top: 3rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.25rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-green);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--bg-white);
    padding: 2rem;
    border-radius: 10px;
    width: 100%;
    max-width: 400px;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--text-dark);
}

.modal-content h2 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    border-color: var(--primary-green);
    outline: none;
}

.modal-btn {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.modal-btn:hover {
    background-color: var(--primary-dark-green);
}

.modal-link {
    text-align: center;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-medium);
}

.modal-link a {
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 600;
}

.modal-link a:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        flex-direction: column;
        width: 100%;
    }

    .nav-links {
        width: 100%;
        justify-content: space-between;
    }

    .nav-buttons {
        width: 100%;
    }

    .nav-buttons button {
        flex: 1;
    }

    .footer-content {
        flex-direction: column;
    }
}
