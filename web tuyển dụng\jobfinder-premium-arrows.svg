<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60">
  <defs>
    <linearGradient id="arrowGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#047857;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- First Arrow -->
  <g transform="translate(0, 10)">
    <path d="M5,10 L35,10 L35,0 L55,15 L35,30 L35,20 L5,20 Z" fill="url(#arrowGradient1)" filter="url(#shadow)"/>
    
    <!-- Dots for first arrow -->
    <circle cx="65" cy="5" r="2.5" fill="#059669"/>
    <circle cx="75" cy="5" r="2.5" fill="#059669"/>
    <circle cx="85" cy="5" r="2.5" fill="#059669"/>
    
    <rect x="95" y="3" width="30" height="4" rx="2" fill="#059669"/>
    
    <circle cx="135" cy="5" r="2.5" fill="#059669"/>
    <circle cx="145" cy="5" r="2.5" fill="#059669"/>
    <circle cx="155" cy="5" r="2.5" fill="#059669"/>
  </g>
  
  <!-- Second Arrow -->
  <g transform="translate(0, 40)">
    <path d="M5,10 L35,10 L35,0 L55,15 L35,30 L35,20 L5,20 Z" fill="url(#arrowGradient2)" filter="url(#shadow)"/>
    
    <!-- Dots for second arrow -->
    <circle cx="65" cy="5" r="2.5" fill="#059669"/>
    <circle cx="75" cy="5" r="2.5" fill="#059669"/>
    <circle cx="85" cy="5" r="2.5" fill="#059669"/>
    
    <rect x="95" y="3" width="20" height="4" rx="2" fill="#059669"/>
    
    <circle cx="125" cy="5" r="2.5" fill="#059669"/>
    <circle cx="135" cy="5" r="2.5" fill="#059669"/>
    <circle cx="145" cy="5" r="2.5" fill="#059669"/>
  </g>
</svg>
