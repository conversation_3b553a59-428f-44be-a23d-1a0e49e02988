const { pool, sql } = require('../config/db');

class CVTemplate {
    // <PERSON><PERSON><PERSON> s<PERSON>ch mẫu CV
    static async getAll(categoryId = null, isPremium = null) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('category_id', sql.Int, categoryId);
            request.input('is_premium', sql.Bit, isPremium !== null ? (isPremium ? 1 : 0) : null);
            
            const result = await request.execute('sp_GetCVTemplates');
            
            return result.recordset ? result.recordset.map(template => this.formatTemplate(template)) : [];
        } catch (error) {
            console.error('CVTemplate getAll error:', error);
            throw error;
        }
    }
    
    // L<PERSON>y thông tin chi tiết mẫu CV
    static async getById(templateId) {
        try {
            await pool.connect();
            
            const request = pool.request();
            request.input('template_id', sql.Int, templateId);
            
            const result = await request.execute('sp_GetCVTemplateDetail');
            
            if (result.recordsets && result.recordsets.length > 0) {
                const template = result.recordsets[0][0];
                const fields = result.recordsets[1] || [];
                
                if (template) {
                    const formattedTemplate = this.formatTemplate(template);
                    formattedTemplate.fields = fields.map(field => ({
                        id: field.id,
                        templateId: field.template_id,
                        fieldName: field.field_name,
                        fieldLabel: field.field_label,
                        fieldType: field.field_type,
                        fieldOrder: field.field_order,
                        isRequired: field.is_required === 1,
                        defaultValue: field.default_value,
                        createdAt: field.created_at,
                        updatedAt: field.updated_at
                    }));
                    
                    return formattedTemplate;
                }
            }
            
            return null;
        } catch (error) {
            console.error('CVTemplate getById error:', error);
            throw error;
        }
    }
    
    // Lấy danh sách danh mục mẫu CV
    static async getCategories() {
        try {
            await pool.connect();
            
            const request = pool.request();
            const result = await request.execute('sp_GetCVTemplateCategories');
            
            return result.recordset ? result.recordset.map(category => ({
                id: category.id,
                name: category.name,
                description: category.description,
                createdAt: category.created_at,
                updatedAt: category.updated_at
            })) : [];
        } catch (error) {
            console.error('CVTemplate getCategories error:', error);
            throw error;
        }
    }
    
    // Định dạng dữ liệu mẫu CV
    static formatTemplate(template) {
        return {
            id: template.id,
            name: template.name,
            description: template.description,
            previewImage: template.preview_image,
            htmlTemplate: template.html_template,
            cssTemplate: template.css_template,
            category: template.category,
            categoryName: template.category_name,
            isPremium: template.is_premium === 1,
            isActive: template.is_active === 1,
            createdAt: template.created_at,
            updatedAt: template.updated_at
        };
    }
}

module.exports = CVTemplate;
