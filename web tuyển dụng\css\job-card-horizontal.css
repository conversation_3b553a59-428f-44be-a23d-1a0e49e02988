/* Job Card Horizontal Style */
.job-card-horizontal {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    position: relative;
    transition: all 0.2s ease;
}

.job-card-horizontal:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.job-card-horizontal::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #00a550;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.job-card-logo {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    object-fit: contain;
    border: 1px solid #f0f0f0;
    padding: 5px;
    background-color: #fff;
    margin-right: 15px;
}

.job-card-content {
    flex: 1;
    min-width: 0;
}

.job-card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
}

.job-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
    display: flex;
    align-items: center;
}

.job-card-title .verified {
    color: #00a550;
    margin-left: 5px;
    font-size: 14px;
}

.job-card-badge {
    display: inline-block;
    padding: 3px 8px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px;
    color: white;
    background-color: #00a550;
    margin-left: 10px;
}

.job-card-badge.hot {
    background-color: #ff4d4d;
}

.job-card-salary {
    font-size: 15px;
    font-weight: 600;
    color: #00a550;
    white-space: nowrap;
}

.job-card-company {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
}

.job-card-update {
    font-size: 12px;
    color: #777;
    margin-bottom: 8px;
}

.job-card-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 8px;
}

.job-card-detail {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #00a550;
}

.job-card-detail i {
    margin-right: 5px;
    color: #00a550;
    font-size: 14px;
}

.job-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.job-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.job-card-tag {
    font-size: 12px;
    color: #555;
    background-color: #f5f5f5;
    padding: 3px 8px;
    border-radius: 4px;
    white-space: nowrap;
}

.job-card-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.job-card-apply {
    background-color: #00a550;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.job-card-apply:hover {
    background-color: #008040;
}

.job-card-save {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ccc;
    border: 1px solid #e0e0e0;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-card-save:hover {
    color: #ff4d4d;
    border-color: #ff4d4d;
}

/* Top badge */
.top-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #00a550;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
}

/* Responsive */
@media (max-width: 768px) {
    .job-card-horizontal {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-card-logo {
        margin-bottom: 10px;
    }

    .job-card-header {
        flex-direction: column;
    }

    .job-card-salary {
        margin-top: 5px;
    }

    .job-card-footer {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-card-actions {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }
}
