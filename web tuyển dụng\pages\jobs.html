<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tìm kiếm việc làm - JobFinder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/header-footer.css">
    <link rel="stylesheet" href="css/jobs.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Jobs Page -->
    <div class="container py-40">
        <h2 class="text-center mb-20">Tìm kiếm việc làm</h2>

        <div class="search-form mb-20">
            <div class="search-bar">
                <input type="text" id="search-input" placeholder="<PERSON><PERSON><PERSON><PERSON> từ khóa, vị trí, công ty...">
                <button type="submit" class="search-btn" id="search-btn"><i class="fas fa-search"></i> Tìm kiếm</button>
            </div>

            <div class="filters">
                <select id="location-filter">
                    <option value="">Địa điểm</option>
                    <option value="Hà Nội">Hà Nội</option>
                    <option value="Hồ Chí Minh">Hồ Chí Minh</option>
                    <option value="Đà Nẵng">Đà Nẵng</option>
                    <option value="Hải Phòng">Hải Phòng</option>
                    <option value="Cần Thơ">Cần Thơ</option>
                </select>

                <select id="salary-filter">
                    <option value="">Mức lương</option>
                    <option value="0-5000000">Dưới 5 triệu</option>
                    <option value="5000000-10000000">5 - 10 triệu</option>
                    <option value="10000000-20000000">10 - 20 triệu</option>
                    <option value="20000000-30000000">20 - 30 triệu</option>
                    <option value="30000000">Trên 30 triệu</option>
                </select>

                <select id="experience-filter">
                    <option value="">Kinh nghiệm</option>
                    <option value="0">Chưa có kinh nghiệm</option>
                    <option value="1">1 năm</option>
                    <option value="2">2 năm</option>
                    <option value="3-5">3 - 5 năm</option>
                    <option value="5">Trên 5 năm</option>
                </select>

                <select id="job-type-filter">
                    <option value="">Loại hình</option>
                    <option value="Toàn thời gian">Toàn thời gian</option>
                    <option value="Bán thời gian">Bán thời gian</option>
                    <option value="Thực tập">Thực tập</option>
                    <option value="Freelance">Freelance</option>
                    <option value="Remote">Remote</option>
                </select>

                <button id="reset-filter" class="reset-filter-btn">Đặt lại</button>
            </div>
        </div>

        <div class="job-results">
            <div class="job-filters">
                <h3>Bộ lọc nâng cao</h3>

                <div class="filter-group">
                    <label>Ngành nghề</label>
                    <div class="filter-options">
                        <div class="filter-option">
                            <input type="checkbox" id="category-it" value="IT">
                            <span>Công nghệ thông tin</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="category-finance" value="Finance">
                            <span>Tài chính - Ngân hàng</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="category-marketing" value="Marketing">
                            <span>Marketing</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="category-sales" value="Sales">
                            <span>Bán hàng</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="category-engineering" value="Engineering">
                            <span>Kỹ thuật</span>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label>Cấp bậc</label>
                    <div class="filter-options">
                        <div class="filter-option">
                            <input type="checkbox" id="level-intern" value="Intern">
                            <span>Thực tập sinh</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="level-fresher" value="Fresher">
                            <span>Mới tốt nghiệp</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="level-junior" value="Junior">
                            <span>Nhân viên</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="level-senior" value="Senior">
                            <span>Trưởng nhóm / Quản lý</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="level-manager" value="Manager">
                            <span>Giám đốc</span>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <label>Kỹ năng</label>
                    <div class="filter-options">
                        <div class="filter-option">
                            <input type="checkbox" id="skill-javascript" value="JavaScript">
                            <span>JavaScript</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="skill-java" value="Java">
                            <span>Java</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="skill-python" value="Python">
                            <span>Python</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="skill-react" value="React">
                            <span>React</span>
                        </div>
                        <div class="filter-option">
                            <input type="checkbox" id="skill-nodejs" value="Node.js">
                            <span>Node.js</span>
                        </div>
                    </div>
                </div>

                <div class="filter-buttons">
                    <button class="apply-filter-btn">Áp dụng</button>
                    <button class="reset-filter-btn">Đặt lại</button>
                </div>
            </div>

            <div class="job-list" id="job-list">
                <!-- Jobs will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Modals -->
    <div id="modals-container"></div>

    <!-- Job Detail Modal -->
    <div id="job-detail-modal" class="modal job-detail-modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="job-detail-content"></div>
        </div>
    </div>

    <!-- Apply Modal -->
    <div id="apply-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Ứng tuyển công việc</h2>
            <form id="apply-job-form">
                <input type="hidden" id="apply-job-id">
                <div class="form-group">
                    <label for="apply-cv">Chọn CV</label>
                    <select id="apply-cv" required>
                        <option value="">-- Chọn CV --</option>
                        <!-- CV options will be loaded dynamically -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="apply-cover-letter">Thư giới thiệu</label>
                    <textarea id="apply-cover-letter" required></textarea>
                </div>
                <button type="submit" class="modal-btn">Ứng tuyển</button>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/jobs.js"></script>
</body>
</html>
