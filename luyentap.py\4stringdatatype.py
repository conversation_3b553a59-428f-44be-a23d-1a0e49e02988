#Chuỗi trần: will auto repair và in ra nội dung in str 
print (r"Hau, \neu mot ngay")
#Toán tử cộng:
str1 = "HowKteam"
str2 = "Free education"
str3 = str1 +'\n'+ str2
print (str3)
# Toán tử nhân:
str4 = "HowKteam \n"# thêm dấu \n sau chuỗi nếu muốn in ra phép nhân chuỗi xuống dòng 
str5 = str4*5
print (str5)
#toán tử in:chuỗi A có kí hiệu exist in B thì true and opposite
str6 = 'Hello world'
str7 = 'H'
str8 = str7 in str6
print (str8)
# indexing và cắt chuỗi 
strA = "HowKteam"
strB = strA [2:None ]
print (strB)
#bước nhảy( bước nhảy dương thì cắt trái qua và âm then opposite)
# không lấy bước nh<PERSON>y khi kết thúc ở cuối chuỗi cả + and -
strC =strA[None:5:-1]
print (strC)
#Ép kiểu dữ liệu 
strA = int('69') + 5 # chuỗi thành số 
strB = float('6.9') + 5#chuỗi thành số 
strC = int(6.9) + 5 # lấy phần nguyên (và phần thực thì opposite)
strD = str(69) + "5987"# số thành chuỗi
print (strA)
print (strB)
print (strC)
print (strD)


