/* Profile Page Styles */
.profile-page {
    padding: 40px 0;
    background-color: #f5f7fa;
}

.profile-page h2 {
    text-align: center;
    font-size: 28px; /* Adjusted size */
    font-weight: 600;
    margin-bottom: 40px; /* Increased margin */
    color: #2c5282; /* Darker blue */
}

.profile-actions {
    display: grid; /* Changed to grid */
    grid-template-columns: repeat(2, 1fr); /* Two equal columns */
    gap: 20px;
    margin-bottom: 40px; /* Increased margin */
    max-width: 800px; /* Limit width */
    margin-left: auto;
    margin-right: auto;
}

.profile-actions .action-btn { /* Updated selector */
    padding: 15px 20px; /* Adjusted padding */
    background-color: #047857; /* Green color */
    color: #fff;
    border: none;
    border-radius: 8px; /* Slightly less rounded */
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.profile-actions .action-btn:hover {
    background-color: #065f46; /* Darker green on hover */
}

.profile-search-filters {
    background-color: #f8f9fa; /* Light background */
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 40px;
    max-width: 800px; /* Limit width */
    margin-left: auto;
    margin-right: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.profile-search {
    display: flex;
    gap: 15px;
    margin-bottom: 20px; /* Spacing below search bar */
}

.profile-search input {
    flex-grow: 1; /* Input takes available space */
    padding: 12px 20px;
    border: 1px solid #e2e8f0; /* Light border */
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    background-color: #fff; /* White background */
}

.profile-search input::placeholder {
    color: #a0aec0;
}

.profile-search .search-btn {
    background-color: #047857; /* Green color */
    color: #fff;
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.profile-search .search-btn i {
    font-size: 14px;
}

.profile-search .search-btn:hover {
    background-color: #065f46; /* Darker green */
}

.profile-filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    /* Removed margin-bottom as it's handled by parent */
}

.profile-filters select {
    padding: 10px 30px 10px 15px; /* Adjusted padding for arrow */
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
    color: #4a5568;
    cursor: pointer;
    outline: none;
    appearance: none; /* Remove default arrow */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e"); /* Custom arrow */
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    min-width: 150px; /* Ensure minimum width */
}

.profile-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.profile-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.profile-card-header {
    background-color: #4A90E2;
    color: #fff;
    padding: 20px;
    text-align: center;
}

.profile-card-header h3 {
    font-size: 20px;
    margin-bottom: 5px;
    color: #fff;
}

.profile-card-header p {
    font-size: 14px;
    opacity: 0.8;
}

.profile-card-body {
    padding: 20px;
}

.profile-card-info {
    margin-bottom: 20px;
}

.profile-card-info p {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.profile-card-info i {
    width: 20px;
    margin-right: 10px;
    color: #4A90E2;
}

.profile-card-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.profile-card-skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.profile-card-actions {
    display: flex;
    gap: 10px;
}

.profile-card-actions button {
    flex: 1;
    padding: 10px 0;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-card-edit {
    background-color: #f0f7ff;
    color: #4A90E2;
    border: 1px solid #4A90E2;
}

.profile-card-edit:hover {
    background-color: #e0f0ff;
}

.profile-card-view {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.profile-card-view:hover {
    background-color: #3A78C2;
}

/* CV Section Styles */
.cv-list {
    display: grid; /* Change to grid */
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); /* Adjust minmax as needed */
    gap: 20px; /* Add gap */
    margin-top: 40px;
}

.cv-list h3 { /* Keep section title styling if needed, or remove if title is outside list */
    /* font-size: 24px; */ /* Assuming title is outside .cv-list now */
    /* color: #333; */
    /* margin-bottom: 20px; */
    /* text-align: center; */
    grid-column: 1 / -1; /* Make title span full width if kept inside grid */
    margin-bottom: 10px; /* Adjust spacing */
}

.cv-card {
    background-color: #fff;
    border-radius: 8px; /* Match other elements */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Match search filter box */
    padding: 20px;
    display: flex; /* Use flexbox */
    align-items: center; /* Vertically align items */
    gap: 20px; /* Gap between elements */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    /* margin-bottom removed as grid handles gap */
}

.cv-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.cv-preview {
    width: 60px; /* Smaller preview */
    height: 80px; /* Aspect ratio for CV */
    background-color: #e2e8f0; /* Lighter gray */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8; /* Gray text */
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    flex-shrink: 0; /* Prevent shrinking */
    text-align: center;
}

.cv-info {
    flex-grow: 1; /* Allow info to take up space */
}

.cv-info h4 {
    font-size: 16px; /* Adjust size */
    font-weight: 600;
    color: #333;
    margin-bottom: 5px; /* Reduce margin */
    padding-bottom: 0; /* Remove padding */
    border-bottom: none; /* Remove border */
}

.cv-info p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

.cv-actions { /* Renamed selector */
    display: flex;
    gap: 10px;
    flex-shrink: 0; /* Prevent shrinking */
}

.cv-actions button {
    padding: 8px 15px;
    border-radius: 6px; /* Match other buttons */
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent; /* Base border */
}

.btn-view-cv { /* New class */
    background-color: #047857; /* Green */
    color: #fff;
    border-color: #047857;
}
.btn-view-cv:hover {
    background-color: #065f46; /* Darker green */
    border-color: #065f46;
}

.btn-edit-cv { /* New class */
    background-color: #e0f2fe; /* Light blue */
    color: #0ea5e9; /* Sky blue */
    border-color: #bae6fd; /* Lighter blue border */
}
.btn-edit-cv:hover {
    background-color: #bae6fd; /* Lighter blue */
}

.btn-delete-cv { /* New class */
    background-color: #fee2e2; /* Light red */
    color: #ef4444; /* Red */
    border-color: #fecaca; /* Lighter red border */
}
.btn-delete-cv:hover {
    background-color: #fecaca; /* Lighter red */
}

/* Remove or comment out old CV styles if they conflict */
/*
.cv-card-section {
    margin-bottom: 15px;
}

.cv-card-section h5 {
    font-size: 16px;
    color: #4A90E2;
    margin-bottom: 10px;
}

.cv-card-section p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

.cv-card-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.cv-card-skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.cv-card-actions button { ... }
.cv-card-edit { ... }
.cv-card-edit:hover { ... }
.cv-card-download { ... }
.cv-card-download:hover { ... }
.cv-card-delete { ... }
.cv-card-delete:hover { ... }
*/

/* Profile Modal */
.profile-modal .modal-content {
    max-width: 600px;
}

.profile-form-group {
    margin-bottom: 20px;
}

.profile-form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.profile-form-group input,
.profile-form-group select,
.profile-form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.profile-form-group input:focus,
.profile-form-group select:focus,
.profile-form-group textarea:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.profile-form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.profile-form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.profile-form-actions button {
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-form-save {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.profile-form-save:hover {
    background-color: #3A78C2;
}

.profile-form-cancel {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.profile-form-cancel:hover {
    background-color: #eee;
}

/* CV Detail Modal */
.cv-detail-modal .modal-content {
    max-width: 800px;
}

.cv-detail-header {
    text-align: center;
    margin-bottom: 30px;
}

.cv-detail-header h2 {
    font-size: 28px;
    color: #333;
    margin-bottom: 10px;
}

.cv-detail-header p {
    font-size: 16px;
    color: #666;
}

.cv-detail-section {
    margin-bottom: 30px;
}

.cv-detail-section h3 {
    font-size: 20px;
    color: #4A90E2;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.cv-detail-section p {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
}

.cv-detail-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.cv-detail-skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
}

.cv-detail-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.cv-detail-actions button {
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cv-detail-download {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.cv-detail-download:hover {
    background-color: #3A78C2;
    transform: translateY(-2px);
}

.cv-detail-close {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.cv-detail-close:hover {
    background-color: #eee;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .profile-actions {
        flex-direction: column;
    }
    
    .profile-actions .modal-btn {
        width: 100%;
    }
    
    .profile-search {
        flex-direction: column;
    }
    
    .profile-search input {
        width: 100%;
    }
    
    .profile-search .search-btn {
        width: 100%;
    }
    
    .profile-filters {
        flex-wrap: wrap;
    }
    
    .profile-filters select {
        width: 100%;
    }
    
    .profile-list {
        grid-template-columns: 1fr;
    }
    
    .cv-card-actions {
        flex-direction: column;
    }
    
    .cv-card-actions button {
        width: 100%;
    }
    
    .profile-form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .profile-form-actions button {
        width: 100%;
    }
    
    .cv-detail-actions {
        flex-direction: column;
    }
    
    .cv-detail-actions button {
        width: 100%;
    }
}

.cv-templates {
    padding: 50px 0;
    background: #ffffff;
    border-bottom: 1px solid #eaeaea;
}

.cv-templates-container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 20px;
}

.cv-templates-header {
    text-align: center;
    margin-bottom: 50px;
}

.cv-templates-header h2 {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.cv-templates-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #4A90E2;
}

.cv-templates-header p {
    font-size: 16px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.cv-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.cv-template-card {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cv-template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.cv-template-preview {
    position: relative;
    padding-top: 141%;
    background: #f8f9fa;
    border-bottom: 1px solid #eaeaea;
}

.cv-template-preview img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cv-template-info {
    padding: 20px;
}

.cv-template-info h3 {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.cv-template-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.cv-template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.cv-template-tag {
    padding: 4px 12px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 13px;
    color: #4A90E2;
    font-weight: 500;
}

.cv-template-actions {
    display: flex;
    gap: 10px;
}

.cv-template-btn {
    flex: 1;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cv-template-btn.preview {
    background: #f5f7fa;
    color: #4A90E2;
    border: 1px solid #4A90E2;
}

.cv-template-btn.preview:hover {
    background: #e8f0fe;
}

.cv-template-btn.use {
    background: #4A90E2;
    color: #fff;
}

.cv-template-btn.use:hover {
    background: #357abd;
}

/* Responsive styles */
@media (max-width: 768px) {
    .cv-templates {
        padding: 40px 0;
    }

    .cv-templates-header h2 {
        font-size: 28px;
    }

    .cv-templates-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 0 15px;
    }
}

/* Loading state */
.cv-template-card.loading {
    position: relative;
    overflow: hidden;
}

.cv-template-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0) 0%, 
        rgba(255,255,255,0.4) 50%, 
        rgba(255,255,255,0) 100%);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(100%);
    }
}
