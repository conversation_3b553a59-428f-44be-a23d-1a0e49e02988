{"version": 3, "file": "PerformanceEvent.js", "sources": ["../../../src/telemetry/performance/PerformanceEvent.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Enumeration of operations that are instrumented by have their performance measured by the PerformanceClient.\r\n *\r\n * @export\r\n * @enum {number}\r\n */\r\nexport enum PerformanceEvents {\r\n\r\n    /**\r\n     * acquireTokenByCode API (msal-browser and msal-node).\r\n     * Used to acquire tokens by trading an authorization code against the token endpoint.\r\n     */\r\n    AcquireTokenByCode = \"acquireTokenByCode\",\r\n\r\n    /**\r\n     * acquireTokenByRefreshToken API (msal-browser and msal-node).\r\n     * Used to renew an access token using a refresh token against the token endpoint.\r\n     */\r\n    AcquireTokenByRefreshToken = \"acquireTokenByRefreshToken\",\r\n\r\n    /**\r\n     * acquireTokenSilent API (msal-browser and msal-node).\r\n     * Used to silently acquire a new access token (from the cache or the network).\r\n     */\r\n    AcquireTokenSilent = \"acquireTokenSilent\",\r\n\r\n    /**\r\n     * acquireTokenSilentAsync (msal-browser).\r\n     * Internal API for acquireTokenSilent.\r\n     */\r\n    AcquireTokenSilentAsync = \"acquireTokenSilentAsync\",\r\n\r\n    /**\r\n     * acquireTokenPopup (msal-browser).\r\n     * Used to acquire a new access token interactively through pop ups\r\n     */\r\n    AcquireTokenPopup = \"acquireTokenPopup\",\r\n\r\n    /**\r\n     * getPublicKeyThumbprint API in CryptoOpts class (msal-browser).\r\n     * Used to generate a public/private keypair and generate a public key thumbprint for pop requests.\r\n     */\r\n    CryptoOptsGetPublicKeyThumbprint = \"cryptoOptsGetPublicKeyThumbprint\",\r\n\r\n    /**\r\n     * signJwt API in CryptoOpts class (msal-browser).\r\n     * Used to signed a pop token.\r\n     */\r\n    CryptoOptsSignJwt = \"cryptoOptsSignJwt\",\r\n\r\n    /**\r\n     * acquireToken API in the SilentCacheClient class (msal-browser).\r\n     * Used to read access tokens from the cache.\r\n     */\r\n    SilentCacheClientAcquireToken = \"silentCacheClientAcquireToken\",\r\n\r\n    /**\r\n     * acquireToken API in the SilentIframeClient class (msal-browser).\r\n     * Used to acquire a new set of tokens from the authorize endpoint in a hidden iframe.\r\n     */\r\n    SilentIframeClientAcquireToken = \"silentIframeClientAcquireToken\",\r\n\r\n    /**\r\n     * acquireToken API in SilentRereshClient (msal-browser).\r\n     * Used to acquire a new set of tokens from the token endpoint using a refresh token.\r\n     */\r\n    SilentRefreshClientAcquireToken = \"silentRefreshClientAcquireToken\",\r\n\r\n    /**\r\n     * ssoSilent API (msal-browser).\r\n     * Used to silently acquire an authorization code and set of tokens using a hidden iframe.\r\n     */\r\n    SsoSilent = \"ssoSilent\",\r\n\r\n    /**\r\n     * getDiscoveredAuthority API in StandardInteractionClient class (msal-browser).\r\n     * Used to load authority metadata for a request.\r\n     */\r\n    StandardInteractionClientGetDiscoveredAuthority = \"standardInteractionClientGetDiscoveredAuthority\",\r\n\r\n    /**\r\n     * acquireToken APIs in msal-browser.\r\n     * Used to make an /authorize endpoint call with native brokering enabled.\r\n     */\r\n    FetchAccountIdWithNativeBroker = \"fetchAccountIdWithNativeBroker\",\r\n\r\n    /**\r\n     * acquireToken API in NativeInteractionClient class (msal-browser).\r\n     * Used to acquire a token from Native component when native brokering is enabled.\r\n     */\r\n    NativeInteractionClientAcquireToken = \"nativeInteractionClientAcquireToken\",\r\n    /**\r\n     * Time spent creating default headers for requests to token endpoint\r\n     */\r\n    BaseClientCreateTokenRequestHeaders = \"baseClientCreateTokenRequestHeaders\",\r\n    /**\r\n     * Used to measure the time taken for completing embedded-broker handshake (PW-Broker).\r\n     */\r\n    BrokerHandhshake = \"brokerHandshake\",\r\n    /**\r\n     * acquireTokenByRefreshToken API in BrokerClientApplication (PW-Broker) .\r\n     */\r\n    AcquireTokenByRefreshTokenInBroker = \"acquireTokenByRefreshTokenInBroker\",\r\n    /**\r\n     * Time taken for token acquisition by broker\r\n     */\r\n    AcquireTokenByBroker = \"acquireTokenByBroker\",\r\n\r\n    /**\r\n     * Time spent on the network for refresh token acquisition\r\n     */\r\n    RefreshTokenClientExecuteTokenRequest = \"refreshTokenClientExecuteTokenRequest\",\r\n\r\n    /**\r\n     * Time taken for acquiring refresh token , records RT size\r\n     */\r\n    RefreshTokenClientAcquireToken = \"refreshTokenClientAcquireToken\",\r\n\r\n    /**\r\n     * Time taken for acquiring cached refresh token\r\n     */\r\n    RefreshTokenClientAcquireTokenWithCachedRefreshToken = \"refreshTokenClientAcquireTokenWithCachedRefreshToken\",\r\n\r\n    /**\r\n     * acquireTokenByRefreshToken API in RefreshTokenClient (msal-common).\r\n     */\r\n    RefreshTokenClientAcquireTokenByRefreshToken = \"refreshTokenClientAcquireTokenByRefreshToken\",\r\n\r\n    /**\r\n     * Helper function to create token request body in RefreshTokenClient (msal-common).\r\n     */\r\n    RefreshTokenClientCreateTokenRequestBody = \"refreshTokenClientCreateTokenRequestBody\",\r\n\r\n    /**\r\n     * acquireTokenFromCache (msal-browser).\r\n     * Internal API for acquiring token from cache\r\n     */\r\n    AcquireTokenFromCache = \"acquireTokenFromCache\",\r\n\r\n    /**\r\n     * acquireTokenBySilentIframe (msal-browser).\r\n     * Internal API for acquiring token by silent Iframe\r\n     */\r\n    AcquireTokenBySilentIframe = \"acquireTokenBySilentIframe\",\r\n\r\n    /**\r\n     * Internal API for initializing base request in BaseInteractionClient (msal-browser)\r\n     */\r\n    InitializeBaseRequest = \"initializeBaseRequest\",\r\n\r\n    /**\r\n     * Internal API for initializing silent request in SilentCacheClient (msal-browser)\r\n     */\r\n    InitializeSilentRequest = \"initializeSilentRequest\",\r\n\r\n    InitializeClientApplication = \"initializeClientApplication\",\r\n\r\n    /**\r\n     * Helper function in SilentIframeClient class (msal-browser).\r\n     */\r\n    SilentIframeClientTokenHelper = \"silentIframeClientTokenHelper\",\r\n\r\n    /**\r\n     * SilentHandler\r\n     */\r\n    SilentHandlerInitiateAuthRequest = \"silentHandlerInitiateAuthRequest\",\r\n    SilentHandlerMonitorIframeForHash = \"silentHandlerMonitorIframeForHash\",\r\n    SilentHandlerLoadFrame = \"silentHandlerLoadFrame\",\r\n\r\n    /**\r\n     * Helper functions in StandardInteractionClient class (msal-browser)\r\n     */\r\n    StandardInteractionClientCreateAuthCodeClient = \"standardInteractionClientCreateAuthCodeClient\",\r\n    StandardInteractionClientGetClientConfiguration = \"standardInteractionClientGetClientConfiguration\",\r\n    StandardInteractionClientInitializeAuthorizationRequest = \"standardInteractionClientInitializeAuthorizationRequest\",\r\n    StandardInteractionClientInitializeAuthorizationCodeRequest = \"standardInteractionClientInitializeAuthorizationCodeRequest\",\r\n\r\n    /**\r\n     * getAuthCodeUrl API (msal-browser and msal-node).\r\n     */\r\n    GetAuthCodeUrl = \"getAuthCodeUrl\",\r\n\r\n    /**\r\n     * Functions from InteractionHandler (msal-browser)\r\n     */\r\n    HandleCodeResponseFromServer = \"handleCodeResponseFromServer\",\r\n    HandleCodeResponseFromHash = \"handleCodeResponseFromHash\",\r\n    UpdateTokenEndpointAuthority = \"updateTokenEndpointAuthority\",\r\n\r\n    /**\r\n     * APIs in Authorization Code Client (msal-common)\r\n     */\r\n    AuthClientAcquireToken = \"authClientAcquireToken\",\r\n    AuthClientExecuteTokenRequest = \"authClientExecuteTokenRequest\",\r\n    AuthClientCreateTokenRequestBody = \"authClientCreateTokenRequestBody\",\r\n    AuthClientCreateQueryString = \"authClientCreateQueryString\",\r\n\r\n    /**\r\n     * Generate functions in PopTokenGenerator (msal-common)\r\n     */\r\n    PopTokenGenerateCnf = \"popTokenGenerateCnf\",\r\n    PopTokenGenerateKid = \"popTokenGenerateKid\",\r\n\r\n    /**\r\n     * handleServerTokenResponse API in ResponseHandler (msal-common)\r\n     */\r\n    HandleServerTokenResponse = \"handleServerTokenResponse\",\r\n\r\n    /**\r\n     * Authority functions\r\n     */\r\n    AuthorityFactoryCreateDiscoveredInstance = \"authorityFactoryCreateDiscoveredInstance\",\r\n    AuthorityResolveEndpointsAsync = \"authorityResolveEndpointsAsync\",\r\n    AuthorityGetCloudDiscoveryMetadataFromNetwork = \"authorityGetCloudDiscoveryMetadataFromNetwork\",\r\n    AuthorityUpdateCloudDiscoveryMetadata = \"authorityUpdateCloudDiscoveryMetadata\",\r\n    AuthorityGetEndpointMetadataFromNetwork = \"authorityGetEndpointMetadataFromNetwork\",\r\n    AuthorityUpdateEndpointMetadata = \"authorityUpdateEndpointMetadata\",\r\n    AuthorityUpdateMetadataWithRegionalInformation = \"authorityUpdateMetadataWithRegionalInformation\",\r\n\r\n    /**\r\n     * Region Discovery functions\r\n     */\r\n    RegionDiscoveryDetectRegion = \"regionDiscoveryDetectRegion\",\r\n    RegionDiscoveryGetRegionFromIMDS = \"regionDiscoveryGetRegionFromIMDS\",\r\n    RegionDiscoveryGetCurrentVersion = \"regionDiscoveryGetCurrentVersion\",\r\n\r\n    AcquireTokenByCodeAsync = \"acquireTokenByCodeAsync\",\r\n\r\n    GetEndpointMetadataFromNetwork = \"getEndpointMetadataFromNetwork\",\r\n    GetCloudDiscoveryMetadataFromNetworkMeasurement = \"getCloudDiscoveryMetadataFromNetworkMeasurement\",\r\n\r\n    HandleRedirectPromiseMeasurement= \"handleRedirectPromiseMeasurement\",\r\n\r\n    UpdateCloudDiscoveryMetadataMeasurement = \"updateCloudDiscoveryMetadataMeasurement\",\r\n\r\n    UsernamePasswordClientAcquireToken = \"usernamePasswordClientAcquireToken\",\r\n\r\n    NativeMessageHandlerHandshake = \"nativeMessageHandlerHandshake\",\r\n\r\n    /**\r\n     * Cache operations\r\n     */\r\n    ClearTokensAndKeysWithClaims = \"clearTokensAndKeysWithClaims\",\r\n}\r\n\r\n/**\r\n * State of the performance event.\r\n *\r\n * @export\r\n * @enum {number}\r\n */\r\nexport enum PerformanceEventStatus {\r\n    NotStarted,\r\n    InProgress,\r\n    Completed\r\n}\r\n\r\n/**\r\n * Fields whose value will not change throughout a request\r\n */\r\nexport type StaticFields = {\r\n    /**\r\n     * The Silent Token Cache Lookup Policy\r\n     *\r\n     * @type {?(number | undefined)}\r\n     */\r\n    cacheLookupPolicy?: number | undefined,\r\n\r\n    /**\r\n     * Size of the id token\r\n     *\r\n     * @type {number}\r\n     */\r\n    idTokenSize?: number,\r\n\r\n    /**\r\n     *\r\n     * Size of the access token\r\n     *\r\n     * @type {number}\r\n     */\r\n\r\n    accessTokenSize?: number,\r\n\r\n    /**\r\n     *\r\n     * Size of the refresh token\r\n     *\r\n     * @type {number}\r\n     */\r\n\r\n    refreshTokenSize?: number | undefined,\r\n\r\n    /**\r\n     * Application name as specified by the app.\r\n     *\r\n     * @type {?string}\r\n     */\r\n    appName?: string,\r\n\r\n    /**\r\n     * Application version as specified by the app.\r\n     *\r\n     * @type {?string}\r\n     */\r\n    appVersion?: string,\r\n\r\n    /**\r\n     * The following are fields that may be emitted in native broker scenarios\r\n     */\r\n    extensionId?: string,\r\n    extensionVersion?: string\r\n    matsBrokerVersion?: string;\r\n    matsAccountJoinOnStart?: string;\r\n    matsAccountJoinOnEnd?: string;\r\n    matsDeviceJoin?: string;\r\n    matsPromptBehavior?: string;\r\n    matsApiErrorCode?: number;\r\n    matsUiVisible?: boolean;\r\n    matsSilentCode?: number;\r\n    matsSilentBiSubCode?: number;\r\n    matsSilentMessage?: string;\r\n    matsSilentStatus?: number;\r\n    matsHttpStatus?: number\r\n    matsHttpEventCount?: number;\r\n    httpVerToken?: string;\r\n    httpVerAuthority?: string;\r\n\r\n    /**\r\n     * Native broker fields\r\n     */\r\n    allowNativeBroker?: boolean;\r\n    extensionInstalled?: boolean;\r\n    extensionHandshakeTimeoutMs?: number;\r\n    extensionHandshakeTimedOut?: boolean;\r\n};\r\n\r\n/**\r\n * Fields whose value may change throughout a request\r\n */\r\nexport type Counters = {\r\n    visibilityChangeCount?: number;\r\n    incompleteSubsCount?: number;\r\n    /**\r\n     * Amount of times queued in the JS event queue.\r\n     *\r\n     * @type {?number}\r\n     */\r\n    queuedCount?: number\r\n    /**\r\n     * Amount of manually completed queue events.\r\n     *\r\n     * @type {?number}\r\n     */\r\n    queuedManuallyCompletedCount?: number;\r\n};\r\n\r\nexport type SubMeasurement = {\r\n    name: PerformanceEvents,\r\n    startTimeMs: number\r\n};\r\n\r\n/**\r\n * Performance measurement taken by the library, including metadata about the request and application.\r\n *\r\n * @export\r\n * @typedef {PerformanceEvent}\r\n */\r\nexport type PerformanceEvent = StaticFields & Counters & {\r\n    /**\r\n     * Unique id for the event\r\n     *\r\n     * @type {string}\r\n     */\r\n    eventId: string,\r\n\r\n    /**\r\n     * State of the perforance measure.\r\n     *\r\n     * @type {PerformanceEventStatus}\r\n     */\r\n    status: PerformanceEventStatus,\r\n\r\n    /**\r\n     * Login authority used for the request\r\n     *\r\n     * @type {string}\r\n     */\r\n    authority: string,\r\n\r\n    /**\r\n     * Client id for the application\r\n     *\r\n     * @type {string}\r\n     */\r\n    clientId: string\r\n\r\n    /**\r\n     * Correlation ID used for the request\r\n     *\r\n     * @type {string}\r\n     */\r\n    correlationId: string,\r\n\r\n    /**\r\n     * End-to-end duration in milliseconds.\r\n     * @date 3/22/2022 - 3:40:05 PM\r\n     *\r\n     * @type {number}\r\n     */\r\n    durationMs?: number,\r\n\r\n    /**\r\n     * Visibility of the page when the event completed.\r\n     * Read from: https://developer.mozilla.org/docs/Web/API/Page_Visibility_API\r\n     *\r\n     * @type {?(string | null)}\r\n     */\r\n    endPageVisibility?: string | null,\r\n\r\n    /**\r\n     * Whether the result was retrieved from the cache.\r\n     *\r\n     * @type {(boolean | null)}\r\n     */\r\n    fromCache?: boolean | null,\r\n\r\n    /**\r\n     * Event name (usually in the form of classNameFunctionName)\r\n     *\r\n     * @type {PerformanceEvents}\r\n     */\r\n    name: PerformanceEvents,\r\n\r\n    /**\r\n     * Visibility of the page when the event completed.\r\n     * Read from: https://developer.mozilla.org/docs/Web/API/Page_Visibility_API\r\n     *\r\n     * @type {?(string | null)}\r\n     */\r\n    startPageVisibility?: string | null,\r\n\r\n    /**\r\n     * Unix millisecond timestamp when the event was initiated.\r\n     *\r\n     * @type {number}\r\n     */\r\n    startTimeMs: number,\r\n\r\n    /**\r\n     * Whether or the operation completed successfully.\r\n     *\r\n     * @type {(boolean | null)}\r\n     */\r\n    success?: boolean | null,\r\n\r\n    /**\r\n     * Add specific error code in case of failure\r\n     *\r\n     * @type {string}\r\n     */\r\n    errorCode?: string,\r\n\r\n    /**\r\n     * Add specific sub error code in case of failure\r\n     *\r\n     * @type {string}\r\n     */\r\n    subErrorCode?: string,\r\n\r\n    /**\r\n     * Name of the library used for the operation.\r\n     *\r\n     * @type {string}\r\n     */\r\n    libraryName: string,\r\n\r\n    /**\r\n     * Version of the library used for the operation.\r\n     *\r\n     * @type {string}\r\n     */\r\n    libraryVersion: string,\r\n\r\n    /**\r\n     * Whether the response is from a native component (e.g., WAM)\r\n     *\r\n     * @type {?boolean}\r\n     */\r\n    isNativeBroker?: boolean,\r\n\r\n    /**\r\n     * Request ID returned from the response\r\n     *\r\n     * @type {?string}\r\n     */\r\n    requestId?: string\r\n\r\n    /**\r\n     * Cache lookup policy\r\n     *\r\n     * @type {?number}\r\n     */\r\n    cacheLookupPolicy?: number | undefined,\r\n\r\n    /**\r\n     * Amount of time spent in the JS queue in milliseconds.\r\n     *\r\n     * @type {?number}\r\n     */\r\n    queuedTimeMs?: number,\r\n\r\n    /**\r\n     * Sub-measurements for internal use. To be deleted before flushing.\r\n     */\r\n    incompleteSubMeasurements?: Map<string, SubMeasurement>\r\n};\r\n\r\nexport const IntFields: ReadonlySet<string> = new Set([\r\n    \"accessTokenSize\",\r\n    \"durationMs\",\r\n    \"idTokenSize\",\r\n    \"matsSilentStatus\",\r\n    \"matsHttpStatus\",\r\n    \"refreshTokenSize\",\r\n    \"queuedTimeMs\",\r\n    \"startTimeMs\",\r\n    \"status\",\r\n]);\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;;;;AAKG;IACS,kBA6OX;AA7OD,CAAA,UAAY,iBAAiB,EAAA;AAEzB;;;AAGG;AACH,IAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AAEzC;;;AAGG;AACH,IAAA,iBAAA,CAAA,4BAAA,CAAA,GAAA,4BAAyD,CAAA;AAEzD;;;AAGG;AACH,IAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AAEzC;;;AAGG;AACH,IAAA,iBAAA,CAAA,yBAAA,CAAA,GAAA,yBAAmD,CAAA;AAEnD;;;AAGG;AACH,IAAA,iBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AAEvC;;;AAGG;AACH,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;AAErE;;;AAGG;AACH,IAAA,iBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AAEvC;;;AAGG;AACH,IAAA,iBAAA,CAAA,+BAAA,CAAA,GAAA,+BAA+D,CAAA;AAE/D;;;AAGG;AACH,IAAA,iBAAA,CAAA,gCAAA,CAAA,GAAA,gCAAiE,CAAA;AAEjE;;;AAGG;AACH,IAAA,iBAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AAEnE;;;AAGG;AACH,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AAEvB;;;AAGG;AACH,IAAA,iBAAA,CAAA,iDAAA,CAAA,GAAA,iDAAmG,CAAA;AAEnG;;;AAGG;AACH,IAAA,iBAAA,CAAA,gCAAA,CAAA,GAAA,gCAAiE,CAAA;AAEjE;;;AAGG;AACH,IAAA,iBAAA,CAAA,qCAAA,CAAA,GAAA,qCAA2E,CAAA;AAC3E;;AAEG;AACH,IAAA,iBAAA,CAAA,qCAAA,CAAA,GAAA,qCAA2E,CAAA;AAC3E;;AAEG;AACH,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,iBAAoC,CAAA;AACpC;;AAEG;AACH,IAAA,iBAAA,CAAA,oCAAA,CAAA,GAAA,oCAAyE,CAAA;AACzE;;AAEG;AACH,IAAA,iBAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAE7C;;AAEG;AACH,IAAA,iBAAA,CAAA,uCAAA,CAAA,GAAA,uCAA+E,CAAA;AAE/E;;AAEG;AACH,IAAA,iBAAA,CAAA,gCAAA,CAAA,GAAA,gCAAiE,CAAA;AAEjE;;AAEG;AACH,IAAA,iBAAA,CAAA,sDAAA,CAAA,GAAA,sDAA6G,CAAA;AAE7G;;AAEG;AACH,IAAA,iBAAA,CAAA,8CAAA,CAAA,GAAA,8CAA6F,CAAA;AAE7F;;AAEG;AACH,IAAA,iBAAA,CAAA,0CAAA,CAAA,GAAA,0CAAqF,CAAA;AAErF;;;AAGG;AACH,IAAA,iBAAA,CAAA,uBAAA,CAAA,GAAA,uBAA+C,CAAA;AAE/C;;;AAGG;AACH,IAAA,iBAAA,CAAA,4BAAA,CAAA,GAAA,4BAAyD,CAAA;AAEzD;;AAEG;AACH,IAAA,iBAAA,CAAA,uBAAA,CAAA,GAAA,uBAA+C,CAAA;AAE/C;;AAEG;AACH,IAAA,iBAAA,CAAA,yBAAA,CAAA,GAAA,yBAAmD,CAAA;AAEnD,IAAA,iBAAA,CAAA,6BAAA,CAAA,GAAA,6BAA2D,CAAA;AAE3D;;AAEG;AACH,IAAA,iBAAA,CAAA,+BAAA,CAAA,GAAA,+BAA+D,CAAA;AAE/D;;AAEG;AACH,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;AACrE,IAAA,iBAAA,CAAA,mCAAA,CAAA,GAAA,mCAAuE,CAAA;AACvE,IAAA,iBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AAEjD;;AAEG;AACH,IAAA,iBAAA,CAAA,+CAAA,CAAA,GAAA,+CAA+F,CAAA;AAC/F,IAAA,iBAAA,CAAA,iDAAA,CAAA,GAAA,iDAAmG,CAAA;AACnG,IAAA,iBAAA,CAAA,yDAAA,CAAA,GAAA,yDAAmH,CAAA;AACnH,IAAA,iBAAA,CAAA,6DAAA,CAAA,GAAA,6DAA2H,CAAA;AAE3H;;AAEG;AACH,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;AAEjC;;AAEG;AACH,IAAA,iBAAA,CAAA,8BAAA,CAAA,GAAA,8BAA6D,CAAA;AAC7D,IAAA,iBAAA,CAAA,4BAAA,CAAA,GAAA,4BAAyD,CAAA;AACzD,IAAA,iBAAA,CAAA,8BAAA,CAAA,GAAA,8BAA6D,CAAA;AAE7D;;AAEG;AACH,IAAA,iBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD,IAAA,iBAAA,CAAA,+BAAA,CAAA,GAAA,+BAA+D,CAAA;AAC/D,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;AACrE,IAAA,iBAAA,CAAA,6BAAA,CAAA,GAAA,6BAA2D,CAAA;AAE3D;;AAEG;AACH,IAAA,iBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C,IAAA,iBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAE3C;;AAEG;AACH,IAAA,iBAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AAEvD;;AAEG;AACH,IAAA,iBAAA,CAAA,0CAAA,CAAA,GAAA,0CAAqF,CAAA;AACrF,IAAA,iBAAA,CAAA,gCAAA,CAAA,GAAA,gCAAiE,CAAA;AACjE,IAAA,iBAAA,CAAA,+CAAA,CAAA,GAAA,+CAA+F,CAAA;AAC/F,IAAA,iBAAA,CAAA,uCAAA,CAAA,GAAA,uCAA+E,CAAA;AAC/E,IAAA,iBAAA,CAAA,yCAAA,CAAA,GAAA,yCAAmF,CAAA;AACnF,IAAA,iBAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AACnE,IAAA,iBAAA,CAAA,gDAAA,CAAA,GAAA,gDAAiG,CAAA;AAEjG;;AAEG;AACH,IAAA,iBAAA,CAAA,6BAAA,CAAA,GAAA,6BAA2D,CAAA;AAC3D,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;AACrE,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAqE,CAAA;AAErE,IAAA,iBAAA,CAAA,yBAAA,CAAA,GAAA,yBAAmD,CAAA;AAEnD,IAAA,iBAAA,CAAA,gCAAA,CAAA,GAAA,gCAAiE,CAAA;AACjE,IAAA,iBAAA,CAAA,iDAAA,CAAA,GAAA,iDAAmG,CAAA;AAEnG,IAAA,iBAAA,CAAA,kCAAA,CAAA,GAAA,kCAAoE,CAAA;AAEpE,IAAA,iBAAA,CAAA,yCAAA,CAAA,GAAA,yCAAmF,CAAA;AAEnF,IAAA,iBAAA,CAAA,oCAAA,CAAA,GAAA,oCAAyE,CAAA;AAEzE,IAAA,iBAAA,CAAA,+BAAA,CAAA,GAAA,+BAA+D,CAAA;AAE/D;;AAEG;AACH,IAAA,iBAAA,CAAA,8BAAA,CAAA,GAAA,8BAA6D,CAAA;AACjE,CAAC,EA7OW,iBAAiB,KAAjB,iBAAiB,GA6O5B,EAAA,CAAA,CAAA,CAAA;AAED;;;;;AAKG;IACS,uBAIX;AAJD,CAAA,UAAY,sBAAsB,EAAA;AAC9B,IAAA,sBAAA,CAAA,sBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,sBAAA,CAAA,sBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU,CAAA;AACV,IAAA,sBAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS,CAAA;AACb,CAAC,EAJW,sBAAsB,KAAtB,sBAAsB,GAIjC,EAAA,CAAA,CAAA,CAAA;AAuQY,IAAA,SAAS,GAAwB,IAAI,GAAG,CAAC;IAClD,iBAAiB;IACjB,YAAY;IACZ,aAAa;IACb,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,QAAQ;AACX,CAAA;;;;"}