const mongoose = require('mongoose');

const cvTemplateSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true
    },
    previewImage: {
        type: String,
        required: true
    },
    htmlTemplate: {
        type: String,
        required: true
    },
    cssTemplate: {
        type: String,
        required: true
    },
    categoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'CVTemplateCategory',
        required: true
    },
    category: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        unique: true,
        trim: true
    },
    isPremium: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    },
    order: {
        type: Number,
        default: 0
    },
    tags: [{
        type: String,
        trim: true
    }],
    fields: [{
        fieldName: {
            type: String,
            required: true
        },
        fieldLabel: {
            type: String,
            required: true
        },
        fieldType: {
            type: String,
            required: true,
            enum: ['text', 'textarea', 'date', 'select', 'checkbox', 'radio', 'file']
        },
        fieldOrder: {
            type: Number,
            default: 0
        },
        isRequired: {
            type: Boolean,
            default: false
        },
        defaultValue: {
            type: String
        },
        options: [{
            label: String,
            value: String
        }]
    }],
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Cập nhật thời gian khi cập nhật mẫu CV
cvTemplateSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Tìm kiếm mẫu CV theo tên
cvTemplateSchema.statics.findByName = async function(name) {
    try {
        const regex = new RegExp(name, 'i');
        return await this.find({ name: regex });
    } catch (error) {
        console.error('Lỗi khi tìm mẫu CV theo tên:', error);
        throw error;
    }
};

// Lấy tất cả mẫu CV đang hoạt động
cvTemplateSchema.statics.getAllActive = async function() {
    try {
        return await this.find({ isActive: true }).sort({ order: 1, name: 1 });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách mẫu CV đang hoạt động:', error);
        throw error;
    }
};

// Lấy mẫu CV theo danh mục
cvTemplateSchema.statics.findByCategory = async function(categoryId) {
    try {
        return await this.find({ categoryId, isActive: true }).sort({ order: 1, name: 1 });
    } catch (error) {
        console.error('Lỗi khi lấy mẫu CV theo danh mục:', error);
        throw error;
    }
};

// Lấy mẫu CV theo loại (premium hoặc free)
cvTemplateSchema.statics.findByPremium = async function(isPremium) {
    try {
        return await this.find({ isPremium, isActive: true }).sort({ order: 1, name: 1 });
    } catch (error) {
        console.error('Lỗi khi lấy mẫu CV theo loại:', error);
        throw error;
    }
};

module.exports = mongoose.model('CVTemplate', cvTemplateSchema, 'cv_templates');
