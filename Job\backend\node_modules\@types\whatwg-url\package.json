{"name": "@types/whatwg-url", "version": "8.2.2", "description": "TypeScript definitions for whatwg-url", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/whatwg-url", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/aomarks", "githubUsername": "aomarks"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/whatwg-url"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/webidl-conversions": "*"}, "typesPublisherContentHash": "ea85d67c501583ff421cfbf206fafac0410c464edef169a6d88e06ecfe226dfc", "typeScriptVersion": "4.0"}