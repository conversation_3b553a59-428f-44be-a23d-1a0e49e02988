# Import libraries
import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import os

# Load and clean data from multiple CSV files
csv_files = []

# Get all CSV files in current directory
csv_files_list = [f for f in os.listdir('.') if f.endswith('.csv')]
print(f'Found {len(csv_files_list)} CSV files: {csv_files_list}')

for filename in csv_files_list:
    print(f'Loading file: {filename}')
    try:
        df_temp = pd.read_csv(filename)
        csv_files.append(df_temp)
        print(f'  - Loaded {len(df_temp)} records from {filename}')
    except Exception as e:
        print(f'  - Error loading {filename}: {e}')

# Combine all CSV files into one DataFrame
df = pd.concat(csv_files, ignore_index=True)
print(f'Total records before cleaning: {len(df)}')
print(f'Columns: {list(df.columns)}')

# Clean data
df = df.dropna()
print(f'Records after removing NaN: {len(df)}')

df = df[df['Order ID'].str.isnumeric()]
print(f'Records after filtering numeric Order ID: {len(df)}')

df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'], errors='coerce')
df['Price Each'] = pd.to_numeric(df['Price Each'], errors='coerce')
df['Order Date'] = pd.to_datetime(df['Order Date'], errors='coerce')

# Remove rows with invalid conversions
df = df.dropna(subset=['Quantity Ordered', 'Price Each', 'Order Date'])
print(f'Records after final cleaning: {len(df)}')

print(f'Date range: {df["Order Date"].min()} to {df["Order Date"].max()}')
print(f'Sample data:')
print(df.head())