# Import libraries
import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import os
import seaborn as sns
from collections import Counter

# Set style for better plots
plt.style.use('default')
sns.set_palette('husl')

# Load and clean data from multiple CSV files
csv_files = []

# Get all CSV files in current directory
csv_files_list = [f for f in os.listdir('.') if f.endswith('.csv')]
print(f'Found {len(csv_files_list)} CSV files: {csv_files_list}')

for filename in csv_files_list:
    print(f'Loading file: {filename}')
    try:
        df_temp = pd.read_csv(filename)
        csv_files.append(df_temp)
        print(f'  - Loaded {len(df_temp)} records from {filename}')
    except Exception as e:
        print(f'  - Error loading {filename}: {e}')

# Combine all CSV files into one DataFrame
df = pd.concat(csv_files, ignore_index=True)
print(f'\nTotal records before cleaning: {len(df)}')
print(f'Columns: {list(df.columns)}')

# Clean data
df = df.dropna()
print(f'Records after removing NaN: {len(df)}')

df = df[df['Order ID'].str.isnumeric()]
print(f'Records after filtering numeric Order ID: {len(df)}')

df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'], errors='coerce')
df['Price Each'] = pd.to_numeric(df['Price Each'], errors='coerce')
df['Order Date'] = pd.to_datetime(df['Order Date'], errors='coerce')

# Remove rows with invalid conversions
df = df.dropna(subset=['Quantity Ordered', 'Price Each', 'Order Date'])
print(f'Records after final cleaning: {len(df)}')

# Add calculated columns
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

print(f'\nDate range: {df["Order Date"].min()} to {df["Order Date"].max()}')
print(f'Total Sales: ${df["Sales"].sum():,.2f}')
print(f'Sample data:')
print(df.head())

# Phân tích doanh số theo tháng
monthly_sales = df.groupby('Month')['Sales'].sum().reset_index()
monthly_sales['Month_Name'] = monthly_sales['Month'].map({
    1: 'January', 2: 'February', 3: 'March', 4: 'April',
    5: 'May', 6: 'June', 7: 'July', 8: 'August',
    9: 'September', 10: 'October', 11: 'November', 12: 'December'
})

# Tìm tháng có doanh số cao nhất
best_month = monthly_sales.loc[monthly_sales['Sales'].idxmax()]
print(f'Tháng có doanh số cao nhất: {best_month["Month_Name"]} (Tháng {best_month["Month"]})')
print(f'Doanh số: ${best_month["Sales"]:,.2f}')

# Vẽ biểu đồ
plt.figure(figsize=(12, 6))
bars = plt.bar(monthly_sales['Month_Name'], monthly_sales['Sales'])
plt.title('Doanh số bán hàng theo tháng', fontsize=16, fontweight='bold')
plt.xlabel('Tháng', fontsize=12)
plt.ylabel('Doanh số ($)', fontsize=12)
plt.xticks(rotation=45)

# Highlight tháng có doanh số cao nhất
max_idx = monthly_sales['Sales'].idxmax()
bars[max_idx].set_color('red')

# Thêm giá trị lên các cột
for i, v in enumerate(monthly_sales['Sales']):
    plt.text(i, v + 50000, f'${v/1000000:.1f}M', ha='center', va='bottom')

plt.tight_layout()
plt.show()

print(f'\nTop 3 tháng có doanh số cao nhất:')
top_months = monthly_sales.nlargest(3, 'Sales')
for idx, row in top_months.iterrows():
    print(f'{row["Month_Name"]}: ${row["Sales"]:,.2f}')

# Phân tích doanh số theo thành phố
city_sales = df.groupby('City')['Sales'].sum().reset_index().sort_values('Sales', ascending=False)

# Tìm thành phố có doanh số cao nhất
best_city = city_sales.iloc[0]
print(f'Thành phố có doanh số cao nhất: {best_city["City"]}')
print(f'Doanh số: ${best_city["Sales"]:,.2f}')

# Vẽ biểu đồ
plt.figure(figsize=(12, 8))
bars = plt.bar(city_sales['City'], city_sales['Sales'])
plt.title('Doanh số bán hàng theo thành phố', fontsize=16, fontweight='bold')
plt.xlabel('Thành phố', fontsize=12)
plt.ylabel('Doanh số ($)', fontsize=12)
plt.xticks(rotation=45, ha='right')

# Highlight thành phố có doanh số cao nhất
bars[0].set_color('red')

# Thêm giá trị lên các cột
for i, v in enumerate(city_sales['Sales']):
    plt.text(i, v + 100000, f'${v/1000000:.1f}M', ha='center', va='bottom', rotation=90)

plt.tight_layout()
plt.show()

print(f'\nTop 5 thành phố có doanh số cao nhất:')
for idx, row in city_sales.head().iterrows():
    print(f'{row["City"]}: ${row["Sales"]:,.2f}')