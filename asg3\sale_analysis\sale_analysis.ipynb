import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import glob, os

# Load CSV files
files = glob.glob('Sales_*.csv') or glob.glob('monthly_sale_2019/Sales_*.csv')
df = pd.concat([pd.read_csv(f) for f in files], ignore_index=True)
df.dropna(inplace=True)
df = df[~df['Order Date'].str.contains('Order Date', na=False)]

# Process data
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

print(f'Loaded {len(df):,} records, Total Sales: ${df["Sales"].sum():,.0f}')
df.head()

monthly = df.groupby('Month')['Sales'].sum()
print(f'Month {monthly.idxmax()}: ${monthly.max():,.0f}')
ax = monthly.plot(kind='bar', figsize=(10,6), title='Monthly Sales', color='skyblue')
plt.ylabel('Sales (Million USD)')
plt.xticks(rotation=0)  # Keep month numbers horizontal
ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))
# Add values on top of bars
for i, v in enumerate(monthly):
    ax.text(i, v + v*0.01, f'${v/1e6:.1f}M', ha='center', va='bottom', fontsize=9)
plt.show()

city = df.groupby('City')['Sales'].sum()
print(f'{city.idxmax()}: ${city.max():,.0f}')
top_cities = city.nlargest(5)
ax = top_cities.plot(kind='bar', figsize=(10,6), title='Top 5 Cities by Sales')
plt.ylabel('Sales (Million USD)')
plt.xticks(rotation=45)
ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x/1e6:.1f}M'))
# Add values on top of bars
for i, v in enumerate(top_cities):
    ax.text(i, v + v*0.01, f'${v/1e6:.1f}M', ha='center', va='bottom', fontsize=9)
plt.show()

hourly = df.groupby('Hour').size()
print(f'{hourly.idxmax()}PM: {hourly.max():,} orders')
ax = hourly.plot(kind='line', figsize=(10,6), title='Orders by Hour', marker='o', color='orange')
plt.xlabel('Hour')
plt.ylabel('Number of Orders')
# Add values on all points
for hour, count in hourly.items():
    ax.annotate(f'{count:,}', (hour, count), textcoords='offset points', xytext=(0,10), ha='center', fontsize=8)
plt.show()

product = df.groupby('Product')['Quantity Ordered'].sum()
print(f'{product.idxmax()}: {product.max():,} units')
top_products = product.nlargest(5)
ax = top_products.plot(kind='barh', figsize=(12,6), title='Top 5 Best Selling Products', color='lightgreen')
plt.xlabel('Quantity Sold (units)')
ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:,.0f}'))
# Add values on bars
for i, v in enumerate(top_products):
    ax.text(v + v*0.01, i, f'{v:,.0f}', va='center', fontsize=9)
plt.show()

from collections import Counter
pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) 
         for combo in combinations(products, 2) if len(products) > 1]
for pair, count in Counter(pairs).most_common(3):
    print(f'{pair[0]} + {pair[1]}: {count} times')