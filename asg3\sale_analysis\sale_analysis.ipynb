import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import os

# Load data
files = [f for f in os.listdir('.') if f.endswith('.csv')]
df = pd.concat([pd.read_csv(f) for f in files]).dropna()
df = df[df['Order ID'].str.isnumeric()]

# Clean data
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

print(f'Loaded {len(df)} records, Total Sales: ${df["Sales"].sum():,.0f}')

monthly = df.groupby('Month')['Sales'].sum()
print(f'Tháng {monthly.idxmax()}: ${monthly.max():,.0f}')
monthly.plot(kind='bar', title='Doanh số theo tháng')
plt.show()

city = df.groupby('City')['Sales'].sum()
print(f'{city.idxmax()}: ${city.max():,.0f}')
city.nlargest(5).plot(kind='bar', title='Top 5 thành phố')
plt.show()

hourly = df.groupby('Hour').size()
print(f'{hourly.idxmax()}h: {hourly.max()} đơn hàng')
hourly.plot(kind='line', marker='o', title='Đơn hàng theo giờ')
plt.show()

product = df.groupby('Product')['Quantity Ordered'].sum()
print(f'{product.idxmax()}: {product.max()} sản phẩm')
product.nlargest(5).plot(kind='barh', title='Top 5 sản phẩm bán chạy')
plt.show()

from collections import Counter
pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) 
         for combo in combinations(products, 2) if len(products) > 1]
top_pairs = Counter(pairs).most_common(3)
for pair, count in top_pairs:
    print(f'{pair[0]} + {pair[1]}: {count} lần')