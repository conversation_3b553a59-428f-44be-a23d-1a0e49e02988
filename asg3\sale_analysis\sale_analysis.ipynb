# Import libraries
import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations

# Load and clean data
df = pd.read_csv('sales_analysis.csv').dropna()
df = df[df['Order ID'].str.isnumeric()]
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'], errors='coerce').dropna()