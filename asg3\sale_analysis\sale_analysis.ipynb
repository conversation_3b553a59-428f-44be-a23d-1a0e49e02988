import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import glob, os

# Load CSV files
files = glob.glob('Sales_*.csv') or glob.glob('monthly_sale_2019/Sales_*.csv')
df = pd.concat([pd.read_csv(f) for f in files], ignore_index=True)
df.dropna(inplace=True)
df = df[~df['Order Date'].str.contains('Order Date', na=False)]

# Process data
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

print(f'Loaded {len(df):,} records, Total Sales: ${df["Sales"].sum():,.0f}')
df.head()

monthly = df.groupby('Month')['Sales'].sum()
print(f'Month {monthly.idxmax()}: ${monthly.max():,.0f}')
monthly.plot(kind='bar', figsize=(10,6), title='Monthly Sales', color='skyblue')
plt.ylabel('Sales (Million USD)')
plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))
plt.show()

city = df.groupby('City')['Sales'].sum()
print(f'{city.idxmax()}: ${city.max():,.0f}')
city.nlargest(5).plot(kind='bar', figsize=(10,6), title='Top 5 Cities by Sales')
plt.ylabel('Sales (Million USD)')
plt.xticks(rotation=45)
plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x/1e6:.1f}M'))
plt.show()

hourly = df.groupby('Hour').size()
print(f'{hourly.idxmax()}h: {hourly.max()} đơn hàng')
plt.figure(figsize=(10, 6))
hourly.plot(kind='line', marker='o', title='Số đơn hàng theo giờ trong ngày', color='orange')
plt.ylabel('Số đơn hàng')
plt.xlabel('Giờ (0-23h)')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

product = df.groupby('Product')['Quantity Ordered'].sum()
print(f'{product.idxmax()}: {product.max():,} units')
product.nlargest(5).plot(kind='barh', figsize=(12,6), title='Top 5 Best Selling Products', color='lightgreen')
plt.xlabel('Quantity Sold (units)')
plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:,.0f}'))
plt.show()

from collections import Counter
pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) 
         for combo in combinations(products, 2) if len(products) > 1]
for pair, count in Counter(pairs).most_common(3):
    print(f'{pair[0]} + {pair[1]}: {count} times')