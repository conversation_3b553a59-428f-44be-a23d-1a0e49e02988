import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import glob
import os

# Đọc file CSV (thử cả 2 đường dẫn)
path = '.'
all_files = glob.glob(os.path.join(path, 'Sales_*.csv'))

# <PERSON><PERSON><PERSON> không tìm thấy file ở thư mục hiện tại, thử thư mục con
if not all_files:
    path = 'monthly_sale_2019'
    all_files = glob.glob(os.path.join(path, 'Sales_*.csv'))

print(f'Tìm thấy {len(all_files)} files CSV')

# Merge all CSV files into one DataFrame
df = pd.concat([pd.read_csv(f) for f in all_files], ignore_index=True)

# Drop rows with any missing values
df.dropna(how='any', inplace=True)

# Remove rows that are likely duplicate headers
df = df[~df['Order Date'].str.contains('Order Date', na=False)]

# Xử lý dữ liệu
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

# Hiển thị thông tin tổng quan
print(f'✅ Loaded {len(df):,} records, Total Sales: ${df["Sales"].sum():,.0f}')
print(f'📊 Data: {df["Order Date"].min().strftime("%m/%Y")} - {df["Order Date"].max().strftime("%m/%Y")}, {df["City"].nunique()} cities, {df["Product"].nunique()} products')

# DataFrame sẽ hiển thị dạng bảng đẹp (phải là dòng cuối)
df.head()

monthly = df.groupby('Month')['Sales'].sum()
print(f'Tháng {monthly.idxmax()}: ${monthly.max():,.0f}')
plt.figure(figsize=(10, 6))
monthly.plot(kind='bar', title='Doanh thu theo tháng', color='skyblue')
plt.ylabel('Doanh thu (triệu USD)')
plt.xlabel('Tháng')
plt.xticks(rotation=0)
ax = plt.gca()
ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))
plt.tight_layout()
plt.show()

city = df.groupby('City')['Sales'].sum()
print(f'{city.idxmax()}: ${city.max():,.0f}')
plt.figure(figsize=(10, 6))
city.nlargest(5).plot(kind='bar', title='Top 5 thành phố theo doanh thu')
plt.ylabel('Doanh thu (triệu USD)')
plt.xlabel('Thành phố')
plt.xticks(rotation=45, ha='right')
# Chuyển đổi trục Y sang triệu USD
ax = plt.gca()
ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))
plt.tight_layout()
plt.show()

hourly = df.groupby('Hour').size()
print(f'{hourly.idxmax()}h: {hourly.max()} đơn hàng')
plt.figure(figsize=(10, 6))
hourly.plot(kind='line', marker='o', title='Số đơn hàng theo giờ trong ngày', color='orange')
plt.ylabel('Số đơn hàng')
plt.xlabel('Giờ (0-23h)')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

product = df.groupby('Product')['Quantity Ordered'].sum()
print(f'{product.idxmax()}: {product.max():,} sản phẩm')
plt.figure(figsize=(12, 6))
product.nlargest(5).plot(kind='barh', title='Top 5 sản phẩm bán chạy nhất', color='lightgreen')
plt.xlabel('Số lượng bán (sản phẩm)')
plt.ylabel('Sản phẩm')
# Thêm dấu phẩy cho số lượng
ax = plt.gca()
ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
plt.tight_layout()
plt.show()

from collections import Counter
pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) 
         for combo in combinations(products, 2) if len(products) > 1]
top_pairs = Counter(pairs).most_common(3)
for pair, count in top_pairs:
    print(f'{pair[0]} + {pair[1]}: {count} lần')