import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations
import os

# Load data
files = [f for f in os.listdir('.') if f.endswith('.csv')]
df = pd.concat([pd.read_csv(f) for f in files]).dropna()
df = df[df['Order ID'].str.isnumeric()]

# Clean data
df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])
df['Price Each'] = pd.to_numeric(df['Price Each'])
df['Order Date'] = pd.to_datetime(df['Order Date'])
df['Sales'] = df['Quantity Ordered'] * df['Price Each']
df['Month'] = df['Order Date'].dt.month
df['Hour'] = df['Order Date'].dt.hour
df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()

print(f'Loaded {len(df)} records, Total Sales: ${df["Sales"].sum():,.0f}')

# <PERSON><PERSON> tích doanh số theo tháng
monthly_sales = df.groupby('Month')['Sales'].sum().reset_index()
monthly_sales['Month_Name'] = monthly_sales['Month'].map({
    1: 'January', 2: 'February', 3: 'March', 4: 'April',
    5: 'May', 6: 'June', 7: 'July', 8: 'August',
    9: 'September', 10: 'October', 11: 'November', 12: 'December'
})

# Tìm tháng có doanh số cao nhất
best_month = monthly_sales.loc[monthly_sales['Sales'].idxmax()]
print(f'Tháng có doanh số cao nhất: {best_month["Month_Name"]} (Tháng {best_month["Month"]})')
print(f'Doanh số: ${best_month["Sales"]:,.2f}')

# Vẽ biểu đồ
plt.figure(figsize=(12, 6))
bars = plt.bar(monthly_sales['Month_Name'], monthly_sales['Sales'])
plt.title('Doanh số bán hàng theo tháng', fontsize=16, fontweight='bold')
plt.xlabel('Tháng', fontsize=12)
plt.ylabel('Doanh số ($)', fontsize=12)
plt.xticks(rotation=45)

# Highlight tháng có doanh số cao nhất
max_idx = monthly_sales['Sales'].idxmax()
bars[max_idx].set_color('red')

# Thêm giá trị lên các cột
for i, v in enumerate(monthly_sales['Sales']):
    plt.text(i, v + 50000, f'${v/1000000:.1f}M', ha='center', va='bottom')

plt.tight_layout()
plt.show()

print(f'\nTop 3 tháng có doanh số cao nhất:')
top_months = monthly_sales.nlargest(3, 'Sales')
for idx, row in top_months.iterrows():
    print(f'{row["Month_Name"]}: ${row["Sales"]:,.2f}')

# Phân tích doanh số theo thành phố
city_sales = df.groupby('City')['Sales'].sum().reset_index().sort_values('Sales', ascending=False)

# Tìm thành phố có doanh số cao nhất
best_city = city_sales.iloc[0]
print(f'Thành phố có doanh số cao nhất: {best_city["City"]}')
print(f'Doanh số: ${best_city["Sales"]:,.2f}')

# Vẽ biểu đồ
plt.figure(figsize=(12, 8))
bars = plt.bar(city_sales['City'], city_sales['Sales'])
plt.title('Doanh số bán hàng theo thành phố', fontsize=16, fontweight='bold')
plt.xlabel('Thành phố', fontsize=12)
plt.ylabel('Doanh số ($)', fontsize=12)
plt.xticks(rotation=45, ha='right')

# Highlight thành phố có doanh số cao nhất
bars[0].set_color('red')

# Thêm giá trị lên các cột
for i, v in enumerate(city_sales['Sales']):
    plt.text(i, v + 100000, f'${v/1000000:.1f}M', ha='center', va='bottom', rotation=90)

plt.tight_layout()
plt.show()

print(f'\nTop 5 thành phố có doanh số cao nhất:')
for idx, row in city_sales.head().iterrows():
    print(f'{row["City"]}: ${row["Sales"]:,.2f}')

# Phân tích ngắn gọn
print('1. Tháng có doanh số cao nhất:', df.groupby('Month')['Sales'].sum().idxmax())
print('2. Thành phố có doanh số cao nhất:', df.groupby('City')['Sales'].sum().idxmax())
print('3. Giờ có nhiều đơn hàng nhất:', df.groupby('Hour').size().idxmax(), 'h')
print('4. Sản phẩm bán chạy nhất:', df.groupby('Product')['Quantity Ordered'].sum().idxmax())

# Sản phẩm thường mua cùng
pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) 
         for combo in combinations(products, 2) if len(products) > 1]
from collections import Counter
print('5. Cặp sản phẩm mua cùng nhiều nhất:', Counter(pairs).most_common(1)[0])

# Biểu đồ đơn giản
fig, axes = plt.subplots(2, 2, figsize=(12, 8))
df.groupby('Month')['Sales'].sum().plot(kind='bar', ax=axes[0,0], title='Doanh số theo tháng')
df.groupby('City')['Sales'].sum().nlargest(5).plot(kind='bar', ax=axes[0,1], title='Top 5 thành phố')
df.groupby('Hour').size().plot(kind='line', ax=axes[1,0], title='Đơn hàng theo giờ')
df.groupby('Product')['Quantity Ordered'].sum().nlargest(5).plot(kind='barh', ax=axes[1,1], title='Top 5 sản phẩm')
plt.tight_layout()
plt.show()