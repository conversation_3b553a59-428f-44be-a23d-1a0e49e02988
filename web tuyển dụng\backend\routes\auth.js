const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { auth } = require('../middleware/auth');

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const { fullName, email, password, phone } = req.body;
    
    // Validate input
    if (!fullName || !email || !password) {
      return res.status(400).json({ error: 'Vui lòng điền đầy đủ thông tin bắt buộc' });
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Email không hợp lệ' });
    }
    
    // Validate password length
    if (password.length < 6) {
      return res.status(400).json({ error: '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự' });
    }
    
    // Register user
    const result = await User.register({ fullName, email, password, phone });
    
    res.status(201).json(result);
  } catch (error) {
    console.error('Register error:', error);
    res.status(400).json({ error: error.message || 'Đăng ký không thành công' });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Validate input
    if (!email || !password) {
      return res.status(400).json({ error: 'Vui lòng điền đầy đủ email và mật khẩu' });
    }
    
    // Login user
    const result = await User.login(email, password);
    
    res.status(200).json(result);
  } catch (error) {
    console.error('Login error:', error);
    res.status(400).json({ error: error.message || 'Đăng nhập không thành công' });
  }
});

// @route   GET /api/auth/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get user
    const user = await User.getById(userId);
    
    if (!user) {
      return res.status(404).json({ error: 'Không tìm thấy người dùng' });
    }
    
    res.status(200).json({ user });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy thông tin người dùng' });
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { fullName, phone } = req.body;
    
    // Validate input
    if (!fullName) {
      return res.status(400).json({ error: 'Vui lòng điền họ và tên' });
    }
    
    // Update user
    const updatedUser = await User.update(userId, { fullName, phone });
    
    res.status(200).json({ user: updatedUser });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi cập nhật thông tin người dùng' });
  }
});

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { oldPassword, newPassword } = req.body;
    
    // Validate input
    if (!oldPassword || !newPassword) {
      return res.status(400).json({ error: 'Vui lòng điền đầy đủ mật khẩu cũ và mới' });
    }
    
    // Validate password length
    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'Mật khẩu mới phải có ít nhất 6 ký tự' });
    }
    
    // Change password
    await User.changePassword(userId, oldPassword, newPassword);
    
    res.status(200).json({ message: 'Đổi mật khẩu thành công' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(400).json({ error: error.message || 'Lỗi khi đổi mật khẩu' });
  }
});

module.exports = router;
