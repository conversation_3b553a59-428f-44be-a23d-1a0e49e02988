/* Simple Blue Header Styles */
:root {
    --primary-blue: #2563eb;
    --primary-dark-blue: #1d4ed8;
    --primary-light-blue: #3b82f6;
    --text-white: #ffffff;
    --text-light: rgba(255, 255, 255, 0.8);
}

.simple-header {
    background-color: var(--primary-blue);
    padding: 0.75rem 0;
    color: var(--text-white);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-container img {
    height: 32px;
    width: auto;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-white);
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 1.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-white);
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.nav-link:hover {
    color: var(--text-light);
}

.nav-link i {
    font-size: 0.75rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: white;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0.5rem 0;
    display: none;
    z-index: 10;
}

.nav-item:hover .dropdown-menu {
    display: block;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
    color: var(--primary-blue);
}

.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    display: block;
    width: 24px;
    height: 2px;
    background-color: var(--text-white);
    margin: 5px 0;
    transition: all 0.3s ease;
}

/* Responsive */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        background-color: var(--primary-blue);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        display: none;
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-list {
        flex-direction: column;
        width: 100%;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        padding: 0.75rem 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        display: none;
        padding: 0;
        margin-left: 1rem;
        animation: none;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .nav-item.active .dropdown-menu {
        display: block;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        color: var(--text-white);
    }

    .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text-white);
    }
}
