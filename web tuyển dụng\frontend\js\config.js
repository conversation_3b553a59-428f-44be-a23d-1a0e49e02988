// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://localhost:5000',
    API_PREFIX: '/api',
    ENDPOINTS: {
        AUTH: {
            LOGIN: '/auth/login',
            REGISTER: '/auth/register',
            PROFILE: '/auth/profile'
        },
        JOBS: {
            LIST: '/jobs',
            SEARCH: '/jobs/search',
            APPLY: '/jobs/apply',
            CREATE: '/jobs/create',
            UPDATE: '/jobs/update',
            DELETE: '/jobs/delete'
        },
        PROFILES: {
            GET: '/profiles',
            UPDATE: '/profiles/update',
            CV: '/profiles/cv'
        }
    },
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
};

// Helper function to get full API URL
function getApiUrl(endpoint) {
    return `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint}`;
}

// Helper function to get headers with authentication
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        ...API_CONFIG.HEADERS,
        'Authorization': token ? `Bearer ${token}` : ''
    };
}

// API request helper
async function apiRequest(endpoint, options = {}) {
    try {
        const url = getApiUrl(endpoint);
        const headers = options.skipAuth ? API_CONFIG.HEADERS : getAuthHeaders();
        
        const response = await fetch(url, {
            ...options,
            headers: {
                ...headers,
                ...options.headers
            }
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'API request failed');
        }

        return data;
    } catch (error) {
        console.error('API request error:', error);
        throw error;
    }
} 