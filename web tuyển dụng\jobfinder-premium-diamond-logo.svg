<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:0.9" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="8" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="8" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Diamond Pattern Background -->
  <g opacity="0.1">
    <path d="M20,20 L30,10 L40,20 L30,30 Z" fill="white"/>
    <path d="M40,20 L50,10 L60,20 L50,30 Z" fill="white"/>
    <path d="M60,20 L70,10 L80,20 L70,30 Z" fill="white"/>
    
    <path d="M20,40 L30,30 L40,40 L30,50 Z" fill="white"/>
    <path d="M40,40 L50,30 L60,40 L50,50 Z" fill="white"/>
    <path d="M60,40 L70,30 L80,40 L70,50 Z" fill="white"/>
    
    <path d="M20,60 L30,50 L40,60 L30,70 Z" fill="white"/>
    <path d="M40,60 L50,50 L60,60 L50,70 Z" fill="white"/>
    <path d="M60,60 L70,50 L80,60 L70,70 Z" fill="white"/>
    
    <path d="M20,80 L30,70 L40,80 L30,90 Z" fill="white"/>
    <path d="M40,80 L50,70 L60,80 L50,90 Z" fill="white"/>
    <path d="M60,80 L70,70 L80,80 L70,90 Z" fill="white"/>
  </g>
  
  <!-- Main Icon: Magnifying Glass with Diamond -->
  <g transform="translate(20, 25)">
    <!-- Magnifying Glass -->
    <circle cx="18" cy="18" r="15" fill="none" stroke="white" stroke-width="3"/>
    <line x1="28" y1="28" x2="38" y2="38" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Glass Interior -->
    <circle cx="18" cy="18" r="12" fill="url(#accentGradient)" opacity="0.15"/>
    
    <!-- Diamond/Gem -->
    <g transform="translate(40, 10)">
      <!-- Diamond Shape -->
      <path d="M0,10 L10,0 L20,10 L10,20 Z" fill="url(#diamondGradient)"/>
      
      <!-- Diamond Facets -->
      <path d="M0,10 L10,0 L10,20 Z" fill="white" opacity="0.3"/>
      <path d="M10,0 L20,10 L10,20 Z" fill="#064e3b" opacity="0.1"/>
      
      <!-- Diamond Sparkle -->
      <circle cx="7" cy="7" r="1" fill="white"/>
      <circle cx="13" cy="5" r="0.5" fill="white"/>
    </g>
    
    <!-- Glass Reflection -->
    <path d="M12,12 Q18,22 24,12" stroke="white" stroke-width="1.5" fill="none" opacity="0.8"/>
    
    <!-- Connecting Line -->
    <path d="M33,18 L40,18" stroke="white" stroke-width="1.5" stroke-dasharray="2,2"/>
  </g>
  
  <!-- Border Accent -->
  <rect x="15" y="15" width="70" height="70" rx="5" fill="none" stroke="url(#accentGradient)" stroke-width="1" opacity="0.5"/>
  
  <!-- Bottom Accent -->
  <rect x="10" y="85" width="80" height="5" rx="2.5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Reflective Highlight -->
  <path d="M10,10 L90,10 L90,30 Q50,40 10,30 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
</svg>
