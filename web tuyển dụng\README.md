# JobFinder - <PERSON><PERSON> thống Tuyển dụng

Hệ thống tuyển dụng JobFinder giúp kết nối ứng viên với các cơ hội việc làm IT hấp dẫn từ các công ty hàng đầu tại Việt Nam.

## Y<PERSON>u cầu hệ thống

- Node.js (v14 trở lên)
- Microsoft SQL Server
- Modern web browser (Chrome, Firefox, Edge)

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd <project-folder>
```

2. Cài đặt dependencies cho backend:
```bash
cd backend
npm install
```

3. Cấu hình database:
- Tạo database mới trong SQL Server
- Chạy script `database_mssql.sql` để tạo các bảng và dữ liệu mẫu
- Cập nhật thông tin kết nối trong file `backend/.env`

4. Khởi động backend:
```bash
cd backend
npm run dev
```
Server sẽ chạy tại http://localhost:5000

5. Mở frontend:
- Mở file `index.html` trong trình duyệt
- Hoặc sử dụng Live Server extension trong VS Code

## Tính năng chính

- **Cho người tìm việc:**
  - Đăng ký/đăng nhập tài khoản
  - Tìm kiếm việc làm với nhiều bộ lọc
  - Quản lý hồ sơ và CV
  - Ứng tuyển việc làm
  - Theo dõi trạng thái ứng tuyển

- **Cho nhà tuyển dụng:**
  - Đăng tin tuyển dụng
  - Quản lý tin đăng
  - Xem hồ sơ ứng viên
  - Quản lý quy trình tuyển dụng

## Cấu trúc thư mục

```
jobfinder/
├── backend/                 # Backend API
│   ├── config/              # Cấu hình
│   ├── middleware/          # Middleware
│   ├── models/              # Models
│   ├── routes/              # Routes API
│   ├── .env                 # Biến môi trường
│   ├── server.js            # Entry point
│   └── README.md            # Hướng dẫn backend
├── css/                     # Stylesheets
├── database/                # Cấu trúc database
│   ├── stored_procedures/   # Stored procedures
│   ├── tables/              # Cấu trúc bảng và dữ liệu mẫu
│   ├── database_mssql.sql   # Script SQL Server
│   └── init.sql             # Script khởi tạo
├── frontend/                # Frontend components
│   ├── css/                 # CSS riêng cho frontend
│   ├── js/                  # JavaScript riêng cho frontend
│   └── register.html        # Trang đăng ký
├── js/                      # JavaScript chung
├── pages/                   # Các trang
│   ├── advanced-search.html # Trang tìm kiếm nâng cao
│   ├── employer.html        # Trang nhà tuyển dụng
│   ├── job-search.html      # Trang tìm kiếm việc làm
│   ├── jobs.html            # Trang danh sách việc làm
│   └── profile.html         # Trang hồ sơ
├── index.html               # Trang chủ
├── start.bat                # Script khởi động (Windows)
└── start.sh                 # Script khởi động (Linux/Mac)
```

## API Documentation

API documentation có thể được truy cập tại: http://localhost:5000/api-docs

## Security

- JWT authentication
- Password hashing với bcrypt
- SQL injection prevention
- XSS protection
- CORS configuration