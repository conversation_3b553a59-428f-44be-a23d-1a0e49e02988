version: "{build}"

environment:
  matrix:
    - nodejs_version: "14"
    - nodejs_version: "16"
    - nodejs_version: "18"

branches:
  only:
    - master
    - /^maint\/.+/
    - /v\d+\.\d+\.\d+/

install:
  - ps: Update-NodeJsInstallation (Get-NodeJsLatestBuild $env:nodejs_version)
  - npm install

services:
  - mssql2017

cache:
  - node_modules

build: off

before_test:
  - npm prune

  - sc config sqlbrowser start= auto
  - net start sqlbrowser

  - mkdir C:\Users\<USER>\.tedious
  - ps: >-
      Set-Content -Value $('{
        "config": {
          "server": "localhost",
          "authentication": {
            "type": "default",
            "options": {
              "userName": "sa",
              "password": "Password12!"
            }
          },
          "options": {
            "database": "master",
            "trustServerCertificate": true
          }
        },

        "ntlm": {
          "server": "localhost",
          "authentication": {
            "type": "ntlm",
            "options": {
              "userName": "' + $env:username + '",
              "password": "' + [Microsoft.Win32.Registry]::GetValue("HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion\Winlogon", "DefaultPassword", '') + '",
              "domain": "' + $env:computername + '"
            }
          },
          "options": {
            "database": "master",
            "trustServerCertificate": true
          }
        }
      }') -Path C:\Users\<USER>\.tedious\test-connection.json


test_script:
  - node --version
  - npm --version

  - cmd: |
      SET EXITVAL=0

      npm run-script test || SET EXITVAL=1

      SET TEDIOUS_TDS_VERSION=7_4
      npm run-script test-integration || SET EXITVAL=1

      SET TEDIOUS_TDS_VERSION=7_3_B
      npm run-script test-integration || SET EXITVAL=1

      SET TEDIOUS_TDS_VERSION=7_3_A
      npm run-script test-integration || SET EXITVAL=1

      SET TEDIOUS_TDS_VERSION=7_2
      npm run-script test-integration || SET EXITVAL=1

      SET TEDIOUS_TDS_VERSION=7_1
      npm run-script test-integration || SET EXITVAL=1

      EXIT /B %EXITVAL%
