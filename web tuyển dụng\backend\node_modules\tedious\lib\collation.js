"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.codepageBySortId = exports.codepageByLanguageId = exports.Flags = exports.Collation = void 0;
// http://technet.microsoft.com/en-us/library/aa176553(v=sql.80).aspx
const codepageByLanguageId = {
  // Arabic_*
  [0x0401]: 'CP1256',
  // Chinese_Taiwan_Stroke_*
  // Chinese_Traditional_Stroke_Count_*
  // Chinese_Taiwan_Bopomofo_*
  // Chinese_Traditional_Bopomofo_*
  [0x0404]: 'CP950',
  // Czech_*
  [0x0405]: 'CP1250',
  // Danish_Greenlandic_*
  // Danish_Norwegian_*
  [0x0406]: 'CP1252',
  // Greek_*
  [0x0408]: 'CP1253',
  // Latin1_General_*
  [0x0409]: 'CP1252',
  // Traditional_Spanish_*
  [0x040A]: 'CP1252',
  // Finnish_Swedish_*
  [0x040B]: 'CP1252',
  // French_*
  [0x040C]: 'CP1252',
  // Hebrew_*
  [0x040D]: 'CP1255',
  // Hungarian_*
  // Hungarian_Technical_*
  [0x040E]: 'CP1250',
  // Icelandic_*
  [0x040F]: 'CP1252',
  // Japanese_*
  // Japanese_XJIS_*
  // Japanese_Unicode_*
  // Japanese_Bushu_Kakusu_*
  [0x0411]: 'CP932',
  // Korean_*
  // Korean_Wansung_*
  [0x0412]: 'CP949',
  // Norwegian_*
  [0x0414]: 'CP1252',
  // Polish_*
  [0x0415]: 'CP1250',
  // Romansh_*
  [0x0417]: 'CP1252',
  // Romanian_*
  [0x0418]: 'CP1250',
  // Cyrillic_*
  [0x0419]: 'CP1251',
  // Croatian_*
  [0x041A]: 'CP1250',
  // Slovak_*
  [0x041B]: 'CP1250',
  // Albanian_*
  [0x041C]: 'CP1250',
  // Thai_*
  [0x041E]: 'CP874',
  // Turkish_*
  [0x041F]: 'CP1254',
  // Urdu_*
  [0x0420]: 'CP1256',
  // Ukrainian_*
  [0x0422]: 'CP1251',
  // Slovenian_*
  [0x0424]: 'CP1250',
  // Estonian_*
  [0x0425]: 'CP1257',
  // Latvian_*
  [0x0426]: 'CP1257',
  // Lithuanian_*
  [0x0427]: 'CP1257',
  // Persian_*
  [0x0429]: 'CP1256',
  // Vietnamese_*
  [0x042A]: 'CP1258',
  // Azeri_Latin_*
  [0x042C]: 'CP1254',
  // Upper_Sorbian_*
  [0x042E]: 'CP1252',
  // Macedonian_FYROM_*
  [0x042F]: 'CP1251',
  // Sami_Norway_*
  [0x043B]: 'CP1252',
  // Kazakh_*
  [0x043F]: 'CP1251',
  // Turkmen_*
  [0x0442]: 'CP1250',
  // Uzbek_Latin_*
  [0x0443]: 'CP1254',
  // Tatar_*
  [0x0444]: 'CP1251',
  // Welsh_*
  [0x0452]: 'CP1252',
  // Frisian_*
  [0x0462]: 'CP1252',
  // Bashkir_*
  [0x046D]: 'CP1251',
  // Mapudungan_*
  [0x047A]: 'CP1252',
  // Mohawk_*
  [0x047C]: 'CP1252',
  // Breton_*
  [0x047E]: 'CP1252',
  // Uighur_*
  [0x0480]: 'CP1256',
  // Corsican_*
  [0x0483]: 'CP1252',
  // Yakut_*
  [0x0485]: 'CP1251',
  // Dari_*
  [0x048C]: 'CP1256',
  // Chinese_PRC_*
  // Chinese_Simplified_Pinyin_*
  // Chinese_PRC_Stroke_*
  // Chinese_Simplified_Stroke_Order_*
  [0x0804]: 'CP936',
  // Serbian_Latin_*
  [0x081A]: 'CP1250',
  // Azeri_Cyrillic_*
  [0x082C]: 'CP1251',
  // Sami_Sweden_Finland_*
  [0x083B]: 'CP1252',
  // Tamazight_*
  [0x085F]: 'CP1252',
  // Chinese_Hong_Kong_Stroke_*
  [0x0C04]: 'CP950',
  // Modern_Spanish_*
  [0x0C0A]: 'CP1252',
  // Serbian_Cyrillic_*
  [0x0C1A]: 'CP1251',
  // Chinese_Traditional_Pinyin_*
  // Chinese_Traditional_Stroke_Order_*
  [0x1404]: 'CP950',
  // Bosnian_Latin_*
  [0x141A]: 'CP1250',
  // Bosnian_Cyrillic_*
  [0x201A]: 'CP1251',
  // German
  // German_PhoneBook_*
  [0x0407]: 'CP1252',
  // Georgian_Modern_Sort_*
  [0x0437]: 'CP1252'
};
exports.codepageByLanguageId = codepageByLanguageId;
const codepageBySortId = {
  [30]: 'CP437',
  // SQL_Latin1_General_CP437_BIN
  [31]: 'CP437',
  // SQL_Latin1_General_CP437_CS_AS
  [32]: 'CP437',
  // SQL_Latin1_General_CP437_CI_AS
  [33]: 'CP437',
  // SQL_Latin1_General_Pref_CP437_CI_AS
  [34]: 'CP437',
  // SQL_Latin1_General_CP437_CI_AI
  [40]: 'CP850',
  // SQL_Latin1_General_CP850_BIN
  [41]: 'CP850',
  // SQL_Latin1_General_CP850_CS_AS
  [42]: 'CP850',
  // SQL_Latin1_General_CP850_CI_AS
  [43]: 'CP850',
  // SQL_Latin1_General_Pref_CP850_CI_AS
  [44]: 'CP850',
  // SQL_Latin1_General_CP850_CI_AI
  [49]: 'CP850',
  // SQL_1xCompat_CP850_CI_AS
  [51]: 'CP1252',
  // SQL_Latin1_General_Cp1_CS_AS_KI_WI
  [52]: 'CP1252',
  // SQL_Latin1_General_Cp1_CI_AS_KI_WI
  [53]: 'CP1252',
  // SQL_Latin1_General_Pref_Cp1_CI_AS_KI_WI
  [54]: 'CP1252',
  // SQL_Latin1_General_Cp1_CI_AI_KI_WI
  [55]: 'CP850',
  // SQL_AltDiction_CP850_CS_AS
  [56]: 'CP850',
  // SQL_AltDiction_Pref_CP850_CI_AS
  [57]: 'CP850',
  // SQL_AltDiction_CP850_CI_AI
  [58]: 'CP850',
  // SQL_Scandinavian_Pref_CP850_CI_AS
  [59]: 'CP850',
  // SQL_Scandinavian_CP850_CS_AS
  [60]: 'CP850',
  // SQL_Scandinavian_CP850_CI_AS
  [61]: 'CP850',
  // SQL_AltDiction_CP850_CI_AS
  [80]: 'CP1250',
  // SQL_Latin1_General_1250_BIN
  [81]: 'CP1250',
  // SQL_Latin1_General_CP1250_CS_AS
  [82]: 'CP1250',
  // SQL_Latin1_General_Cp1250_CI_AS_KI_WI
  [83]: 'CP1250',
  // SQL_Czech_Cp1250_CS_AS_KI_WI
  [84]: 'CP1250',
  // SQL_Czech_Cp1250_CI_AS_KI_WI
  [85]: 'CP1250',
  // SQL_Hungarian_Cp1250_CS_AS_KI_WI
  [86]: 'CP1250',
  // SQL_Hungarian_Cp1250_CI_AS_KI_WI
  [87]: 'CP1250',
  // SQL_Polish_Cp1250_CS_AS_KI_WI
  [88]: 'CP1250',
  // SQL_Polish_Cp1250_CI_AS_KI_WI
  [89]: 'CP1250',
  // SQL_Romanian_Cp1250_CS_AS_KI_WI
  [90]: 'CP1250',
  // SQL_Romanian_Cp1250_CI_AS_KI_WI
  [91]: 'CP1250',
  // SQL_Croatian_Cp1250_CS_AS_KI_WI
  [92]: 'CP1250',
  // SQL_Croatian_Cp1250_CI_AS_KI_WI
  [93]: 'CP1250',
  // SQL_Slovak_Cp1250_CS_AS_KI_WI
  [94]: 'CP1250',
  // SQL_Slovak_Cp1250_CI_AS_KI_WI
  [95]: 'CP1250',
  // SQL_Slovenian_Cp1250_CS_AS_KI_WI
  [96]: 'CP1250',
  // SQL_Slovenian_Cp1250_CI_AS_KI_WI
  [104]: 'CP1251',
  // SQL_Latin1_General_1251_BIN
  [105]: 'CP1251',
  // SQL_Latin1_General_CP1251_CS_AS
  [106]: 'CP1251',
  // SQL_Latin1_General_CP1251_CI_AS
  [107]: 'CP1251',
  // SQL_Ukrainian_Cp1251_CS_AS_KI_WI
  [108]: 'CP1251',
  // SQL_Ukrainian_Cp1251_CI_AS_KI_WI
  [112]: 'CP1253',
  // SQL_Latin1_General_1253_BIN
  [113]: 'CP1253',
  // SQL_Latin1_General_CP1253_CS_AS
  [114]: 'CP1253',
  // SQL_Latin1_General_CP1253_CI_AS
  [120]: 'CP1253',
  // SQL_MixDiction_CP1253_CS_AS
  [121]: 'CP1253',
  // SQL_AltDiction_CP1253_CS_AS
  [122]: 'CP1253',
  // SQL_AltDiction2_CP1253_CS_AS
  [124]: 'CP1253',
  // SQL_Latin1_General_CP1253_CI_AI
  [128]: 'CP1254',
  // SQL_Latin1_General_1254_BIN
  [129]: 'CP1254',
  // SQL_Latin1_General_Cp1254_CS_AS_KI_WI
  [130]: 'CP1254',
  // SQL_Latin1_General_Cp1254_CI_AS_KI_WI
  [136]: 'CP1255',
  // SQL_Latin1_General_1255_BIN
  [137]: 'CP1255',
  // SQL_Latin1_General_CP1255_CS_AS
  [138]: 'CP1255',
  // SQL_Latin1_General_CP1255_CI_AS
  [144]: 'CP1256',
  // SQL_Latin1_General_1256_BIN
  [145]: 'CP1256',
  // SQL_Latin1_General_CP1256_CS_AS
  [146]: 'CP1256',
  // SQL_Latin1_General_CP1256_CI_AS
  [152]: 'CP1257',
  // SQL_Latin1_General_1257_BIN
  [153]: 'CP1257',
  // SQL_Latin1_General_CP1257_CS_AS
  [154]: 'CP1257',
  // SQL_Latin1_General_CP1257_CI_AS
  [155]: 'CP1257',
  // SQL_Estonian_Cp1257_CS_AS_KI_WI
  [156]: 'CP1257',
  // SQL_Estonian_Cp1257_CI_AS_KI_WI
  [157]: 'CP1257',
  // SQL_Latvian_Cp1257_CS_AS_KI_WI
  [158]: 'CP1257',
  // SQL_Latvian_Cp1257_CI_AS_KI_WI
  [159]: 'CP1257',
  // SQL_Lithuanian_Cp1257_CS_AS_KI_WI
  [160]: 'CP1257',
  // SQL_Lithuanian_Cp1257_CI_AS_KI_WI
  [183]: 'CP1252',
  // SQL_Danish_Pref_Cp1_CI_AS_KI_WI
  [184]: 'CP1252',
  // SQL_SwedishPhone_Pref_Cp1_CI_AS_KI_WI
  [185]: 'CP1252',
  // SQL_SwedishStd_Pref_Cp1_CI_AS_KI_WI
  [186]: 'CP1252' // SQL_Icelandic_Pref_Cp1_CI_AS_KI_WI

};
exports.codepageBySortId = codepageBySortId;
const Flags = {
  IGNORE_CASE: 1 << 0,
  IGNORE_ACCENT: 1 << 1,
  IGNORE_KANA: 1 << 2,
  IGNORE_WIDTH: 1 << 3,
  BINARY: 1 << 4,
  BINARY2: 1 << 5,
  UTF8: 1 << 6
};
exports.Flags = Flags;

class Collation {
  static fromBuffer(buffer, offset = 0) {
    let lcid = (buffer[offset + 2] & 0x0F) << 16;
    lcid |= buffer[offset + 1] << 8;
    lcid |= buffer[offset + 0];
    let flags = (buffer[offset + 3] & 0x0F) << 4;
    flags |= (buffer[offset + 2] & 0xF0) >>> 4;
    const version = (buffer[offset + 3] & 0xF0) >>> 4;
    const sortId = buffer[offset + 4];
    return new this(lcid, flags, version, sortId);
  }

  constructor(lcid, flags, version, sortId) {
    this.lcid = void 0;
    this.flags = void 0;
    this.version = void 0;
    this.sortId = void 0;
    this.codepage = void 0;
    this.buffer = void 0;
    this.buffer = undefined;
    this.lcid = lcid;
    this.flags = flags;
    this.version = version;
    this.sortId = sortId;

    if (this.flags & Flags.UTF8) {
      this.codepage = 'utf-8';
    } else if (this.sortId) {
      this.codepage = codepageBySortId[this.sortId];
    } else {
      // The last 16 bits of the LCID are the language id.
      // The first 4 bits define additional sort orders.
      const languageId = this.lcid & 0xFFFF;
      this.codepage = codepageByLanguageId[languageId];
    }
  }

  toBuffer() {
    if (this.buffer) {
      return this.buffer;
    }

    this.buffer = Buffer.alloc(5);
    this.buffer[0] = this.lcid & 0xFF;
    this.buffer[1] = this.lcid >>> 8 & 0xFF;
    this.buffer[2] = this.lcid >>> 16 & 0x0F | (this.flags & 0x0F) << 4;
    this.buffer[3] = (this.flags & 0xF0) >>> 4 | (this.version & 0x0F) << 4;
    this.buffer[4] = this.sortId & 0xFF;
    return this.buffer;
  }

}

exports.Collation = Collation;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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