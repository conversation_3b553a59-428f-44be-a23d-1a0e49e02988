/* Utility Classes */

/* Text Sizes */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

/* Font Weights */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Text Colors */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-50 { color: #f9fafb; }
.text-gray-100 { color: #f3f4f6; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }

.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }

.text-green-500 { color: #10b981; }
.text-green-600 { color: #059669; }
.text-green-700 { color: #047857; }

.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }

/* Background Colors */
.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-700 { background-color: #1d4ed8; }

.bg-green-50 { background-color: #ecfdf5; }
.bg-green-100 { background-color: #d1fae5; }
.bg-green-500 { background-color: #10b981; }
.bg-green-600 { background-color: #059669; }
.bg-green-700 { background-color: #047857; }

.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }
.bg-red-700 { background-color: #b91c1c; }

/* Max Width */
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-screen-sm { max-width: 640px; }
.max-w-screen-md { max-width: 768px; }
.max-w-screen-lg { max-width: 1024px; }
.max-w-screen-xl { max-width: 1280px; }
.max-w-screen-2xl { max-width: 1536px; }

/* Transforms */
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-gpu { transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-none { transform: none; }

/* Translate */
.translate-x-0 { --tw-translate-x: 0px; }
.translate-x-1 { --tw-translate-x: 0.25rem; }
.translate-x-2 { --tw-translate-x: 0.5rem; }
.translate-x-4 { --tw-translate-x: 1rem; }
.translate-x-8 { --tw-translate-x: 2rem; }
.-translate-x-1 { --tw-translate-x: -0.25rem; }
.-translate-x-2 { --tw-translate-x: -0.5rem; }
.-translate-x-4 { --tw-translate-x: -1rem; }
.-translate-x-8 { --tw-translate-x: -2rem; }
.translate-x-1/2 { --tw-translate-x: 50%; }
.translate-x-full { --tw-translate-x: 100%; }
.-translate-x-1/2 { --tw-translate-x: -50%; }
.-translate-x-full { --tw-translate-x: -100%; }

.translate-y-0 { --tw-translate-y: 0px; }
.translate-y-1 { --tw-translate-y: 0.25rem; }
.translate-y-2 { --tw-translate-y: 0.5rem; }
.translate-y-4 { --tw-translate-y: 1rem; }
.translate-y-8 { --tw-translate-y: 2rem; }
.-translate-y-1 { --tw-translate-y: -0.25rem; }
.-translate-y-2 { --tw-translate-y: -0.5rem; }
.-translate-y-4 { --tw-translate-y: -1rem; }
.-translate-y-8 { --tw-translate-y: -2rem; }
.translate-y-1/2 { --tw-translate-y: 50%; }
.translate-y-full { --tw-translate-y: 100%; }
.-translate-y-1/2 { --tw-translate-y: -50%; }
.-translate-y-full { --tw-translate-y: -100%; }

/* Spacing */
.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Borders */
.border { border-width: 1px; }
.border-0 { border-width: 0px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }

.border-t { border-top-width: 1px; }
.border-r { border-right-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-l { border-left-width: 1px; }

.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }
.border-none { border-style: none; }

.border-white { border-color: #ffffff; }
.border-black { border-color: #000000; }
.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-400 { border-color: #9ca3af; }
.border-gray-500 { border-color: #6b7280; }
.border-gray-600 { border-color: #4b5563; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-gray-900 { border-color: #111827; }

.border-blue-500 { border-color: #3b82f6; }
.border-blue-600 { border-color: #2563eb; }
.border-blue-700 { border-color: #1d4ed8; }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Transitions */
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

/* Responsive Utilities */
@media (min-width: 640px) {
    .sm\\:text-lg { font-size: 1.125rem; }
    .sm\\:text-xl { font-size: 1.25rem; }
    .sm\\:text-2xl { font-size: 1.5rem; }
    .sm\\:text-3xl { font-size: 1.875rem; }
}

@media (min-width: 768px) {
    .md\\:text-lg { font-size: 1.125rem; }
    .md\\:text-xl { font-size: 1.25rem; }
    .md\\:text-2xl { font-size: 1.5rem; }
    .md\\:text-3xl { font-size: 1.875rem; }
    .md\\:text-4xl { font-size: 2.25rem; }
}

@media (min-width: 1024px) {
    .lg\\:text-xl { font-size: 1.25rem; }
    .lg\\:text-2xl { font-size: 1.5rem; }
    .lg\\:text-3xl { font-size: 1.875rem; }
    .lg\\:text-4xl { font-size: 2.25rem; }
    .lg\\:text-5xl { font-size: 3rem; }
}
