{"version": 3, "file": "ClientInfo.js", "sources": ["../../src/account/ClientInfo.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\nimport { Separators, Constants } from \"../utils/Constants\";\r\n\r\n/**\r\n * Client info object which consists of two IDs. Need to add more info here.\r\n */\r\nexport type ClientInfo = {\r\n    uid: string,\r\n    utid: string\r\n};\r\n\r\n/**\r\n * Function to build a client info object from server clientInfo string\r\n * @param rawClientInfo\r\n * @param crypto\r\n */\r\nexport function buildClientInfo(rawClientInfo: string, crypto: ICrypto): ClientInfo {\r\n    if (StringUtils.isEmpty(rawClientInfo)) {\r\n        throw ClientAuthError.createClientInfoEmptyError();\r\n    }\r\n\r\n    try {\r\n        const decodedClientInfo: string = crypto.base64Decode(rawClientInfo);\r\n        return JSON.parse(decodedClientInfo) as ClientInfo;\r\n    } catch (e) {\r\n        throw ClientAuthError.createClientInfoDecodingError((e as ClientAuthError).message);\r\n    }\r\n}\r\n\r\n/**\r\n * Function to build a client info object from cached homeAccountId string\r\n * @param homeAccountId \r\n */\r\nexport function buildClientInfoFromHomeAccountId(homeAccountId: string): ClientInfo {\r\n    if (StringUtils.isEmpty(homeAccountId)) {\r\n        throw ClientAuthError.createClientInfoDecodingError(\"Home account ID was empty.\");\r\n    }\r\n    const clientInfoParts: string[] = homeAccountId.split(Separators.CLIENT_INFO_SEPARATOR, 2);\r\n    return {\r\n        uid: clientInfoParts[0],\r\n        utid: clientInfoParts.length < 2 ? Constants.EMPTY_STRING : clientInfoParts[1]\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAeH;;;;AAIG;AACa,SAAA,eAAe,CAAC,aAAqB,EAAE,MAAe,EAAA;AAClE,IAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACpC,QAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,KAAA;IAED,IAAI;QACA,IAAM,iBAAiB,GAAW,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACrE,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAe,CAAC;AACtD,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;QACR,MAAM,eAAe,CAAC,6BAA6B,CAAE,CAAqB,CAAC,OAAO,CAAC,CAAC;AACvF,KAAA;AACL,CAAC;AAED;;;AAGG;AACG,SAAU,gCAAgC,CAAC,aAAqB,EAAA;AAClE,IAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACpC,QAAA,MAAM,eAAe,CAAC,6BAA6B,CAAC,4BAA4B,CAAC,CAAC;AACrF,KAAA;AACD,IAAA,IAAM,eAAe,GAAa,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;IAC3F,OAAO;AACH,QAAA,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;AACvB,QAAA,IAAI,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC,CAAC;KACjF,CAAC;AACN;;;;"}