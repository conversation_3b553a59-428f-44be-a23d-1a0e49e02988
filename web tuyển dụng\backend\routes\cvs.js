const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');
const CV = require('../models/CV');
const CVTemplate = require('../models/CVTemplate');
const { auth } = require('../middleware/auth');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '..', process.env.UPLOAD_DIR || 'uploads', 'cvs');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'cv-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// @route   GET /api/cvs
// @desc    Get user CVs
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const cvs = await CV.getByUserId(userId);
    
    res.status(200).json({ cvs });
  } catch (error) {
    console.error('Get CVs error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh sách CV' });
  }
});

// @route   GET /api/cvs/:id
// @desc    Get CV detail
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    
    const cv = await CV.getDetail(cvId, userId);
    
    if (!cv) {
      return res.status(404).json({ error: 'Không tìm thấy CV' });
    }
    
    res.status(200).json({ cv });
  } catch (error) {
    console.error('Get CV detail error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy thông tin CV' });
  }
});

// @route   POST /api/cvs
// @desc    Create new CV
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { 
      profileId, templateId, title, content, 
      isPrimary, isPublic 
    } = req.body;
    
    // Validate required fields
    if (!templateId || !title) {
      return res.status(400).json({ error: 'Mẫu CV và tiêu đề là bắt buộc' });
    }
    
    // Check if template exists
    const template = await CVTemplate.getById(templateId);
    if (!template) {
      return res.status(400).json({ error: 'Mẫu CV không tồn tại' });
    }
    
    const cv = await CV.create(userId, {
      profileId,
      templateId,
      title,
      content,
      isPrimary: isPrimary ? 1 : 0,
      isPublic: isPublic ? 1 : 0
    });
    
    res.status(201).json({ cv });
  } catch (error) {
    console.error('Create CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tạo CV' });
  }
});

// @route   PUT /api/cvs/:id
// @desc    Update CV
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    const { 
      profileId, title, content, 
      isPrimary, isPublic 
    } = req.body;
    
    const cv = await CV.update(cvId, userId, {
      profileId,
      title,
      content,
      isPrimary,
      isPublic
    });
    
    res.status(200).json({ cv });
  } catch (error) {
    console.error('Update CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi cập nhật CV' });
  }
});

// @route   DELETE /api/cvs/:id
// @desc    Delete CV
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    
    const result = await CV.delete(cvId, userId);
    
    res.status(200).json(result);
  } catch (error) {
    console.error('Delete CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi xóa CV' });
  }
});

// @route   GET /api/cvs/search
// @desc    Search CVs
// @access  Private
router.get('/search', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { keyword } = req.query;
    
    const cvs = await CV.search(userId, keyword);
    
    res.status(200).json({ cvs });
  } catch (error) {
    console.error('Search CVs error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tìm kiếm CV' });
  }
});

// @route   POST /api/cvs/:id/upload
// @desc    Upload CV file
// @access  Private
router.post('/:id/upload', auth, upload.single('cv'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Vui lòng chọn file CV' });
    }
    
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    const cvPath = '/' + path.relative(path.join(__dirname, '..'), req.file.path).replace(/\\/g, '/');
    
    const cv = await CV.update(cvId, userId, {
      cvPath
    });
    
    res.status(200).json({ cv });
  } catch (error) {
    console.error('Upload CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tải lên CV' });
  }
});

// @route   POST /api/cvs/:id/share
// @desc    Create share link for CV
// @access  Private
router.post('/:id/share', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    const { expiryDays, isPasswordProtected, password } = req.body;
    
    // Hash password if provided
    let passwordHash = null;
    if (isPasswordProtected && password) {
      passwordHash = await bcrypt.hash(password, 10);
    }
    
    const shareLink = await CV.createShareLink(
      userId, 
      cvId, 
      expiryDays || 7, 
      isPasswordProtected ? 1 : 0, 
      passwordHash
    );
    
    res.status(201).json({ shareLink });
  } catch (error) {
    console.error('Create share link error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tạo liên kết chia sẻ' });
  }
});

// @route   GET /api/cvs/shared/:token
// @desc    Get shared CV
// @access  Public
router.get('/shared/:token', async (req, res) => {
  try {
    const shareToken = req.params.token;
    const { password } = req.query;
    
    // Hash password if provided
    let passwordHash = null;
    if (password) {
      passwordHash = await bcrypt.hash(password, 10);
    }
    
    const cv = await CV.getFromShareLink(shareToken, passwordHash);
    
    if (!cv) {
      return res.status(404).json({ error: 'Liên kết chia sẻ không tồn tại hoặc đã hết hạn' });
    }
    
    res.status(200).json({ cv });
  } catch (error) {
    console.error('Get shared CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy CV được chia sẻ' });
  }
});

// @route   POST /api/cvs/:id/download
// @desc    Log CV download
// @access  Private
router.post('/:id/download', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const cvId = parseInt(req.params.id);
    const { format } = req.body;
    
    if (!format) {
      return res.status(400).json({ error: 'Định dạng tải xuống là bắt buộc' });
    }
    
    const result = await CV.logDownload(userId, cvId, format);
    
    res.status(200).json(result);
  } catch (error) {
    console.error('Log download error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi ghi lại lịch sử tải xuống' });
  }
});

module.exports = router;
