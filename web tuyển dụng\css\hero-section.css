/* Hero Section Styles */
:root {
    --primary-green: #00a550;
    --primary-light-green: #e6f7ee;
    --primary-dark-green: #008040;
    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #777777;
    --border-color: #e0e0e0;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-lighter: #f2f2f2;
}

.hero {
    background-color: var(--bg-light);
    padding: 3rem 0;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    position: relative;
}

.hero-content {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.search-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.search-bar {
    display: flex;
    width: 100%;
    max-width: 700px;
    gap: 0.5rem;
}

.search-bar input {
    flex: 1;
    padding: 0.875rem 1.25rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    border-color: var(--primary-green);
}

.search-btn {
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: var(--primary-dark-green);
}

.search-tagline {
    font-size: 1rem;
    color: var(--text-medium);
    max-width: 700px;
    margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
    .hero {
        padding: 2rem 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .search-bar {
        flex-direction: column;
    }
    
    .search-btn {
        width: 100%;
    }
}
