/* Professional Header Styles */
:root {
    --primary-green: #00a550;
    --primary-dark-green: #008040;
    --primary-light-green: #e6f7ee;
    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #777777;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-lighter: #f2f2f2;
    --border-color: #e0e0e0;
}

.professional-header {
    background-color: var(--bg-white);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-container img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 1.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-medium);
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-green);
}

.nav-link i {
    font-size: 0.75rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--bg-white);
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 0.5rem 0;
    display: none;
    z-index: 10;
}

.nav-item:hover .dropdown-menu {
    display: block;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--text-medium);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-light-green);
    color: var(--primary-green);
}

.auth-buttons {
    display: flex;
    gap: 0.75rem;
}

.auth-button {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.login-button {
    background-color: var(--bg-white);
    color: var(--primary-green);
    border: 1px solid var(--primary-green);
}

.login-button:hover {
    background-color: var(--primary-light-green);
}

.register-button {
    background-color: var(--primary-green);
    color: white;
    border: 1px solid var(--primary-green);
}

.register-button:hover {
    background-color: var(--primary-dark-green);
}

.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    display: block;
    width: 24px;
    height: 2px;
    background-color: var(--text-dark);
    margin: 5px 0;
    transition: all 0.3s ease;
}

/* Responsive */
@media (max-width: 992px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: var(--bg-white);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        display: none;
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-list {
        flex-direction: column;
        width: 100%;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        padding: 0.75rem 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .dropdown-menu {
        position: static;
        box-shadow: none;
        display: none;
        padding: 0;
        margin-left: 1rem;
        animation: none;
    }

    .nav-item.active .dropdown-menu {
        display: block;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
    }

    .auth-buttons {
        margin-top: 1rem;
        width: 100%;
    }

    .auth-button {
        flex: 1;
        text-align: center;
    }
}
