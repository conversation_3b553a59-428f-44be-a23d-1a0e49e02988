"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bufferToLowerCaseGuid = bufferToLowerCaseGuid;
exports.bufferToUpperCaseGuid = bufferToUpperCaseGuid;
exports.guidToArray = guidToArray;
const UPPER_CASE_MAP = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '0A', '0B', '0C', '0D', '0E', '0F', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '1A', '1B', '1C', '1D', '1E', '1F', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '2A', '2B', '2C', '2D', '2E', '2F', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '3A', '3B', '3C', '3D', '3E', '3F', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '4A', '4B', '4C', '4D', '4E', '4F', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '5A', '5B', '5C', '5D', '5E', '5F', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '6A', '6B', '6C', '6D', '6E', '6F', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '7A', '7B', '7C', '7D', '7E', '7F', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '8A', '8B', '8C', '8D', '8E', '8F', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '9A', '9B', '9C', '9D', '9E', '9F', 'A0', 'A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'B0', 'B1', 'B2', 'B3', 'B4', 'B5', 'B6', 'B7', 'B8', 'B9', 'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'C0', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'CA', 'CB', 'CC', 'CD', 'CE', 'CF', 'D0', 'D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8', 'D9', 'DA', 'DB', 'DC', 'DD', 'DE', 'DF', 'E0', 'E1', 'E2', 'E3', 'E4', 'E5', 'E6', 'E7', 'E8', 'E9', 'EA', 'EB', 'EC', 'ED', 'EE', 'EF', 'F0', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'FA', 'FB', 'FC', 'FD', 'FE', 'FF'];
const LOWER_CASE_MAP = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '0a', '0b', '0c', '0d', '0e', '0f', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '1a', '1b', '1c', '1d', '1e', '1f', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '2a', '2b', '2c', '2d', '2e', '2f', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '3a', '3b', '3c', '3d', '3e', '3f', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '4a', '4b', '4c', '4d', '4e', '4f', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '5a', '5b', '5c', '5d', '5e', '5f', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '6a', '6b', '6c', '6d', '6e', '6f', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '7a', '7b', '7c', '7d', '7e', '7f', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '8a', '8b', '8c', '8d', '8e', '8f', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '9a', '9b', '9c', '9d', '9e', '9f', 'a0', 'a1', 'a2', 'a3', 'a4', 'a5', 'a6', 'a7', 'a8', 'a9', 'aa', 'ab', 'ac', 'ad', 'ae', 'af', 'b0', 'b1', 'b2', 'b3', 'b4', 'b5', 'b6', 'b7', 'b8', 'b9', 'ba', 'bb', 'bc', 'bd', 'be', 'bf', 'c0', 'c1', 'c2', 'c3', 'c4', 'c5', 'c6', 'c7', 'c8', 'c9', 'ca', 'cb', 'cc', 'cd', 'ce', 'cf', 'd0', 'd1', 'd2', 'd3', 'd4', 'd5', 'd6', 'd7', 'd8', 'd9', 'da', 'db', 'dc', 'dd', 'de', 'df', 'e0', 'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7', 'e8', 'e9', 'ea', 'eb', 'ec', 'ed', 'ee', 'ef', 'f0', 'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'fa', 'fb', 'fc', 'fd', 'fe', 'ff'];

function bufferToUpperCaseGuid(buffer) {
  return UPPER_CASE_MAP[buffer[3]] + UPPER_CASE_MAP[buffer[2]] + UPPER_CASE_MAP[buffer[1]] + UPPER_CASE_MAP[buffer[0]] + '-' + UPPER_CASE_MAP[buffer[5]] + UPPER_CASE_MAP[buffer[4]] + '-' + UPPER_CASE_MAP[buffer[7]] + UPPER_CASE_MAP[buffer[6]] + '-' + UPPER_CASE_MAP[buffer[8]] + UPPER_CASE_MAP[buffer[9]] + '-' + UPPER_CASE_MAP[buffer[10]] + UPPER_CASE_MAP[buffer[11]] + UPPER_CASE_MAP[buffer[12]] + UPPER_CASE_MAP[buffer[13]] + UPPER_CASE_MAP[buffer[14]] + UPPER_CASE_MAP[buffer[15]];
}

function bufferToLowerCaseGuid(buffer) {
  return LOWER_CASE_MAP[buffer[3]] + LOWER_CASE_MAP[buffer[2]] + LOWER_CASE_MAP[buffer[1]] + LOWER_CASE_MAP[buffer[0]] + '-' + LOWER_CASE_MAP[buffer[5]] + LOWER_CASE_MAP[buffer[4]] + '-' + LOWER_CASE_MAP[buffer[7]] + LOWER_CASE_MAP[buffer[6]] + '-' + LOWER_CASE_MAP[buffer[8]] + LOWER_CASE_MAP[buffer[9]] + '-' + LOWER_CASE_MAP[buffer[10]] + LOWER_CASE_MAP[buffer[11]] + LOWER_CASE_MAP[buffer[12]] + LOWER_CASE_MAP[buffer[13]] + LOWER_CASE_MAP[buffer[14]] + LOWER_CASE_MAP[buffer[15]];
}

const CHARCODEMAP = {};
const hexDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'A', 'B', 'C', 'D', 'E', 'F'].map(d => d.charCodeAt(0));

for (let i = 0; i < hexDigits.length; i++) {
  const map = CHARCODEMAP[hexDigits[i]] = {};

  for (let j = 0; j < hexDigits.length; j++) {
    const hex = String.fromCharCode(hexDigits[i], hexDigits[j]);
    const value = parseInt(hex, 16);
    map[hexDigits[j]] = value;
  }
}

function guidToArray(guid) {
  return [CHARCODEMAP[guid.charCodeAt(6)][guid.charCodeAt(7)], CHARCODEMAP[guid.charCodeAt(4)][guid.charCodeAt(5)], CHARCODEMAP[guid.charCodeAt(2)][guid.charCodeAt(3)], CHARCODEMAP[guid.charCodeAt(0)][guid.charCodeAt(1)], CHARCODEMAP[guid.charCodeAt(11)][guid.charCodeAt(12)], CHARCODEMAP[guid.charCodeAt(9)][guid.charCodeAt(10)], CHARCODEMAP[guid.charCodeAt(16)][guid.charCodeAt(17)], CHARCODEMAP[guid.charCodeAt(14)][guid.charCodeAt(15)], CHARCODEMAP[guid.charCodeAt(19)][guid.charCodeAt(20)], CHARCODEMAP[guid.charCodeAt(21)][guid.charCodeAt(22)], CHARCODEMAP[guid.charCodeAt(24)][guid.charCodeAt(25)], CHARCODEMAP[guid.charCodeAt(26)][guid.charCodeAt(27)], CHARCODEMAP[guid.charCodeAt(28)][guid.charCodeAt(29)], CHARCODEMAP[guid.charCodeAt(30)][guid.charCodeAt(31)], CHARCODEMAP[guid.charCodeAt(32)][guid.charCodeAt(33)], CHARCODEMAP[guid.charCodeAt(34)][guid.charCodeAt(35)]];
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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