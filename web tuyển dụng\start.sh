#!/bin/bash
echo "Starting JobFinder System..."

# Start backend
cd backend
npm install
npm run dev &

# Wait for backend to start
sleep 5

# Check backend connection
node test-connection.js
if [ $? -ne 0 ]; then
    echo "Failed to connect to backend server"
    exit 1
fi

# Return to root directory
cd ..

# Open frontend in default browser
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open index.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open index.html
fi

echo "System started successfully!"
echo "Backend running at http://localhost:5000"
echo "Frontend opened in default browser"