{"author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> <<EMAIL>> (https://github.com/bretcope)", "<PERSON> <<EMAIL>> (https://github.com/rossipedia)", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "last<PERSON>ky", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON>"], "name": "tedious", "description": "A TDS driver, for connecting to MS SQLServer databases.", "keywords": ["sql", "database", "mssql", "sqlserver", "sql-server", "tds", "msnodesql", "azure"], "homepage": "https://github.com/tediousjs/tedious", "bugs": "https://github.com/tediousjs/tedious/issues", "license": "MIT", "version": "15.1.3", "main": "./lib/tedious.js", "repository": {"type": "git", "url": "https://github.com/tediousjs/tedious.git"}, "engines": {"node": ">=14"}, "publishConfig": {"tag": "next"}, "dependencies": {"@azure/identity": "^2.0.4", "@azure/keyvault-keys": "^4.4.0", "@js-joda/core": "^5.2.0", "bl": "^5.0.0", "es-aggregate-error": "^1.0.8", "iconv-lite": "^0.6.3", "js-md4": "^0.3.2", "jsbi": "^4.3.0", "native-duplexpair": "^1.0.0", "node-abort-controller": "^3.0.1", "punycode": "^2.1.0", "sprintf-js": "^1.1.2"}, "devDependencies": {"@babel/cli": "^7.17.10", "@babel/core": "^7.17.10", "@babel/node": "^7.17.10", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/preset-env": "^7.17.10", "@babel/preset-typescript": "^7.16.7", "@babel/register": "^7.17.7", "@commitlint/cli": "^16.2.4", "@commitlint/config-conventional": "^16.2.4", "@commitlint/travis-cli": "^16.2.4", "@types/es-aggregate-error": "^1.0.2", "@types/async": "^3.2.13", "@types/bl": "^5.0.2", "@types/chai": "^4.3.1", "@types/depd": "^1.1.32", "@types/lru-cache": "^5.1.0", "@types/mocha": "^9.1.1", "@types/node": "^12.20.50", "@types/sprintf-js": "^1.1.2", "@typescript-eslint/eslint-plugin": "^5.22.0", "@typescript-eslint/parser": "^5.22.0", "async": "^3.2.3", "babel-plugin-istanbul": "^6.1.1", "chai": "^4.3.6", "codecov": "^3.8.3", "eslint": "^7.32.0", "mitm": "^1.7.2", "mocha": "^9.2.2", "nyc": "^15.1.0", "rimraf": "^3.0.2", "semantic-release": "^19.0.3", "sinon": "^11.1.2", "typedoc": "^0.22.15", "typescript": "^4.6.4"}, "scripts": {"docs": "typedoc", "lint": "eslint src test --ext .js,.ts && tsc", "test": "mocha test/unit test/unit/token test/unit/tracking-buffer", "test-integration": "mocha test/integration/", "test-all": "mocha test/unit/ test/unit/token/ test/unit/tracking-buffer test/integration/", "build": "rimraf lib && babel src --out-dir lib --extensions .js,.ts", "prepublish": "npm run build", "semantic-release": "semantic-release"}, "babel": {"sourceMaps": "both", "ignore": ["./src/**/*.d.ts"], "presets": [["@babel/preset-env", {"targets": {"node": 14}}], ["@babel/preset-typescript", {"allowDeclareFields": true}]], "plugins": [["@babel/transform-typescript", {"allowDeclareFields": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-proposal-private-methods", {"loose": true}], ["@babel/plugin-proposal-private-property-in-object", {"loose": true}]]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-max-line-length": [1, "always", 100], "footer-max-line-length": [1, "always", 100], "header-max-length": [1, "always", 100]}}, "mocha": {"require": "test/setup.js", "timeout": 5000, "extension": ["js", "ts"]}, "nyc": {"sourceMap": false, "instrument": false, "extension": [".ts"]}}