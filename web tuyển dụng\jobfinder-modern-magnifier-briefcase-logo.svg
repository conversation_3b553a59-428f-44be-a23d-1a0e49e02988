<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="10" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="10" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Grid Pattern -->
  <g opacity="0.1">
    <line x1="10" y1="30" x2="90" y2="30" stroke="white" stroke-width="0.5"/>
    <line x1="10" y1="50" x2="90" y2="50" stroke="white" stroke-width="0.5"/>
    <line x1="10" y1="70" x2="90" y2="70" stroke="white" stroke-width="0.5"/>
    <line x1="30" y1="10" x2="30" y2="90" stroke="white" stroke-width="0.5"/>
    <line x1="50" y1="10" x2="50" y2="90" stroke="white" stroke-width="0.5"/>
    <line x1="70" y1="10" x2="70" y2="90" stroke="white" stroke-width="0.5"/>
  </g>
  
  <!-- Magnifying Glass -->
  <g transform="translate(15, 25)">
    <!-- Glass Circle -->
    <circle cx="20" cy="20" r="18" fill="none" stroke="white" stroke-width="3.5"/>
    
    <!-- Glass Handle -->
    <rect x="33" y="33" width="25" height="7" rx="3.5" fill="white" transform="rotate(45, 33, 33)"/>
    
    <!-- Glass Interior -->
    <circle cx="20" cy="20" r="14" fill="url(#accentGradient)" opacity="0.15"/>
    
    <!-- Glass Reflection -->
    <path d="M10,15 Q20,25 30,15" stroke="white" stroke-width="1.5" fill="none" opacity="0.7"/>
  </g>
  
  <!-- Briefcase -->
  <g transform="translate(55, 30)">
    <!-- Briefcase Body -->
    <rect x="-15" y="10" width="30" height="25" rx="4" fill="white"/>
    
    <!-- Briefcase Top -->
    <rect x="-10" y="0" width="20" height="10" rx="3" fill="white"/>
    
    <!-- Briefcase Handle -->
    <rect x="-3" y="5" width="6" height="3" rx="1.5" fill="#065f46"/>
    
    <!-- Briefcase Lock -->
    <rect x="-3" y="17" width="6" height="4" rx="1" fill="url(#accentGradient)"/>
    
    <!-- Briefcase Details -->
    <line x1="-10" y1="25" x2="10" y2="25" stroke="#065f46" stroke-width="0.5" opacity="0.5"/>
    <line x1="-10" y1="30" x2="10" y2="30" stroke="#065f46" stroke-width="0.5" opacity="0.5"/>
  </g>
  
  <!-- Decorative Elements -->
  <rect x="10" y="10" width="80" height="5" fill="url(#accentGradient)" opacity="0.8"/>
  <rect x="10" y="85" width="80" height="5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Reflective Highlight -->
  <path d="M15,15 L85,15 L85,25 Q50,35 15,25 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
</svg>
