import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


df = pd.read_csv("StudentsPerformance.csv")
df


df.info()

df .describe()

sns.histplot(df['math score'], kde=True)
plt.title("Distribution of Math Scores")
plt.show()


df['average_score'] = (df['math score'] + df['reading score'] + df['writing score']) / 3
df['pass'] = df['average_score'].apply(lambda x: 1 if x >= 50 else 0)


# Tính xác suất học sinh pass (pass = 1) và fail (pass = 0)
pass_prob = df['pass'].value_counts(normalize=True)
print("Probability pass/fail:")
print(pass_prob)


# Xác suất học sinh pass nếu đã tham gia khóa học luyện thi
prep_pass_prob = df[df['test preparation course'] == 'completed']['pass'].value_counts(normalize=True)
print("Pass probability if the test preparation course was completed:")
print(prep_pass_prob)


no_prep_pass_prob = df[df['test preparation course'] == 'none']['pass'].value_counts(normalize=True)
print("Pass probability if the test preparation course was NOT completed:")
print(no_prep_pass_prob)


sns.barplot(x='gender', y='pass', data=df)
plt.title("Pass Rate by Gender")
plt.ylabel("Pass Probability")
plt.show()


sns.barplot(x='test preparation course', y='pass', data=df)
plt.title("Pass Rate by Test Preparation Course Status")
plt.ylabel("Pass Probability")
plt.show()
