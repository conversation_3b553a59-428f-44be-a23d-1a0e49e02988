<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.6" />
    </linearGradient>
    <filter id="logoShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="10" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="10" fill="url(#logoGradient)" filter="url(#logoShadow)"/>
  
  <!-- Decorative Elements -->
  <circle cx="75" cy="25" r="12" fill="#10b981" opacity="0.3"/>
  <circle cx="25" cy="75" r="15" fill="#10b981" opacity="0.2"/>
  
  <!-- Briefcase Icon -->
  <g transform="translate(30, 30) scale(0.9)">
    <path d="M40,20H35V15c0-2.8-2.2-5-5-5H20c-2.8,0-5,2.2-5,5v5H10c-2.8,0-5,2.2-5,5v25c0,2.8,2.2,5,5,5h30c2.8,0,5-2.2,5-5V25C45,22.2,42.8,20,40,20z M20,15h10v5H20V15z M40,50H10V35h10v5c0,1.1,0.9,2,2,2h6c1.1,0,2-0.9,2-2v-5h10V50z M30,35H20v-5h10V35z M40,30H10v-5h30V30z" fill="white"/>
  </g>
  
  <!-- Magnifying Glass -->
  <circle cx="25" cy="25" r="6" fill="none" stroke="white" stroke-width="2.5"/>
  <line x1="29" y1="29" x2="33" y2="33" stroke="white" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Document Icon -->
  <g transform="translate(60, 20)">
    <rect x="0" y="0" width="12" height="16" rx="2" fill="white" opacity="0.9"/>
    <line x1="3" y1="4" x2="9" y2="4" stroke="#065f46" stroke-width="1.2"/>
    <line x1="3" y1="8" x2="9" y2="8" stroke="#065f46" stroke-width="1.2"/>
    <line x1="3" y1="12" x2="7" y2="12" stroke="#065f46" stroke-width="1.2"/>
  </g>
  
  <!-- Reflective Glass Effect -->
  <path d="M10,10 L90,10 L90,40 Q50,60 10,40 Z" fill="url(#glassGradient)" opacity="0.1" clip-path="url(#roundedRect)"/>
  
  <!-- Decorative Dots -->
  <circle cx="15" cy="15" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="85" cy="85" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="15" cy="85" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="85" cy="15" r="1.5" fill="white" opacity="0.7"/>
</svg>
