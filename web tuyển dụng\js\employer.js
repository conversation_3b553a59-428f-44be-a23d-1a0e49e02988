// Employer JavaScript file

// API URL
const API_URL = 'http://localhost:5000/api';

// DOM Elements
const postJobForm = document.getElementById('post-job-form');
const findProfilesForm = document.getElementById('find-profiles-form');
const profileSearchInput = document.getElementById('profile-search-input');
const profileFilters = document.querySelectorAll('.profile-filter');
const profileResults = document.getElementById('profile-results');
const jobPostingsList = document.getElementById('job-postings-list');
const applicationsList = document.getElementById('applications-list');
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');

// Post a job
async function postJob(jobData) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token) {
            alert('Vui lòng đăng nhập để đăng tin tuyển dụng');
            return false;
        }
        
        if (user.role !== 'employer') {
            alert('Chỉ nhà tuyển dụng mới có thể đăng tin tuyển dụng');
            return false;
        }
        
        const response = await fetch(`${API_URL}/jobs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(jobData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Đăng tin tuyển dụng thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Đăng tin tuyển dụng thất bại');
        }
    } catch (error) {
        console.error('Error posting job:', error);
        alert(error.message);
        return false;
    }
}

// Get employer's job postings
async function getJobPostings() {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            return [];
        }
        
        const response = await fetch(`${API_URL}/jobs/employer`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch job postings');
        }
    } catch (error) {
        console.error('Error fetching job postings:', error);
        return [];
    }
}

// Get job applications
async function getJobApplications() {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            return [];
        }
        
        const response = await fetch(`${API_URL}/jobs/applications`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch job applications');
        }
    } catch (error) {
        console.error('Error fetching job applications:', error);
        return [];
    }
}

// Search for profiles
async function searchProfiles(filters = {}) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            return [];
        }
        
        // Build query string from filters
        const queryParams = new URLSearchParams();
        
        if (filters.search) {
            queryParams.append('search', filters.search);
        }
        
        if (filters.skills) {
            queryParams.append('skills', filters.skills);
        }
        
        if (filters.experience) {
            queryParams.append('experience', filters.experience);
        }
        
        if (filters.education) {
            queryParams.append('education', filters.education);
        }
        
        const queryString = queryParams.toString();
        const url = queryString ? `${API_URL}/profiles/search?${queryString}` : `${API_URL}/profiles/search`;
        
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to search profiles');
        }
    } catch (error) {
        console.error('Error searching profiles:', error);
        return [];
    }
}

// Get profile by ID
async function getProfileById(profileId) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            return null;
        }
        
        const response = await fetch(`${API_URL}/profiles/${profileId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch profile details');
        }
    } catch (error) {
        console.error('Error fetching profile details:', error);
        return null;
    }
}

// Update job status
async function updateJobStatus(jobId, status) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            alert('Chỉ nhà tuyển dụng mới có thể cập nhật trạng thái công việc');
            return false;
        }
        
        const response = await fetch(`${API_URL}/jobs/${jobId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ status })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Cập nhật trạng thái công việc thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Cập nhật trạng thái công việc thất bại');
        }
    } catch (error) {
        console.error('Error updating job status:', error);
        alert(error.message);
        return false;
    }
}

// Update application status
async function updateApplicationStatus(applicationId, status) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            alert('Chỉ nhà tuyển dụng mới có thể cập nhật trạng thái ứng tuyển');
            return false;
        }
        
        const response = await fetch(`${API_URL}/jobs/applications/${applicationId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ status })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Cập nhật trạng thái ứng tuyển thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Cập nhật trạng thái ứng tuyển thất bại');
        }
    } catch (error) {
        console.error('Error updating application status:', error);
        alert(error.message);
        return false;
    }
}

// Delete job posting
async function deleteJobPosting(jobId) {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            alert('Chỉ nhà tuyển dụng mới có thể xóa tin tuyển dụng');
            return false;
        }
        
        const confirmed = confirm('Bạn có chắc chắn muốn xóa tin tuyển dụng này?');
        if (!confirmed) {
            return false;
        }
        
        const response = await fetch(`${API_URL}/jobs/${jobId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Xóa tin tuyển dụng thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Xóa tin tuyển dụng thất bại');
        }
    } catch (error) {
        console.error('Error deleting job posting:', error);
        alert(error.message);
        return false;
    }
}

// Render job postings
function renderJobPostings(jobs) {
    if (!jobPostingsList) return;
    
    // Clear job postings list
    jobPostingsList.innerHTML = '';
    
    if (jobs.length === 0) {
        jobPostingsList.innerHTML = '<div class="no-jobs">Bạn chưa đăng tin tuyển dụng nào</div>';
        return;
    }
    
    // Create job posting items
    jobs.forEach(job => {
        const jobItem = document.createElement('div');
        jobItem.className = 'job-posting-item';
        
        // Format deadline
        const deadline = new Date(job.deadline);
        const formattedDeadline = deadline.toLocaleDateString('vi-VN');
        
        // Determine status class
        let statusClass = '';
        switch (job.status) {
            case 'active':
                statusClass = 'status-active';
                break;
            case 'expired':
                statusClass = 'status-expired';
                break;
            case 'draft':
                statusClass = 'status-draft';
                break;
        }
        
        jobItem.innerHTML = `
            <div class="job-posting-info">
                <h4>${job.title}</h4>
                <p>Địa điểm: ${job.location}</p>
                <p>Hạn nộp hồ sơ: ${formattedDeadline}</p>
                <p>Số lượng ứng tuyển: ${job.applications.length}</p>
            </div>
            <div class="job-posting-status">
                <span class="status-badge ${statusClass}">${job.status === 'active' ? 'Đang hiển thị' : job.status === 'expired' ? 'Hết hạn' : 'Bản nháp'}</span>
            </div>
            <div class="job-posting-actions">
                <button class="job-posting-edit" data-job-id="${job._id}">Chỉnh sửa</button>
                <button class="job-posting-view" data-job-id="${job._id}">Xem chi tiết</button>
                <button class="job-posting-delete" data-job-id="${job._id}">Xóa</button>
            </div>
        `;
        
        // Add event listeners
        const editBtn = jobItem.querySelector('.job-posting-edit');
        editBtn.addEventListener('click', () => {
            // Redirect to edit job page
            window.location.href = `/edit-job.html?id=${job._id}`;
        });
        
        const viewBtn = jobItem.querySelector('.job-posting-view');
        viewBtn.addEventListener('click', () => {
            // Redirect to job detail page
            window.location.href = `/job-detail.html?id=${job._id}`;
        });
        
        const deleteBtn = jobItem.querySelector('.job-posting-delete');
        deleteBtn.addEventListener('click', async () => {
            const success = await deleteJobPosting(job._id);
            if (success) {
                // Refresh job postings list
                const jobs = await getJobPostings();
                renderJobPostings(jobs);
            }
        });
        
        jobPostingsList.appendChild(jobItem);
    });
}

// Render job applications
function renderJobApplications(applications) {
    if (!applicationsList) return;
    
    // Clear applications list
    applicationsList.innerHTML = '';
    
    if (applications.length === 0) {
        applicationsList.innerHTML = '<div class="no-applications">Chưa có ứng viên nào ứng tuyển</div>';
        return;
    }
    
    // Create application items
    applications.forEach(application => {
        const applicationItem = document.createElement('div');
        applicationItem.className = 'application-item';
        
        // Format date
        const date = new Date(application.createdAt);
        const formattedDate = date.toLocaleDateString('vi-VN');
        
        applicationItem.innerHTML = `
            <div class="application-candidate">
                <img src="${application.candidate.avatar || 'images/avatar-placeholder.png'}" alt="${application.candidate.name}">
                <div class="application-candidate-info">
                    <h4>${application.candidate.name}</h4>
                    <p>${application.candidate.email}</p>
                </div>
            </div>
            <div class="application-job">
                <h4>${application.job.title}</h4>
                <p>${application.job.company.name}</p>
            </div>
            <div class="application-date">${formattedDate}</div>
            <div class="application-actions">
                <button class="application-view" data-application-id="${application._id}">Xem hồ sơ</button>
                <button class="application-approve" data-application-id="${application._id}">Duyệt</button>
                <button class="application-reject" data-application-id="${application._id}">Từ chối</button>
            </div>
        `;
        
        // Add event listeners
        const viewBtn = applicationItem.querySelector('.application-view');
        viewBtn.addEventListener('click', () => {
            // Redirect to CV detail page
            window.location.href = `/cv-detail.html?id=${application.cv._id}`;
        });
        
        const approveBtn = applicationItem.querySelector('.application-approve');
        approveBtn.addEventListener('click', async () => {
            const success = await updateApplicationStatus(application._id, 'approved');
            if (success) {
                // Refresh applications list
                const applications = await getJobApplications();
                renderJobApplications(applications);
            }
        });
        
        const rejectBtn = applicationItem.querySelector('.application-reject');
        rejectBtn.addEventListener('click', async () => {
            const success = await updateApplicationStatus(application._id, 'rejected');
            if (success) {
                // Refresh applications list
                const applications = await getJobApplications();
                renderJobApplications(applications);
            }
        });
        
        applicationsList.appendChild(applicationItem);
    });
}

// Render profile search results
function renderProfileResults(profiles) {
    if (!profileResults) return;
    
    // Clear profile results
    profileResults.innerHTML = '';
    
    if (profiles.length === 0) {
        profileResults.innerHTML = '<div class="no-profiles">Không tìm thấy hồ sơ phù hợp</div>';
        return;
    }
    
    // Create profile items
    profiles.forEach(profile => {
        const profileItem = document.createElement('div');
        profileItem.className = 'profile-item';
        
        // Create skills HTML
        const skillsHTML = profile.skills.map(skill => `<span>${skill}</span>`).join('');
        
        profileItem.innerHTML = `
            <h4>${profile.name}</h4>
            <p>${profile.title}</p>
            <p>Kinh nghiệm: ${profile.experience} năm</p>
            <p>Học vấn: ${profile.education}</p>
            <div class="skills">
                ${skillsHTML}
            </div>
            <button class="view-btn" data-profile-id="${profile._id}">Xem chi tiết</button>
        `;
        
        // Add event listener to view button
        const viewBtn = profileItem.querySelector('.view-btn');
        viewBtn.addEventListener('click', () => {
            // Redirect to profile detail page
            window.location.href = `/profile-detail.html?id=${profile._id}`;
        });
        
        profileResults.appendChild(profileItem);
    });
}

// Initialize employer page
async function initEmployerPage() {
    try {
        // Check if user is logged in and is an employer
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user'));
        
        if (!token || user.role !== 'employer') {
            alert('Chỉ nhà tuyển dụng mới có thể truy cập trang này');
            window.location.href = '/';
            return;
        }
        
        // Initialize tabs
        initTabs();
        
        // Get job postings and applications
        const jobs = await getJobPostings();
        const applications = await getJobApplications();
        
        // Render job postings and applications
        renderJobPostings(jobs);
        renderJobApplications(applications);
        
        // Initialize profile search
        initProfileSearch();
    } catch (error) {
        console.error('Error initializing employer page:', error);
    }
}

// Initialize tabs
function initTabs() {
    if (tabButtons.length === 0 || tabContents.length === 0) return;
    
    // Hide all tab contents
    tabContents.forEach(content => {
        content.style.display = 'none';
    });
    
    // Show first tab content
    if (tabContents[0]) {
        tabContents[0].style.display = 'block';
    }
    
    // Add click event to tab buttons
    tabButtons.forEach((button, index) => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            button.classList.add('active');
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.style.display = 'none';
            });
            
            // Show corresponding tab content
            if (tabContents[index]) {
                tabContents[index].style.display = 'block';
            }
        });
    });
    
    // Set first tab as active
    if (tabButtons[0]) {
        tabButtons[0].classList.add('active');
    }
}

// Initialize profile search
function initProfileSearch() {
    // Profile search form submission
    if (findProfilesForm) {
        findProfilesForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const filters = {
                search: profileSearchInput.value
            };
            
            // Add filter values
            profileFilters.forEach(filter => {
                if (filter.value) {
                    filters[filter.name] = filter.value;
                }
            });
            
            const profiles = await searchProfiles(filters);
            renderProfileResults(profiles);
        });
    }
}

// Post job form submission
if (postJobForm) {
    postJobForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const title = document.getElementById('job-title').value;
        const description = document.getElementById('job-description').value;
        const requirements = document.getElementById('job-requirements').value;
        const benefits = document.getElementById('job-benefits').value;
        const location = document.getElementById('job-location').value;
        const type = document.getElementById('job-type').value;
        const level = document.getElementById('job-level').value;
        const experience = document.getElementById('job-experience').value;
        const salaryMin = document.getElementById('job-salary-min').value;
        const salaryMax = document.getElementById('job-salary-max').value;
        const salaryHidden = document.getElementById('job-salary-hidden').checked;
        const deadline = document.getElementById('job-deadline').value;
        const skillsString = document.getElementById('job-skills').value;
        const isHot = document.getElementById('job-hot').checked;
        
        // Convert skills string to array
        const skills = skillsString.split(',').map(skill => skill.trim()).filter(skill => skill);
        
        const jobData = {
            title,
            description,
            requirements,
            benefits,
            location,
            type,
            level,
            experience,
            salaryMin: parseInt(salaryMin),
            salaryMax: parseInt(salaryMax),
            salaryHidden,
            deadline,
            skills,
            isHot
        };
        
        const success = await postJob(jobData);
        
        if (success) {
            // Clear form
            postJobForm.reset();
            
            // Switch to job postings tab
            if (tabButtons[1]) {
                tabButtons[1].click();
            }
            
            // Refresh job postings list
            const jobs = await getJobPostings();
            renderJobPostings(jobs);
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Check if we're on the employer page
    if (tabButtons.length > 0 && tabContents.length > 0) {
        initEmployerPage();
    }
});
