<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="12" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="12" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Decorative Elements -->
  <circle cx="75" cy="25" r="12" fill="#10b981" opacity="0.2"/>
  <circle cx="25" cy="75" r="15" fill="#10b981" opacity="0.1"/>
  
  <!-- Main Icon: Stylized J and F letters with magnifying glass and briefcase -->
  <g transform="translate(25, 25)">
    <!-- Magnifying Glass -->
    <circle cx="15" cy="15" r="12" fill="none" stroke="white" stroke-width="3"/>
    <line x1="24" y1="24" x2="30" y2="30" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Briefcase -->
    <rect x="35" y="15" width="20" height="15" rx="2" fill="white"/>
    <rect x="40" y="10" width="10" height="5" rx="1" fill="white"/>
    <rect x="44" y="15" width="2" height="15" fill="url(#bgGradient)"/>
    
    <!-- Document -->
    <rect x="38" y="20" width="14" height="2" rx="1" fill="url(#bgGradient)" opacity="0.7"/>
    <rect x="38" y="24" width="14" height="2" rx="1" fill="url(#bgGradient)" opacity="0.7"/>
    
    <!-- Glass Reflection -->
    <path d="M10,12 Q15,18 20,12" stroke="white" stroke-width="1" fill="none" opacity="0.7"/>
    
    <!-- Connecting Line -->
    <path d="M27,20 L35,20" stroke="white" stroke-width="1.5" stroke-dasharray="2,2"/>
  </g>
  
  <!-- Bottom Accent -->
  <rect x="10" y="85" width="80" height="5" rx="2.5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Top Accent -->
  <rect x="10" y="10" width="80" height="5" rx="2.5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Reflective Highlight -->
  <path d="M10,10 L90,10 L90,30 Q50,40 10,30 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
  
  <!-- Decorative Dots -->
  <circle cx="20" cy="20" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="80" cy="80" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="20" cy="80" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="80" cy="20" r="1.5" fill="white" opacity="0.7"/>
</svg>
