"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _writableTrackingBuffer = _interopRequireDefault(require("./tracking-buffer/writable-tracking-buffer"));

var crypto = _interopRequireWildcard(require("crypto"));

var _jsMd = _interopRequireDefault(require("js-md4"));

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

class NTLMResponsePayload {
  constructor(loginData) {
    this.data = void 0;
    this.data = this.createResponse(loginData);
  }

  toString(indent = '') {
    return indent + 'NTLM Auth';
  }

  createResponse(challenge) {
    const client_nonce = this.createClientNonce();
    const lmv2len = 24;
    const ntlmv2len = 16;
    const domain = challenge.domain;
    const username = challenge.userName;
    const password = challenge.password;
    const ntlmData = challenge.ntlmpacket;
    const server_data = ntlmData.target;
    const server_nonce = ntlmData.nonce;
    const bufferLength = 64 + domain.length * 2 + username.length * 2 + lmv2len + ntlmv2len + 8 + 8 + 8 + 4 + server_data.length + 4;
    const data = new _writableTrackingBuffer.default(bufferLength);
    data.position = 0;
    data.writeString('NTLMSSP\u0000', 'utf8');
    data.writeUInt32LE(0x03);
    const baseIdx = 64;
    const dnIdx = baseIdx;
    const unIdx = dnIdx + domain.length * 2;
    const l2Idx = unIdx + username.length * 2;
    const ntIdx = l2Idx + lmv2len;
    data.writeUInt16LE(lmv2len);
    data.writeUInt16LE(lmv2len);
    data.writeUInt32LE(l2Idx);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt16LE(ntlmv2len);
    data.writeUInt32LE(ntIdx);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt16LE(domain.length * 2);
    data.writeUInt32LE(dnIdx);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt16LE(username.length * 2);
    data.writeUInt32LE(unIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0);
    data.writeUInt16LE(0);
    data.writeUInt32LE(baseIdx);
    data.writeUInt16LE(0x8201);
    data.writeUInt16LE(0x08);
    data.writeString(domain, 'ucs2');
    data.writeString(username, 'ucs2');
    const lmv2Data = this.lmv2Response(domain, username, password, server_nonce, client_nonce);
    data.copyFrom(lmv2Data);
    const genTime = new Date().getTime();
    const ntlmDataBuffer = this.ntlmv2Response(domain, username, password, server_nonce, server_data, client_nonce, genTime);
    data.copyFrom(ntlmDataBuffer);
    data.writeUInt32LE(0x0101);
    data.writeUInt32LE(0x0000);
    const timestamp = this.createTimestamp(genTime);
    data.copyFrom(timestamp);
    data.copyFrom(client_nonce);
    data.writeUInt32LE(0x0000);
    data.copyFrom(server_data);
    data.writeUInt32LE(0x0000);
    return data.data;
  }

  createClientNonce() {
    const client_nonce = Buffer.alloc(8, 0);
    let nidx = 0;

    while (nidx < 8) {
      client_nonce.writeUInt8(Math.ceil(Math.random() * 255), nidx);
      nidx++;
    }

    return client_nonce;
  }

  ntlmv2Response(domain, user, password, serverNonce, targetInfo, clientNonce, mytime) {
    const timestamp = this.createTimestamp(mytime);
    const hash = this.ntv2Hash(domain, user, password);
    const dataLength = 40 + targetInfo.length;
    const data = Buffer.alloc(dataLength, 0);
    serverNonce.copy(data, 0, 0, 8);
    data.writeUInt32LE(0x101, 8);
    data.writeUInt32LE(0x0, 12);
    timestamp.copy(data, 16, 0, 8);
    clientNonce.copy(data, 24, 0, 8);
    data.writeUInt32LE(0x0, 32);
    targetInfo.copy(data, 36, 0, targetInfo.length);
    data.writeUInt32LE(0x0, 36 + targetInfo.length);
    return this.hmacMD5(data, hash);
  }

  createTimestamp(time) {
    const tenthsOfAMicrosecond = (BigInt(time) + BigInt(11644473600)) * BigInt(10000000);
    const lo = Number(tenthsOfAMicrosecond & BigInt(0xffffffff));
    const hi = Number(tenthsOfAMicrosecond >> BigInt(32) & BigInt(0xffffffff));
    const result = Buffer.alloc(8);
    result.writeUInt32LE(lo, 0);
    result.writeUInt32LE(hi, 4);
    return result;
  }

  lmv2Response(domain, user, password, serverNonce, clientNonce) {
    const hash = this.ntv2Hash(domain, user, password);
    const data = Buffer.alloc(serverNonce.length + clientNonce.length, 0);
    serverNonce.copy(data);
    clientNonce.copy(data, serverNonce.length, 0, clientNonce.length);
    const newhash = this.hmacMD5(data, hash);
    const response = Buffer.alloc(newhash.length + clientNonce.length, 0);
    newhash.copy(response);
    clientNonce.copy(response, newhash.length, 0, clientNonce.length);
    return response;
  }

  ntv2Hash(domain, user, password) {
    const hash = this.ntHash(password);
    const identity = Buffer.from(user.toUpperCase() + domain.toUpperCase(), 'ucs2');
    return this.hmacMD5(identity, hash);
  }

  ntHash(text) {
    const unicodeString = Buffer.from(text, 'ucs2');
    return Buffer.from(_jsMd.default.arrayBuffer(unicodeString));
  }

  hmacMD5(data, key) {
    return crypto.createHmac('MD5', key).update(data).digest();
  }

}

var _default = NTLMResponsePayload;
exports.default = _default;
module.exports = NTLMResponsePayload;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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