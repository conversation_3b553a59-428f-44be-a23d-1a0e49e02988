<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> sơ & CV - JobFinder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/job-styles.css">
    <link rel="stylesheet" href="../css/profile-cv.css">
</head>
<body>
    <!-- Header section would go here -->

    <section class="profile-cv-section">
        <div class="profile-cv-container">
            <div class="profile-cv-header">
                <h2><PERSON><PERSON> sơ & CV</h2>
                <p>Tạ<PERSON> và quản lý hồ sơ cá nhân, CV chuyên nghiệp để tăng cơ hội được nhà tuyển dụng chú ý</p>
            </div>

            <div class="profile-cv-actions">
                <button class="action-button">
                    <i class="fas fa-file-alt"></i>
                    TẠO HỒ SƠ MỚI
                </button>
                <button class="action-button">
                    <i class="fas fa-file-pdf"></i>
                    TẠO CV MỚI
                </button>
            </div>

            <div class="search-container">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Tìm theo tên, kỹ năng...">
                    <button class="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div class="filter-container">
                <button class="filter-button">
                    <span>Tất cả kỹ năng</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <button class="filter-button">
                    <span>Tất cả kinh nghiệm</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <button class="filter-button">
                    <span>Tất cả địa điểm</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>

            <div class="profile-cv-content">
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <h3>Bạn chưa có hồ sơ hoặc CV nào</h3>
                    <p>Tạo hồ sơ và CV chuyên nghiệp để tăng cơ hội được nhà tuyển dụng chú ý</p>
                    <button class="start-button">
                        <i class="fas fa-plus"></i>
                        Bắt đầu ngay
                    </button>
                </div>
            </div>

            <!-- CV Templates Section -->
            <div class="cv-templates-section">
                <h2 class="section-title">Mẫu CV Tham Khảo</h2>

                <div class="templates-filter">
                    <button class="template-filter-btn active">Tất cả</button>
                    <button class="template-filter-btn">CNTT - Phần mềm</button>
                    <button class="template-filter-btn">Kỹ thuật</button>
                    <button class="template-filter-btn">Marketing</button>
                    <button class="template-filter-btn">Kinh doanh</button>
                    <button class="template-filter-btn">Thiết kế</button>
                </div>

                <div class="templates-grid">
                    <!-- Template 1 - Pro 1 v2 -->
                    <div class="template-card">
                        <span class="template-badge pro">PRO</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png" alt="CV Pro 1 v2">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Pro 1 v2</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                CNTT - Phần mềm
                            </div>
                        </div>
                    </div>

                    <!-- Template 2 - Ambitious -->
                    <div class="template-card">
                        <span class="template-badge free">MIỄN PHÍ</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/ambitious.png" alt="CV Ambitious">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Ambitious</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                CNTT - Phần mềm
                            </div>
                        </div>
                    </div>

                    <!-- Template 3 -->
                    <div class="template-card">
                        <span class="template-badge free">MIỄN PHÍ</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/basic_5.png" alt="CV Basic 5">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Basic 5</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                Đa ngành nghề
                            </div>
                        </div>
                    </div>

                    <!-- Template 4 -->
                    <div class="template-card">
                        <span class="template-badge pro">PRO</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/professional_6.png" alt="CV Professional 6">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Professional 6</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                Kỹ thuật
                            </div>
                        </div>
                    </div>

                    <!-- Template 5 -->
                    <div class="template-card">
                        <span class="template-badge free">MIỄN PHÍ</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/simple_6.png" alt="CV Simple 6">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Simple 6</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                Marketing
                            </div>
                        </div>
                    </div>

                    <!-- Template 6 -->
                    <div class="template-card">
                        <span class="template-badge pro">PRO</span>
                        <div class="template-image">
                            <img src="https://www.topcv.vn/images/cv/screenshots/creative_4.png" alt="CV Creative 4">
                            <div class="template-overlay">
                                <div class="template-actions">
                                    <button class="template-btn preview-btn">
                                        <i class="fas fa-eye"></i>
                                        Xem trước
                                    </button>
                                    <button class="template-btn use-btn">
                                        <i class="fas fa-plus"></i>
                                        Sử dụng
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="template-info">
                            <div class="template-name">Creative 4</div>
                            <div class="template-category">
                                <i class="fas fa-tag"></i>
                                Thiết kế
                            </div>
                        </div>
                    </div>
                </div>

                <button class="view-more-btn">Xem thêm mẫu CV</button>
            </div>
        </div>
    </section>

    <!-- Footer section would go here -->

    <script>
        // JavaScript for interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Filter buttons toggle
            const filterButtons = document.querySelectorAll('.filter-button');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Toggle active state
                    this.classList.toggle('active');

                    // Here you would normally show a dropdown
                    // For demo purposes, we'll just toggle the icon rotation
                    const icon = this.querySelector('i');
                    if (this.classList.contains('active')) {
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        icon.style.transform = 'rotate(0)';
                    }
                });
            });

            // Action buttons
            const actionButtons = document.querySelectorAll('.action-button, .start-button, .template-btn, .view-more-btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Add a temporary active class for feedback
                    this.classList.add('button-clicked');

                    // Remove the class after animation completes
                    setTimeout(() => {
                        this.classList.remove('button-clicked');
                    }, 300);

                    // Here you would normally navigate to create form
                    console.log('Button clicked:', this.textContent.trim());
                });
            });

            // Template filter buttons
            const templateFilterButtons = document.querySelectorAll('.template-filter-btn');
            templateFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    templateFilterButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get selected category
                    const category = this.textContent.trim();

                    // Filter templates
                    filterTemplates(category);
                });
            });

            // Function to filter templates
            function filterTemplates(category) {
                const templates = document.querySelectorAll('.template-card');

                templates.forEach(template => {
                    const templateCategory = template.querySelector('.template-category').textContent.trim();

                    if (category === 'Tất cả' || templateCategory.includes(category)) {
                        template.style.display = 'block';
                    } else {
                        template.style.display = 'none';
                    }
                });
            }

            // Preview buttons
            const previewButtons = document.querySelectorAll('.preview-btn');
            previewButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const templateCard = this.closest('.template-card');
                    const templateName = templateCard.querySelector('.template-name').textContent;
                    const templateImage = templateCard.querySelector('.template-image img').src;

                    // Here you would normally open a preview modal
                    console.log('Preview template:', templateName, templateImage);
                    alert(`Đang xem trước mẫu CV: ${templateName}`);
                });
            });

            // Use template buttons
            const useButtons = document.querySelectorAll('.use-btn');
            useButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const templateCard = this.closest('.template-card');
                    const templateName = templateCard.querySelector('.template-name').textContent;

                    // Here you would normally redirect to CV editor with selected template
                    console.log('Use template:', templateName);
                    alert(`Đã chọn mẫu CV: ${templateName}. Bạn sẽ được chuyển đến trang tạo CV.`);
                });
            });
        });
    </script>
</body>
</html>
