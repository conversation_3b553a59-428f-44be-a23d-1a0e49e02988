/* Job Search Page Styles */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --primary-light: #60a5fa;
    --secondary-color: #f59e0b;
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #6b7280;
    --bg-light: #f9fafb;
    --bg-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --transition: all 0.3s ease;
}

body {
    background-color: var(--bg-light);
    color: var(--text-dark);
    font-family: 'Inter', 'Segoe UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.search-page {
    padding: 2rem 0;
}

.search-page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.search-page-header p {
    font-size: 1rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Main Search Bar */
.main-search {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.search-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-input-wrapper i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1.25rem;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    color: var(--text-dark);
    background-color: var(--bg-white);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 120px;
}

.search-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.search-button:active {
    transform: translateY(0);
}

/* Basic Filters */
.basic-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-select-group {
    flex: 1;
    min-width: 180px;
}

.filter-select {
    position: relative;
    width: 100%;
}

.filter-select select {
    width: 100%;
    padding: 0.75rem 1rem;
    appearance: none;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-white);
    color: var(--text-dark);
    font-size: 0.9375rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--text-medium);
    pointer-events: none;
}

.reset-button {
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.75rem 1.25rem;
    color: var(--text-medium);
    font-size: 0.9375rem;
    cursor: pointer;
    transition: var(--transition);
}

.reset-button:hover {
    background-color: var(--bg-light);
    color: var(--text-dark);
}

/* Advanced Filters */
.advanced-filters {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.advanced-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.advanced-filters-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
}

.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

.filter-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.filter-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.75rem;
}

/* Custom Checkbox */
.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-item label {
    position: relative;
    padding-left: 1.75rem;
    cursor: pointer;
    font-size: 0.9375rem;
    color: var(--text-medium);
    user-select: none;
    display: inline-block;
}

.checkbox-item label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.125rem;
    width: 1.125rem;
    height: 1.125rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-white);
    transition: var(--transition);
}

.checkbox-item input[type="checkbox"]:checked + label:before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-item label:after {
    content: '';
    position: absolute;
    left: 0.375rem;
    top: 0.375rem;
    width: 0.375rem;
    height: 0.625rem;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: var(--transition);
}

.checkbox-item input[type="checkbox"]:checked + label:after {
    opacity: 1;
}

.checkbox-item input[type="checkbox"]:focus + label:before {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-item:hover label:before {
    border-color: var(--primary-color);
}

/* Filter Actions */
.filter-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.filter-button {
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-md);
    font-size: 0.9375rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.apply-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.apply-button:hover {
    background-color: var(--primary-dark);
}

.clear-button {
    background-color: transparent;
    color: var(--text-medium);
    border: 1px solid var(--border-color);
}

.clear-button:hover {
    background-color: var(--bg-light);
    color: var(--text-dark);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .search-input-group {
        flex-direction: column;
    }
    
    .search-button {
        width: 100%;
    }
    
    .basic-filters {
        flex-direction: column;
    }
    
    .filter-select-group {
        width: 100%;
    }
    
    .filter-options {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 576px) {
    .filter-options {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        flex-direction: column;
    }
    
    .filter-button {
        width: 100%;
    }
}
