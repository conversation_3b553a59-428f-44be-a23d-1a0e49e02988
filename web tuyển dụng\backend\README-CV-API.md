# API Hồ sơ & CV - JobFinder

API quản lý hồ sơ và CV cho ứng dụng JobFinder.

## Cài đặt

1. <PERSON><PERSON><PERSON> đặt các gói phụ thuộc:

```bash
npm install
```

2. <PERSON><PERSON><PERSON> hình database trong file `.env`

3. Thiết lập cơ sở dữ liệu:

```bash
npm run setup-db
```

4. Chạy server:

```bash
npm run dev
```

## API Endpoints

### <PERSON><PERSON> sơ người dùng

#### L<PERSON>y thông tin hồ sơ người dùng

```
GET /api/profiles
```

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "profile": {
    "id": 1,
    "userId": 1,
    "title": "Senior Developer",
    "fullName": "Nguyễn Văn <PERSON>",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "address": "<PERSON><PERSON>, Việt Nam",
    "dateOfBirth": "1990-01-01T00:00:00.000Z",
    "gender": "Nam",
    "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
    "skills": "JavaScript, React, Node.js",
    "experience": "...",
    "education": "...",
    "certifications": "...",
    "languages": "Tiếng Việt, Tiếng Anh",
    "avatarPath": "/uploads/avatars/avatar-123456.jpg",
    "isPublic": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Cập nhật hồ sơ người dùng

```
PUT /api/profiles/update
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Body:**
```json
{
  "title": "Senior Developer",
  "fullName": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phone": "0123456789",
  "address": "Hà Nội, Việt Nam",
  "dateOfBirth": "1990-01-01",
  "gender": "Nam",
  "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
  "skills": "JavaScript, React, Node.js",
  "experience": "...",
  "education": "...",
  "certifications": "...",
  "languages": "Tiếng Việt, Tiếng Anh",
  "isPublic": true
}
```

**Response:**
```json
{
  "profile": {
    "id": 1,
    "userId": 1,
    "title": "Senior Developer",
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "address": "Hà Nội, Việt Nam",
    "dateOfBirth": "1990-01-01T00:00:00.000Z",
    "gender": "Nam",
    "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
    "skills": "JavaScript, React, Node.js",
    "experience": "...",
    "education": "...",
    "certifications": "...",
    "languages": "Tiếng Việt, Tiếng Anh",
    "avatarPath": "/uploads/avatars/avatar-123456.jpg",
    "isPublic": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Tải lên ảnh đại diện

```
POST /api/profiles/avatar
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**Body:**
- avatar: [File]

**Response:**
```json
{
  "profile": {
    "id": 1,
    "userId": 1,
    "title": "Senior Developer",
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phone": "0123456789",
    "address": "Hà Nội, Việt Nam",
    "dateOfBirth": "1990-01-01T00:00:00.000Z",
    "gender": "Nam",
    "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
    "skills": "JavaScript, React, Node.js",
    "experience": "...",
    "education": "...",
    "certifications": "...",
    "languages": "Tiếng Việt, Tiếng Anh",
    "avatarPath": "/uploads/avatars/avatar-123456.jpg",
    "isPublic": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### CV

#### Lấy danh sách CV của người dùng

```
GET /api/cvs
```

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "cvs": [
    {
      "id": 1,
      "userId": 1,
      "profileId": 1,
      "templateId": 1,
      "title": "CV IT Developer",
      "content": "...",
      "cvPath": "/uploads/cvs/cv-123456.pdf",
      "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
      "isPrimary": true,
      "isPublic": false,
      "templateName": "Pro 1 v2",
      "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
      "category": "CNTT - Phần mềm",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Lấy thông tin chi tiết CV

```
GET /api/cvs/:id
```

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "cv": {
    "id": 1,
    "userId": 1,
    "profileId": 1,
    "templateId": 1,
    "title": "CV IT Developer",
    "content": "...",
    "cvPath": "/uploads/cvs/cv-123456.pdf",
    "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
    "isPrimary": true,
    "isPublic": false,
    "templateName": "Pro 1 v2",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "category": "CNTT - Phần mềm",
    "htmlTemplate": "...",
    "cssTemplate": "...",
    "profile": {
      "fullName": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Hà Nội, Việt Nam",
      "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
      "skills": "JavaScript, React, Node.js",
      "experience": "...",
      "education": "...",
      "certifications": "...",
      "languages": "Tiếng Việt, Tiếng Anh"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Tạo CV mới

```
POST /api/cvs
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Body:**
```json
{
  "profileId": 1,
  "templateId": 1,
  "title": "CV IT Developer",
  "content": "...",
  "isPrimary": true,
  "isPublic": false
}
```

**Response:**
```json
{
  "cv": {
    "id": 1,
    "userId": 1,
    "profileId": 1,
    "templateId": 1,
    "title": "CV IT Developer",
    "content": "...",
    "cvPath": null,
    "thumbnailPath": null,
    "isPrimary": true,
    "isPublic": false,
    "templateName": "Pro 1 v2",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "category": "CNTT - Phần mềm",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Cập nhật CV

```
PUT /api/cvs/:id
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Body:**
```json
{
  "profileId": 1,
  "title": "CV IT Developer Updated",
  "content": "...",
  "isPrimary": true,
  "isPublic": true
}
```

**Response:**
```json
{
  "cv": {
    "id": 1,
    "userId": 1,
    "profileId": 1,
    "templateId": 1,
    "title": "CV IT Developer Updated",
    "content": "...",
    "cvPath": "/uploads/cvs/cv-123456.pdf",
    "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
    "isPrimary": true,
    "isPublic": true,
    "templateName": "Pro 1 v2",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "category": "CNTT - Phần mềm",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Xóa CV

```
DELETE /api/cvs/:id
```

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "message": "CV đã được xóa thành công"
}
```

#### Tìm kiếm CV

```
GET /api/cvs/search?keyword=developer
```

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "cvs": [
    {
      "id": 1,
      "userId": 1,
      "profileId": 1,
      "templateId": 1,
      "title": "CV IT Developer",
      "content": "...",
      "cvPath": "/uploads/cvs/cv-123456.pdf",
      "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
      "isPrimary": true,
      "isPublic": false,
      "templateName": "Pro 1 v2",
      "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
      "category": "CNTT - Phần mềm",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Tải lên file CV

```
POST /api/cvs/:id/upload
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**Body:**
- cv: [File]

**Response:**
```json
{
  "cv": {
    "id": 1,
    "userId": 1,
    "profileId": 1,
    "templateId": 1,
    "title": "CV IT Developer",
    "content": "...",
    "cvPath": "/uploads/cvs/cv-123456.pdf",
    "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
    "isPrimary": true,
    "isPublic": false,
    "templateName": "Pro 1 v2",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "category": "CNTT - Phần mềm",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Tạo liên kết chia sẻ CV

```
POST /api/cvs/:id/share
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Body:**
```json
{
  "expiryDays": 7,
  "isPasswordProtected": true,
  "password": "password123"
}
```

**Response:**
```json
{
  "shareLink": {
    "id": 1,
    "userId": 1,
    "cvId": 1,
    "shareToken": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6",
    "expiryDate": "2023-01-08T00:00:00.000Z",
    "isPasswordProtected": true,
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Lấy CV từ liên kết chia sẻ

```
GET /api/cvs/shared/:token?password=password123
```

**Response:**
```json
{
  "cv": {
    "id": 1,
    "userId": 1,
    "profileId": 1,
    "templateId": 1,
    "title": "CV IT Developer",
    "content": "...",
    "cvPath": "/uploads/cvs/cv-123456.pdf",
    "thumbnailPath": "/uploads/cvs/cv-123456-thumb.jpg",
    "isPrimary": true,
    "isPublic": false,
    "templateName": "Pro 1 v2",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "category": "CNTT - Phần mềm",
    "htmlTemplate": "...",
    "cssTemplate": "...",
    "profile": {
      "fullName": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Hà Nội, Việt Nam",
      "introduction": "Tôi là một lập trình viên với hơn 5 năm kinh nghiệm...",
      "skills": "JavaScript, React, Node.js",
      "experience": "...",
      "education": "...",
      "certifications": "...",
      "languages": "Tiếng Việt, Tiếng Anh"
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Ghi lại lịch sử tải xuống CV

```
POST /api/cvs/:id/download
```

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Body:**
```json
{
  "format": "PDF"
}
```

**Response:**
```json
{
  "message": "Đã ghi lại lịch sử tải xuống"
}
```

### Mẫu CV

#### Lấy danh sách mẫu CV

```
GET /api/cv-templates?categoryId=1&premium=true
```

**Response:**
```json
{
  "templates": [
    {
      "id": 1,
      "name": "Pro 1 v2",
      "description": "Mẫu CV chuyên nghiệp với thiết kế hiện đại, phù hợp với các vị trí CNTT",
      "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
      "htmlTemplate": "...",
      "cssTemplate": "...",
      "category": "CNTT - Phần mềm",
      "categoryName": "CNTT - Phần mềm",
      "isPremium": true,
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Lấy thông tin chi tiết mẫu CV

```
GET /api/cv-templates/:id
```

**Response:**
```json
{
  "template": {
    "id": 1,
    "name": "Pro 1 v2",
    "description": "Mẫu CV chuyên nghiệp với thiết kế hiện đại, phù hợp với các vị trí CNTT",
    "previewImage": "https://www.topcv.vn/images/cv/screenshots/pro_1_v2.png",
    "htmlTemplate": "...",
    "cssTemplate": "...",
    "category": "CNTT - Phần mềm",
    "categoryName": "CNTT - Phần mềm",
    "isPremium": true,
    "isActive": true,
    "fields": [
      {
        "id": 1,
        "templateId": 1,
        "fieldName": "full_name",
        "fieldLabel": "Họ và tên",
        "fieldType": "text",
        "fieldOrder": 1,
        "isRequired": true,
        "defaultValue": null,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

#### Lấy danh sách danh mục mẫu CV

```
GET /api/cv-templates/categories/all
```

**Response:**
```json
{
  "categories": [
    {
      "id": 1,
      "name": "CNTT - Phần mềm",
      "description": "Mẫu CV dành cho các vị trí trong lĩnh vực CNTT và phát triển phần mềm",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```
