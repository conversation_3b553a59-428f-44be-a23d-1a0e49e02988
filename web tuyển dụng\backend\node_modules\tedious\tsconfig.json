{
  "compilerOptions": {
    "target": "esnext",
    "moduleResolution": "node",
    "allowJs": true,
    "noEmit": true,
    "strict": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "lib": [ "esnext" ],
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "exactOptionalPropertyTypes": true
  },

  "include": [
    "types/*.d.ts",
    "src/**/*.ts",
    "test/**/*.ts",
    "test/**/*.js",
  ],

  "typedocOptions": {
    "name": "Ted<PERSON>",
    "out": "./docs",
    "entryPoints": ["./src/tedious.ts"]
  }
}
