{"version": 3, "file": "ServerTelemetryManager.js", "sources": ["../../../src/telemetry/server/ServerTelemetryManager.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { SERVER_TELEM_CONSTANTS, Separators, CacheOutcome, Constants, RegionDiscoverySources, RegionDiscoveryOutcomes } from \"../../utils/Constants\";\r\nimport { CacheManager } from \"../../cache/CacheManager\";\r\nimport { AuthError } from \"../../error/AuthError\";\r\nimport { ServerTelemetryRequest } from \"./ServerTelemetryRequest\";\r\nimport { ServerTelemetryEntity } from \"../../cache/entities/ServerTelemetryEntity\";\r\nimport { StringUtils } from \"../../utils/StringUtils\";\r\nimport { RegionDiscoveryMetadata } from \"../../authority/RegionDiscoveryMetadata\";\r\n\r\nexport class ServerTelemetryManager {\r\n    private cacheManager: CacheManager;\r\n    private apiId: number;\r\n    private correlationId: string;\r\n    private telemetryCacheKey: string;\r\n    private wrapperSKU: String;\r\n    private wrapperVer: String;\r\n    private regionUsed: string | undefined;\r\n    private regionSource: RegionDiscoverySources | undefined;\r\n    private regionOutcome: RegionDiscoveryOutcomes | undefined;\r\n    private cacheOutcome: CacheOutcome = CacheOutcome.NO_CACHE_HIT;\r\n\r\n    constructor(telemetryRequest: ServerTelemetryRequest, cacheManager: CacheManager) {\r\n        this.cacheManager = cacheManager;\r\n        this.apiId = telemetryRequest.apiId;\r\n        this.correlationId = telemetryRequest.correlationId;\r\n        this.wrapperSKU = telemetryRequest.wrapperSKU || Constants.EMPTY_STRING;\r\n        this.wrapperVer = telemetryRequest.wrapperVer || Constants.EMPTY_STRING;\r\n\r\n        this.telemetryCacheKey = SERVER_TELEM_CONSTANTS.CACHE_KEY + Separators.CACHE_KEY_SEPARATOR + telemetryRequest.clientId;\r\n    }\r\n\r\n    /**\r\n     * API to add MSER Telemetry to request\r\n     */\r\n    generateCurrentRequestHeaderValue(): string {\r\n        const request = `${this.apiId}${SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR}${this.cacheOutcome}`;\r\n        const platformFields = [this.wrapperSKU, this.wrapperVer].join(SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR);\r\n        const regionDiscoveryFields = this.getRegionDiscoveryFields();\r\n        const requestWithRegionDiscoveryFields = [request, regionDiscoveryFields].join(SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR);\r\n\r\n        return [SERVER_TELEM_CONSTANTS.SCHEMA_VERSION, requestWithRegionDiscoveryFields, platformFields].join(SERVER_TELEM_CONSTANTS.CATEGORY_SEPARATOR);\r\n    }\r\n\r\n    /**\r\n     * API to add MSER Telemetry for the last failed request\r\n     */\r\n    generateLastRequestHeaderValue(): string {\r\n        const lastRequests = this.getLastRequests();\r\n\r\n        const maxErrors = ServerTelemetryManager.maxErrorsToSend(lastRequests);\r\n        const failedRequests = lastRequests.failedRequests.slice(0, 2*maxErrors).join(SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR);\r\n        const errors = lastRequests.errors.slice(0, maxErrors).join(SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR);\r\n        const errorCount = lastRequests.errors.length;\r\n\r\n        // Indicate whether this header contains all data or partial data\r\n        const overflow = maxErrors < errorCount ? SERVER_TELEM_CONSTANTS.OVERFLOW_TRUE : SERVER_TELEM_CONSTANTS.OVERFLOW_FALSE;\r\n        const platformFields = [errorCount, overflow].join(SERVER_TELEM_CONSTANTS.VALUE_SEPARATOR);\r\n\r\n        return [SERVER_TELEM_CONSTANTS.SCHEMA_VERSION, lastRequests.cacheHits, failedRequests, errors, platformFields].join(SERVER_TELEM_CONSTANTS.CATEGORY_SEPARATOR);\r\n    }\r\n\r\n    /**\r\n     * API to cache token failures for MSER data capture\r\n     * @param error\r\n     */\r\n    cacheFailedRequest(error: AuthError): void {\r\n        const lastRequests = this.getLastRequests();\r\n        if (lastRequests.errors.length >= SERVER_TELEM_CONSTANTS.MAX_CACHED_ERRORS) {\r\n            // Remove a cached error to make room, first in first out\r\n            lastRequests.failedRequests.shift(); // apiId\r\n            lastRequests.failedRequests.shift(); // correlationId\r\n            lastRequests.errors.shift();\r\n        }\r\n        \r\n        lastRequests.failedRequests.push(this.apiId, this.correlationId);\r\n\r\n        if (!StringUtils.isEmpty(error.subError)) {\r\n            lastRequests.errors.push(error.subError);\r\n        } else if (!StringUtils.isEmpty(error.errorCode)) {\r\n            lastRequests.errors.push(error.errorCode);\r\n        } else if (!!error && error.toString()) {\r\n            lastRequests.errors.push(error.toString());\r\n        } else {\r\n            lastRequests.errors.push(SERVER_TELEM_CONSTANTS.UNKNOWN_ERROR);\r\n        }\r\n\r\n        this.cacheManager.setServerTelemetry(this.telemetryCacheKey, lastRequests);\r\n\r\n        return;\r\n    }\r\n\r\n    /**\r\n     * Update server telemetry cache entry by incrementing cache hit counter\r\n     */\r\n    incrementCacheHits(): number {\r\n        const lastRequests = this.getLastRequests();\r\n        lastRequests.cacheHits += 1;\r\n\r\n        this.cacheManager.setServerTelemetry(this.telemetryCacheKey, lastRequests);\r\n        return lastRequests.cacheHits;\r\n    }\r\n\r\n    /**\r\n     * Get the server telemetry entity from cache or initialize a new one\r\n     */\r\n    getLastRequests(): ServerTelemetryEntity {\r\n        const initialValue: ServerTelemetryEntity = new ServerTelemetryEntity();\r\n        const lastRequests = this.cacheManager.getServerTelemetry(this.telemetryCacheKey) as ServerTelemetryEntity;\r\n\r\n        return lastRequests || initialValue;\r\n    }\r\n\r\n    /**\r\n     * Remove server telemetry cache entry\r\n     */\r\n    clearTelemetryCache(): void {\r\n        const lastRequests = this.getLastRequests();\r\n        const numErrorsFlushed = ServerTelemetryManager.maxErrorsToSend(lastRequests);\r\n        const errorCount = lastRequests.errors.length;\r\n        if (numErrorsFlushed === errorCount) {\r\n            // All errors were sent on last request, clear Telemetry cache\r\n            this.cacheManager.removeItem(this.telemetryCacheKey);\r\n        } else {\r\n            // Partial data was flushed to server, construct a new telemetry cache item with errors that were not flushed\r\n            const serverTelemEntity = new ServerTelemetryEntity();\r\n            serverTelemEntity.failedRequests = lastRequests.failedRequests.slice(numErrorsFlushed*2); // failedRequests contains 2 items for each error\r\n            serverTelemEntity.errors = lastRequests.errors.slice(numErrorsFlushed);\r\n\r\n            this.cacheManager.setServerTelemetry(this.telemetryCacheKey, serverTelemEntity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the maximum number of errors that can be flushed to the server in the next network request\r\n     * @param serverTelemetryEntity\r\n     */\r\n    static maxErrorsToSend(serverTelemetryEntity: ServerTelemetryEntity): number {\r\n        let i;\r\n        let maxErrors = 0;\r\n        let dataSize = 0;\r\n        const errorCount = serverTelemetryEntity.errors.length;\r\n        for (i = 0; i < errorCount; i++) {\r\n            // failedRequests parameter contains pairs of apiId and correlationId, multiply index by 2 to preserve pairs\r\n            const apiId = serverTelemetryEntity.failedRequests[2*i] || Constants.EMPTY_STRING;\r\n            const correlationId = serverTelemetryEntity.failedRequests[2*i + 1] || Constants.EMPTY_STRING;\r\n            const errorCode = serverTelemetryEntity.errors[i] || Constants.EMPTY_STRING;\r\n\r\n            // Count number of characters that would be added to header, each character is 1 byte. Add 3 at the end to account for separators\r\n            dataSize += apiId.toString().length + correlationId.toString().length + errorCode.length + 3;\r\n\r\n            if (dataSize < SERVER_TELEM_CONSTANTS.MAX_LAST_HEADER_BYTES) {\r\n                // Adding this entry to the header would still keep header size below the limit\r\n                maxErrors += 1;\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        return maxErrors;\r\n    }\r\n\r\n    /**\r\n     * Get the region discovery fields\r\n     * \r\n     * @returns string\r\n     */\r\n    getRegionDiscoveryFields(): string {\r\n        const regionDiscoveryFields: string[] = [];\r\n\r\n        regionDiscoveryFields.push(this.regionUsed || Constants.EMPTY_STRING);\r\n        regionDiscoveryFields.push(this.regionSource || Constants.EMPTY_STRING);\r\n        regionDiscoveryFields.push(this.regionOutcome || Constants.EMPTY_STRING);\r\n\r\n        return regionDiscoveryFields.join(\",\");\r\n    }\r\n\r\n    /**\r\n     * Update the region discovery metadata\r\n     * \r\n     * @param regionDiscoveryMetadata\r\n     * @returns void\r\n     */\r\n    updateRegionDiscoveryMetadata(regionDiscoveryMetadata: RegionDiscoveryMetadata): void {\r\n        this.regionUsed = regionDiscoveryMetadata.region_used;\r\n        this.regionSource = regionDiscoveryMetadata.region_source;\r\n        this.regionOutcome = regionDiscoveryMetadata.region_outcome;\r\n    }\r\n\r\n    /**\r\n     * Set cache outcome \r\n     */\r\n    setCacheOutcome(cacheOutcome: CacheOutcome): void {\r\n        this.cacheOutcome = cacheOutcome;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAUH,IAAA,sBAAA,kBAAA,YAAA;IAYI,SAAY,sBAAA,CAAA,gBAAwC,EAAE,YAA0B,EAAA;AAFxE,QAAA,IAAA,CAAA,YAAY,GAAiB,YAAY,CAAC,YAAY,CAAC;AAG3D,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACpC,QAAA,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC;QACxE,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC;AAExE,QAAA,IAAI,CAAC,iBAAiB,GAAG,sBAAsB,CAAC,SAAS,GAAG,UAAU,CAAC,mBAAmB,GAAG,gBAAgB,CAAC,QAAQ,CAAC;KAC1H;AAED;;AAEG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,iCAAiC,GAAjC,YAAA;AACI,QAAA,IAAM,OAAO,GAAG,EAAG,GAAA,IAAI,CAAC,KAAK,GAAG,sBAAsB,CAAC,eAAe,GAAG,IAAI,CAAC,YAAc,CAAC;AAC7F,QAAA,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AACvG,QAAA,IAAM,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAC9D,QAAA,IAAM,gCAAgC,GAAG,CAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AAEvH,QAAA,OAAO,CAAC,sBAAsB,CAAC,cAAc,EAAE,gCAAgC,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;KACpJ,CAAA;AAED;;AAEG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,8BAA8B,GAA9B,YAAA;AACI,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAE5C,IAAM,SAAS,GAAG,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QACvE,IAAM,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAC,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AACtH,QAAA,IAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AACpG,QAAA,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;;AAG9C,QAAA,IAAM,QAAQ,GAAG,SAAS,GAAG,UAAU,GAAG,sBAAsB,CAAC,aAAa,GAAG,sBAAsB,CAAC,cAAc,CAAC;AACvH,QAAA,IAAM,cAAc,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAE3F,OAAO,CAAC,sBAAsB,CAAC,cAAc,EAAE,YAAY,CAAC,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;KAClK,CAAA;AAED;;;AAGG;IACH,sBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,KAAgB,EAAA;AAC/B,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,sBAAsB,CAAC,iBAAiB,EAAE;;AAExE,YAAA,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACpC,YAAA,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACpC,YAAA,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAC/B,SAAA;AAED,QAAA,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACtC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC5C,SAAA;aAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YAC9C,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC7C,SAAA;aAAM,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE;YACpC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9C,SAAA;AAAM,aAAA;YACH,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAClE,SAAA;QAED,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAE3E,OAAO;KACV,CAAA;AAED;;AAEG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;AACI,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5C,QAAA,YAAY,CAAC,SAAS,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAC3E,OAAO,YAAY,CAAC,SAAS,CAAC;KACjC,CAAA;AAED;;AAEG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACI,QAAA,IAAM,YAAY,GAA0B,IAAI,qBAAqB,EAAE,CAAC;AACxE,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAA0B,CAAC;QAE3G,OAAO,YAAY,IAAI,YAAY,CAAC;KACvC,CAAA;AAED;;AAEG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,YAAA;AACI,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AAC9E,QAAA,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9C,IAAI,gBAAgB,KAAK,UAAU,EAAE;;YAEjC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACxD,SAAA;AAAM,aAAA;;AAEH,YAAA,IAAM,iBAAiB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AACtD,YAAA,iBAAiB,CAAC,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,gBAAgB,GAAC,CAAC,CAAC,CAAC;YACzF,iBAAiB,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAEvE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AACnF,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,sBAAe,CAAA,eAAA,GAAtB,UAAuB,qBAA4C,EAAA;AAC/D,QAAA,IAAI,CAAC,CAAC;QACN,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,QAAA,IAAM,UAAU,GAAG,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC;QACvD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;;AAE7B,YAAA,IAAM,KAAK,GAAG,qBAAqB,CAAC,cAAc,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AAClF,YAAA,IAAM,aAAa,GAAG,qBAAqB,CAAC,cAAc,CAAC,CAAC,GAAC,CAAC,GAAG,CAAC,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;AAC9F,YAAA,IAAM,SAAS,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,YAAY,CAAC;;YAG5E,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAE7F,YAAA,IAAI,QAAQ,GAAG,sBAAsB,CAAC,qBAAqB,EAAE;;gBAEzD,SAAS,IAAI,CAAC,CAAC;AAClB,aAAA;AAAM,iBAAA;gBACH,MAAM;AACT,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;;AAIG;AACH,IAAA,sBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,qBAAqB,GAAa,EAAE,CAAC;QAE3C,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QACtE,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QACxE,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;AAEzE,QAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1C,CAAA;AAED;;;;;AAKG;IACH,sBAA6B,CAAA,SAAA,CAAA,6BAAA,GAA7B,UAA8B,uBAAgD,EAAA;AAC1E,QAAA,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAC,WAAW,CAAC;AACtD,QAAA,IAAI,CAAC,YAAY,GAAG,uBAAuB,CAAC,aAAa,CAAC;AAC1D,QAAA,IAAI,CAAC,aAAa,GAAG,uBAAuB,CAAC,cAAc,CAAC;KAC/D,CAAA;AAED;;AAEG;IACH,sBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAA0B,EAAA;AACtC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KACpC,CAAA;IACL,OAAC,sBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}