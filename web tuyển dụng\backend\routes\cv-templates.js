const express = require('express');
const router = express.Router();
const CVTemplate = require('../models/CVTemplate');
const { auth, adminAuth } = require('../middleware/auth');

// @route   GET /api/cv-templates
// @desc    Get all CV templates
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { categoryId, premium } = req.query;
    
    // Convert premium to boolean if provided
    let isPremium = null;
    if (premium !== undefined) {
      isPremium = premium === 'true';
    }
    
    const templates = await CVTemplate.getAll(
      categoryId ? parseInt(categoryId) : null,
      isPremium
    );
    
    res.status(200).json({ templates });
  } catch (error) {
    console.error('Get templates error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh sách mẫu CV' });
  }
});

// @route   GET /api/cv-templates/:id
// @desc    Get CV template detail
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const templateId = parseInt(req.params.id);
    
    const template = await CVTemplate.getById(templateId);
    
    if (!template) {
      return res.status(404).json({ error: 'Không tìm thấy mẫu CV' });
    }
    
    res.status(200).json({ template });
  } catch (error) {
    console.error('Get template detail error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy thông tin mẫu CV' });
  }
});

// @route   GET /api/cv-templates/categories
// @desc    Get CV template categories
// @access  Public
router.get('/categories/all', async (req, res) => {
  try {
    const categories = await CVTemplate.getCategories();
    
    res.status(200).json({ categories });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh sách danh mục mẫu CV' });
  }
});

module.exports = router;
