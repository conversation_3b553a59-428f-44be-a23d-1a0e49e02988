"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _events = require("events");

var _errors = require("./errors");

var _types = require("./always-encrypted/types");

/**
 * ```js
 * const { Request } = require('tedious');
 * const request = new Request("select 42, 'hello world'", (err, rowCount) {
 *   // Request completion callback...
 * });
 * connection.execSql(request);
 * ```
 */
class Request extends _events.EventEmitter {
  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * @private
   */

  /**
   * This event, describing result set columns, will be emitted before row
   * events are emitted. This event may be emited multiple times when more
   * than one recordset is produced by the statement.
   *
   * An array like object, where the columns can be accessed either by index
   * or name. Columns with a name that is an integer are not accessible by name,
   * as it would be interpreted as an array index.
   */

  /**
   * The request has been prepared and can be used in subsequent calls to execute and unprepare.
   */

  /**
   * The request encountered an error and has not been prepared.
   */

  /**
   * A row resulting from execution of the SQL statement.
   */

  /**
   * All rows from a result set have been provided (through `row` events).
   *
   * This token is used to indicate the completion of a SQL statement.
   * As multiple SQL statements can be sent to the server in a single SQL batch, multiple `done` can be generated.
   * An `done` event is emited for each SQL statement in the SQL batch except variable declarations.
   * For execution of SQL statements within stored procedures, `doneProc` and `doneInProc` events are used in place of `done`.
   *
   * If you are using [[Connection.execSql]] then SQL server may treat the multiple calls with the same query as a stored procedure.
   * When this occurs, the `doneProc` and `doneInProc` events may be emitted instead. You must handle both events to ensure complete coverage.
   */

  /**
   * `request.on('doneInProc', function (rowCount, more, rows) { });`
   *
   * Indicates the completion status of a SQL statement within a stored procedure. All rows from a statement
   * in a stored procedure have been provided (through `row` events).
   *
   * This event may also occur when executing multiple calls with the same query using [[execSql]].
   */

  /**
   * Indicates the completion status of a stored procedure. This is also generated for stored procedures
   * executed through SQL statements.\
   * This event may also occur when executing multiple calls with the same query using [[execSql]].
   */

  /**
   * A value for an output parameter (that was added to the request with [[addOutputParameter]]).
   * See also `Using Parameters`.
   */

  /**
   * This event gives the columns by which data is ordered, if `ORDER BY` clause is executed in SQL Server.
   */
  on(event, listener) {
    return super.on(event, listener);
  }
  /**
   * @private
   */


  emit(event, ...args) {
    return super.emit(event, ...args);
  }
  /**
   * @param sqlTextOrProcedure
   *   The SQL statement to be executed
   *
   * @param callback
   *   The callback to execute once the request has been fully completed.
   */


  constructor(sqlTextOrProcedure, callback, options) {
    super();
    this.sqlTextOrProcedure = void 0;
    this.parameters = void 0;
    this.parametersByName = void 0;
    this.preparing = void 0;
    this.canceled = void 0;
    this.paused = void 0;
    this.userCallback = void 0;
    this.handle = void 0;
    this.error = void 0;
    this.connection = void 0;
    this.timeout = void 0;
    this.rows = void 0;
    this.rst = void 0;
    this.rowCount = void 0;
    this.callback = void 0;
    this.shouldHonorAE = void 0;
    this.statementColumnEncryptionSetting = void 0;
    this.cryptoMetadataLoaded = void 0;
    this.sqlTextOrProcedure = sqlTextOrProcedure;
    this.parameters = [];
    this.parametersByName = {};
    this.preparing = false;
    this.handle = undefined;
    this.canceled = false;
    this.paused = false;
    this.error = undefined;
    this.connection = undefined;
    this.timeout = undefined;
    this.userCallback = callback;
    this.statementColumnEncryptionSetting = options && options.statementColumnEncryptionSetting || _types.SQLServerStatementColumnEncryptionSetting.UseConnectionSetting;
    this.cryptoMetadataLoaded = false;

    this.callback = function (err, rowCount, rows) {
      if (this.preparing) {
        this.preparing = false;

        if (err) {
          this.emit('error', err);
        } else {
          this.emit('prepared');
        }
      } else {
        this.userCallback(err, rowCount, rows);
        this.emit('requestCompleted');
      }
    };
  }
  /**
   * @param name
   *   The parameter name. This should correspond to a parameter in the SQL,
   *   or a parameter that a called procedure expects. The name should not start with `@`.
   *
   * @param type
   *   One of the supported data types.
   *
   * @param value
   *   The value that the parameter is to be given. The Javascript type of the
   *   argument should match that documented for data types.
   *
   * @param options
   *   Additional type options. Optional.
   */
  // TODO: `type` must be a valid TDS value type


  addParameter(name, type, value, options) {
    const {
      output = false,
      length,
      precision,
      scale
    } = options ?? {};
    const parameter = {
      type: type,
      name: name,
      value: value,
      output: output,
      length: length,
      precision: precision,
      scale: scale
    };
    this.parameters.push(parameter);
    this.parametersByName[name] = parameter;
  }
  /**
   * @param name
   *   The parameter name. This should correspond to a parameter in the SQL,
   *   or a parameter that a called procedure expects.
   *
   * @param type
   *   One of the supported data types.
   *
   * @param value
   *   The value that the parameter is to be given. The Javascript type of the
   *   argument should match that documented for data types
   *
   * @param options
   *   Additional type options. Optional.
   */


  addOutputParameter(name, type, value, options) {
    this.addParameter(name, type, value, { ...options,
      output: true
    });
  }
  /**
   * @private
   */


  makeParamsParameter(parameters) {
    let paramsParameter = '';

    for (let i = 0, len = parameters.length; i < len; i++) {
      const parameter = parameters[i];

      if (paramsParameter.length > 0) {
        paramsParameter += ', ';
      }

      paramsParameter += '@' + parameter.name + ' ';
      paramsParameter += parameter.type.declaration(parameter);

      if (parameter.output) {
        paramsParameter += ' OUTPUT';
      }
    }

    return paramsParameter;
  }
  /**
   * @private
   */


  validateParameters(collation) {
    for (let i = 0, len = this.parameters.length; i < len; i++) {
      const parameter = this.parameters[i];

      try {
        parameter.value = parameter.type.validate(parameter.value, collation);
      } catch (error) {
        throw new _errors.RequestError('Validation failed for parameter \'' + parameter.name + '\'. ' + error.message, 'EPARAM');
      }
    }
  }
  /**
   * Temporarily suspends the flow of data from the database. No more `row` events will be emitted until [[resume] is called.
   * If this request is already in a paused state, calling [[pause]] has no effect.
   */


  pause() {
    if (this.paused) {
      return;
    }

    this.emit('pause');
    this.paused = true;
  }
  /**
   * Resumes the flow of data from the database.
   * If this request is not in a paused state, calling [[resume]] has no effect.
   */


  resume() {
    if (!this.paused) {
      return;
    }

    this.paused = false;
    this.emit('resume');
  }
  /**
   * Cancels a request while waiting for a server response.
   */


  cancel() {
    if (this.canceled) {
      return;
    }

    this.canceled = true;
    this.emit('cancel');
  }
  /**
   * Sets a timeout for this request.
   *
   * @param timeout
   *   The number of milliseconds before the request is considered failed,
   *   or `0` for no timeout. When no timeout is set for the request,
   *   the [[ConnectionOptions.requestTimeout]] of the [[Connection]] is used.
   */


  setTimeout(timeout) {
    this.timeout = timeout;
  }

}

var _default = Request;
exports.default = _default;
module.exports = Request;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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