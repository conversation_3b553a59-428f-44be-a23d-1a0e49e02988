<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.6" />
    </linearGradient>
    <filter id="logoShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="8" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="8" fill="url(#logoGradient)" filter="url(#logoShadow)"/>
  
  <!-- Decorative Elements -->
  <circle cx="75" cy="25" r="12" fill="#10b981" opacity="0.2"/>
  <circle cx="25" cy="75" r="15" fill="#10b981" opacity="0.1"/>
  
  <!-- Building/Skyscraper Icon -->
  <g transform="translate(30, 25) scale(0.9)">
    <rect x="0" y="0" width="10" height="50" rx="1" fill="white"/>
    <rect x="15" y="10" width="10" height="40" rx="1" fill="white"/>
    <rect x="30" y="5" width="10" height="45" rx="1" fill="white"/>
    <rect x="3" y="10" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="3" y="20" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="3" y="30" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="3" y="40" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="18" y="20" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="18" y="30" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="18" y="40" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="33" y="15" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="33" y="25" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="33" y="35" width="4" height="4" rx="1" fill="#064e3b"/>
    <rect x="33" y="45" width="4" height="4" rx="1" fill="#064e3b"/>
  </g>
  
  <!-- Magnifying Glass -->
  <circle cx="25" cy="25" r="6" fill="none" stroke="white" stroke-width="2.5"/>
  <line x1="29" y1="29" x2="33" y2="33" stroke="white" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Reflective Glass Effect -->
  <path d="M10,10 L90,10 L90,40 Q50,60 10,40 Z" fill="url(#glassGradient)" opacity="0.1" clip-path="url(#roundedRect)"/>
  
  <!-- Decorative Dots -->
  <circle cx="15" cy="15" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="85" cy="85" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="15" cy="85" r="1.5" fill="white" opacity="0.7"/>
  <circle cx="85" cy="15" r="1.5" fill="white" opacity="0.7"/>
  
  <!-- Bottom Accent Line -->
  <rect x="20" y="80" width="60" height="2" rx="1" fill="#34d399" opacity="0.8"/>
</svg>
