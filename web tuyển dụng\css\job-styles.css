/* Enhanced Job Listing Styles */
.featured-jobs {
    padding: 60px 0 40px;
    background-color: #f8fafc;
    border-radius: 0;
    margin: 0 0 60px 0; /* Tăng margin-bottom để tạo khoảng cách lớn hơn */
    box-shadow: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    background: linear-gradient(rgba(0, 128, 128, 0.92), rgba(0, 128, 128, 0.92)), url('job-interview-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.featured-jobs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Cpath d="M0 0h20L0 20z"%3E%3C/path%3E%3Cpath d="M20 0v20H0z"%3E%3C/path%3E%3C/g%3E%3C/svg%3E');
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: 0;
    animation: subtle-float 30s infinite alternate ease-in-out;
}

.featured-jobs::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    z-index: 0;
    animation: pulse 8s infinite alternate ease-in-out;
}

@keyframes subtle-float {
    0% {
        transform: translateY(0) scale(1);
    }
    100% {
        transform: translateY(-10px) scale(1.02);
    }
}

@keyframes pulse {
    0% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

@keyframes shine {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

@keyframes float-text {
    0% {
        transform: translateY(0px);
        text-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: translateY(-5px);
        text-shadow: 0 15px 25px rgba(0, 0, 0, 0.4);
    }
    100% {
        transform: translateY(0px);
        text-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
}

.featured-jobs .container {
    max-width: 1200px;
    position: relative;
    z-index: 1;
    padding: 0 20px;
}

.featured-jobs h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 15px;
    font-weight: 700;
    position: relative;
    padding-bottom: 15px;
    font-family: 'Poppins', sans-serif;
    letter-spacing: 1px;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.job-subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    margin-bottom: 35px;
    font-weight: 400;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.5px;
    line-height: 1.5;
    animation: fade-in 1s ease-out;
}

@keyframes fade-in {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.featured-jobs h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, rgba(186, 230, 253, 0.3), rgba(186, 230, 253, 0.9), rgba(186, 230, 253, 0.3));
    border-radius: 3px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: width-pulse 3s ease-in-out infinite;
}

@keyframes width-pulse {
    0% {
        width: 60px;
    }
    50% {
        width: 120px;
    }
    100% {
        width: 60px;
    }
}

.job-list {
    max-height: 650px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ffffff #e0e0e0;
    background-color: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.job-list::-webkit-scrollbar {
    width: 6px;
}

.job-list::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 6px;
}

.job-list::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.job-item {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 18px 20px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
}

.job-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #ffffff;
    background-color: rgba(255, 255, 255, 0.98);
}

.job-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.job-item img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    margin-right: 20px;
    border-radius: 8px;
    padding: 5px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.job-item:hover img {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.job-info {
    flex: 1;
}

.job-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
    transition: color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
}

.job-item:hover .job-info h3 {
    color: #0284c7;
}

.job-info .company {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.job-info .level {
    font-size: 11px;
    color: #999;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.job-info .level::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #008080;
    border-radius: 50%;
    margin-right: 6px;
}

.job-info .skills {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 5px;
}

.job-info .skills span {
    background-color: rgba(2, 132, 199, 0.1);
    color: #0284c7;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(2, 132, 199, 0.2);
}

.job-info .skills span:hover {
    background-color: #0284c7;
    color: white;
}

.job-info .details {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
    color: #00a550;
}

.job-info .details i {
    color: #00a550;
    margin-right: 5px;
}

.job-info .details .days-left {
    color: #00a550;
    font-weight: 500;
}

.job-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.job-card-salary {
    font-size: 15px;
    font-weight: 600;
    color: #00a550;
    white-space: nowrap;
}

.job-card-apply {
    background-color: #00a550;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-card-apply:hover {
    background-color: #008040;
}

.job-card-save {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-card-save:hover {
    background-color: #f5f5f5;
}

.job-card-save i {
    color: #ccc;
    font-size: 14px;
}

.job-card-save.saved i {
    color: #ff4d4d;
}



.job-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #0284c7;
    color: #fff;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(2, 132, 199, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .job-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-item img {
        margin-bottom: 10px;
    }

    .job-actions {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }
}
