{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# SALES ANALYSIS - IMPROVED VERSION"]}, {"cell_type": "markdown", "id": "load_data_title", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> và xử lý dữ liệu"]}, {"cell_type": "code", "execution_count": null, "id": "load_data", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from itertools import combinations\n", "import glob\n", "import os\n", "\n", "# Đọc file CSV\n", "path = '.'\n", "all_files = glob.glob(os.path.join(path, 'Sales_*.csv'))\n", "df = pd.concat([pd.read_csv(f) for f in all_files], ignore_index=True)\n", "df.dropna(how='any', inplace=True)\n", "df = df[~df['Order Date'].str.contains('Order Date', na=False)]\n", "\n", "# <PERSON><PERSON> lý dữ liệu\n", "df['Quantity Ordered'] = pd.to_numeric(df['Quantity Ordered'])\n", "df['Price Each'] = pd.to_numeric(df['Price Each'])\n", "df['Order Date'] = pd.to_datetime(df['Order Date'])\n", "df['Sales'] = df['Quantity Ordered'] * df['Price Each']\n", "df['Month'] = df['Order Date'].dt.month\n", "df['Hour'] = df['Order Date'].dt.hour\n", "df['City'] = df['Purchase Address'].str.split(',').str[1].str.strip()\n", "\n", "# <PERSON><PERSON><PERSON> thị thông tin tổng quan\n", "print(f'✅ Đã load {len(df):,} records từ {len(all_files)} files')\n", "print(f'💰 Tổng doanh thu: ${df[\"Sales\"].sum():,.0f}')\n", "print(f'📊 Dữ liệu từ {df[\"Order Date\"].min().strftime(\"%m/%Y\")} đến {df[\"Order Date\"].max().strftime(\"%m/%Y\")}')\n", "print(f'🏙️ {df[\"City\"].nunique()} thành phố, {df[\"Product\"].nunique()} sản phẩm')\n", "print('\\n📋 Sample data:')\n", "\n", "# <PERSON><PERSON><PERSON> thị DataFrame sample\n", "df.head()"]}, {"cell_type": "markdown", "id": "q1", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON><PERSON> nào có doanh số cao nhất?"]}, {"cell_type": "code", "execution_count": null, "id": "a1", "metadata": {}, "outputs": [], "source": ["monthly = df.groupby('Month')['Sales'].sum()\n", "print(f'Tháng {monthly.idxmax()}: ${monthly.max():,.0f}')\n", "plt.figure(figsize=(10, 6))\n", "monthly.plot(kind='bar', title='Doanh thu theo tháng', color='skyblue')\n", "plt.ylabel('<PERSON><PERSON><PERSON> thu (triệu USD)')\n", "plt.xlabel('Tháng')\n", "plt.xticks(rotation=0)\n", "ax = plt.gca()\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q2", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON> phố nào có doanh số cao nhất?"]}, {"cell_type": "code", "execution_count": null, "id": "a2", "metadata": {}, "outputs": [], "source": ["city = df.groupby('City')['Sales'].sum()\n", "print(f'{city.idxmax()}: ${city.max():,.0f}')\n", "plt.figure(figsize=(10, 6))\n", "city.nlargest(5).plot(kind='bar', title='Top 5 thành phố theo doanh thu')\n", "plt.ylabel('<PERSON><PERSON><PERSON> thu (triệu USD)')\n", "plt.xlabel('Thành phố')\n", "plt.xticks(rotation=45, ha='right')\n", "ax = plt.gca()\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q3", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON> g<PERSON><PERSON> nào có nhiều đơn hàng nhất?"]}, {"cell_type": "code", "execution_count": null, "id": "a3", "metadata": {}, "outputs": [], "source": ["hourly = df.groupby('Hour').size()\n", "print(f'{hourly.idxmax()}h: {hourly.max()} đơn hàng')\n", "plt.figure(figsize=(10, 6))\n", "hourly.plot(kind='line', marker='o', title='<PERSON>ố đơn hàng theo giờ trong ngày', color='orange')\n", "plt.ylabel('Số đơn hàng')\n", "plt.xlabel('<PERSON><PERSON><PERSON> (0-23h)')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q4", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> ph<PERSON>m nào bán chạy nhất?"]}, {"cell_type": "code", "execution_count": null, "id": "a4", "metadata": {}, "outputs": [], "source": ["product = df.groupby('Product')['Quantity Ordered'].sum()\n", "print(f'{product.idxmax()}: {product.max():,} sản phẩm')\n", "plt.figure(figsize=(12, 6))\n", "product.nlargest(5).plot(kind='barh', title='Top 5 sản phẩm bán chạy nhất', color='lightgreen')\n", "plt.xlabel('<PERSON><PERSON> bán (sản phẩm)')\n", "plt.ylabel('Sản phẩm')\n", "ax = plt.gca()\n", "ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q5", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> ph<PERSON>m nào thường đ<PERSON><PERSON><PERSON> bán cùng nhau?"]}, {"cell_type": "code", "execution_count": null, "id": "a5", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "pairs = [tuple(sorted(combo)) for products in df.groupby('Order ID')['Product'].apply(list) \n", "         for combo in combinations(products, 2) if len(products) > 1]\n", "top_pairs = Counter(pairs).most_common(5)\n", "print('Top 5 cặp sản phẩm thường mua cùng:')\n", "for i, (pair, count) in enumerate(top_pairs, 1):\n", "    print(f'{i}. {pair[0]} + {pair[1]}: {count} lần')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}