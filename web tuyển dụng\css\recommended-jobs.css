/* Recommended Jobs Styles */
.recommended-jobs {
    margin-bottom: 20px;
    overflow: hidden;
    background-color: #f0f9fa;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    height: auto;
    border: 1px solid rgba(10, 147, 150, 0.2);
}

.recommended-jobs-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.recommended-jobs-header h3 {
    margin: 0;
    font-size: 24px;
    color: #0a9396;
    font-weight: 700;
    text-shadow: 0 1px 1px rgba(0,0,0,0.1);
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recommended-jobs-header h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #0a9396, #94d2bd);
    border-radius: 2px;
}

.recommended-jobs-list {
    padding: 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-height: 600px;
    overflow-y: auto;
}

.recommended-jobs-list::-webkit-scrollbar {
    width: 6px;
}

.recommended-jobs-list::-webkit-scrollbar-thumb {
    background-color: #10b981;
    border-radius: 3px;
}

.recommended-jobs-list::-webkit-scrollbar-track {
    background-color: #e0e0e0;
    border-radius: 3px;
}

.recommended-job-item {
    padding: 10px;
    background-color: #fff;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 150px;
}

.recommended-job-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.recommended-job-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    display: block;
    text-decoration: none;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 32px;
}

.recommended-job-title:hover {
    color: #0284c7;
}

.recommended-job-company {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.recommended-job-company img {
    width: 16px;
    height: 16px;
    margin-right: 5px;
    object-fit: contain;
    border-radius: 2px;
    background-color: #fff;
}

.recommended-job-company span {
    font-size: 11px;
    color: #666;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.recommended-job-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin: 6px 0;
    min-height: 22px;
    max-height: 44px;
    overflow: hidden;
}

.recommended-job-tags span {
    background-color: #f0f9ff;
    color: #0284c7;
    padding: 1px 5px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 400;
    border: 1px solid rgba(2, 132, 199, 0.1);
}

.recommended-job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 6px;
    margin-top: auto;
    padding-top: 6px;
}

.recommended-job-location {
    display: flex;
    align-items: center;
    font-size: 10px;
    color: #00a550;
}

.recommended-job-location i {
    margin-right: 3px;
    color: #00a550;
    font-size: 10px;
}

.recommended-job-days {
    display: flex;
    align-items: center;
    font-size: 10px;
    color: #00a550;
}

.recommended-job-days i {
    margin-right: 3px;
    color: #00a550;
    font-size: 10px;
}

.recommended-job-salary {
    font-size: 10px;
    font-weight: 600;
    color: #00a550;
    padding: 1px 5px;
    border-radius: 3px;
    background-color: #f0fdf4;
    border: 1px solid rgba(0, 165, 80, 0.1);
}

.recommended-job-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    flex-wrap: wrap;
    gap: 5px;
}

.apply-btn {
    background-color: #00a550;
    color: white;
    border: none;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    cursor: pointer;
    font-size: 10px;
    transition: background-color 0.2s ease;
}

.apply-btn:hover {
    background-color: #008040;
}

.favorite {
    color: #d1d5db;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.favorite:hover, .favorite.active {
    color: #f43f5e;
}

.view-all-link {
    color: #0284c7;
    text-decoration: none;
    font-weight: 500;
    font-size: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.view-all-link:hover {
    text-decoration: underline;
    color: #0366a3;
}

.view-all-link i {
    margin-left: 5px;
    font-size: 10px;
}

/* Layout for jobs section with sidebar */
.jobs-container {
    display: flex;
    flex-direction: row-reverse;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.jobs-main {
    flex: 1;
}

.jobs-sidebar {
    width: 100%;
    padding-top: 20px;
}

@media (max-width: 1200px) {
    .jobs-container {
        max-width: 100%;
        padding: 0 15px;
    }

    .recommended-jobs-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .jobs-container {
        flex-direction: column;
    }

    .recommended-jobs-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .recommended-jobs-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .recommended-jobs-list {
        grid-template-columns: 1fr;
    }
}
