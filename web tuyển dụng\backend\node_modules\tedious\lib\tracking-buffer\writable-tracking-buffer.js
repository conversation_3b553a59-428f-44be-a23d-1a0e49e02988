"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
const SHIFT_LEFT_32 = (1 << 16) * (1 << 16);
const SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;
const UNKNOWN_PLP_LEN = Buffer.from([0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff]);
const ZERO_LENGTH_BUFFER = Buffer.alloc(0);

/**
  A Buffer-like class that tracks position.

  As values are written, the position advances by the size of the written data.
  When writing, automatically allocates new buffers if there's not enough space.
 */
class WritableTrackingBuffer {
  constructor(initialSize, encoding, doubleSizeGrowth) {
    this.initialSize = void 0;
    this.encoding = void 0;
    this.doubleSizeGrowth = void 0;
    this.buffer = void 0;
    this.compositeBuffer = void 0;
    this.position = void 0;
    this.initialSize = initialSize;
    this.encoding = encoding || 'ucs2';
    this.doubleSizeGrowth = doubleSizeGrowth || false;
    this.buffer = Buffer.alloc(this.initialSize, 0);
    this.compositeBuffer = ZERO_LENGTH_BUFFER;
    this.position = 0;
  }

  get data() {
    this.newBuffer(0);
    return this.compositeBuffer;
  }

  copyFrom(buffer) {
    const length = buffer.length;
    this.makeRoomFor(length);
    buffer.copy(this.buffer, this.position);
    this.position += length;
  }

  makeRoomFor(requiredLength) {
    if (this.buffer.length - this.position < requiredLength) {
      if (this.doubleSizeGrowth) {
        let size = Math.max(128, this.buffer.length * 2);

        while (size < requiredLength) {
          size *= 2;
        }

        this.newBuffer(size);
      } else {
        this.newBuffer(requiredLength);
      }
    }
  }

  newBuffer(size) {
    const buffer = this.buffer.slice(0, this.position);
    this.compositeBuffer = Buffer.concat([this.compositeBuffer, buffer]);
    this.buffer = size === 0 ? ZERO_LENGTH_BUFFER : Buffer.alloc(size, 0);
    this.position = 0;
  }

  writeUInt8(value) {
    const length = 1;
    this.makeRoomFor(length);
    this.buffer.writeUInt8(value, this.position);
    this.position += length;
  }

  writeUInt16LE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeUInt16LE(value, this.position);
    this.position += length;
  }

  writeUShort(value) {
    this.writeUInt16LE(value);
  }

  writeUInt16BE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeUInt16BE(value, this.position);
    this.position += length;
  }

  writeUInt24LE(value) {
    const length = 3;
    this.makeRoomFor(length);
    this.buffer[this.position + 2] = value >>> 16 & 0xff;
    this.buffer[this.position + 1] = value >>> 8 & 0xff;
    this.buffer[this.position] = value & 0xff;
    this.position += length;
  }

  writeUInt32LE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeUInt32LE(value, this.position);
    this.position += length;
  }

  writeBigInt64LE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeBigInt64LE(value, this.position);
    this.position += length;
  }

  writeInt64LE(value) {
    this.writeBigInt64LE(BigInt(value));
  }

  writeUInt64LE(value) {
    this.writeBigUInt64LE(BigInt(value));
  }

  writeBigUInt64LE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeBigUInt64LE(value, this.position);
    this.position += length;
  }

  writeUInt32BE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeUInt32BE(value, this.position);
    this.position += length;
  }

  writeUInt40LE(value) {
    // inspired by https://github.com/dpw/node-buffer-more-ints
    this.writeInt32LE(value & -1);
    this.writeUInt8(Math.floor(value * SHIFT_RIGHT_32));
  }

  writeInt8(value) {
    const length = 1;
    this.makeRoomFor(length);
    this.buffer.writeInt8(value, this.position);
    this.position += length;
  }

  writeInt16LE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeInt16LE(value, this.position);
    this.position += length;
  }

  writeInt16BE(value) {
    const length = 2;
    this.makeRoomFor(length);
    this.buffer.writeInt16BE(value, this.position);
    this.position += length;
  }

  writeInt32LE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeInt32LE(value, this.position);
    this.position += length;
  }

  writeInt32BE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeInt32BE(value, this.position);
    this.position += length;
  }

  writeFloatLE(value) {
    const length = 4;
    this.makeRoomFor(length);
    this.buffer.writeFloatLE(value, this.position);
    this.position += length;
  }

  writeDoubleLE(value) {
    const length = 8;
    this.makeRoomFor(length);
    this.buffer.writeDoubleLE(value, this.position);
    this.position += length;
  }

  writeString(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }

    const length = Buffer.byteLength(value, encoding);
    this.makeRoomFor(length); // $FlowFixMe https://github.com/facebook/flow/pull/5398

    this.buffer.write(value, this.position, encoding);
    this.position += length;
  }

  writeBVarchar(value, encoding) {
    this.writeUInt8(value.length);
    this.writeString(value, encoding);
  }

  writeUsVarchar(value, encoding) {
    this.writeUInt16LE(value.length);
    this.writeString(value, encoding);
  } // TODO: Figure out what types are passed in other than `Buffer`


  writeUsVarbyte(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }

    let length;

    if (value instanceof Buffer) {
      length = value.length;
    } else {
      value = value.toString();
      length = Buffer.byteLength(value, encoding);
    }

    this.writeUInt16LE(length);

    if (value instanceof Buffer) {
      this.writeBuffer(value);
    } else {
      this.makeRoomFor(length); // $FlowFixMe https://github.com/facebook/flow/pull/5398

      this.buffer.write(value, this.position, encoding);
      this.position += length;
    }
  }

  writePLPBody(value, encoding) {
    if (encoding == null) {
      encoding = this.encoding;
    }

    let length;

    if (value instanceof Buffer) {
      length = value.length;
    } else {
      value = value.toString();
      length = Buffer.byteLength(value, encoding);
    } // Length of all chunks.
    // this.writeUInt64LE(length);
    // unknown seems to work better here - might revisit later.


    this.writeBuffer(UNKNOWN_PLP_LEN); // In the UNKNOWN_PLP_LEN case, the data is represented as a series of zero or more chunks.

    if (length > 0) {
      // One chunk.
      this.writeUInt32LE(length);

      if (value instanceof Buffer) {
        this.writeBuffer(value);
      } else {
        this.makeRoomFor(length);
        this.buffer.write(value, this.position, encoding);
        this.position += length;
      }
    } // PLP_TERMINATOR (no more chunks).


    this.writeUInt32LE(0);
  }

  writeBuffer(value) {
    const length = value.length;
    this.makeRoomFor(length);
    value.copy(this.buffer, this.position);
    this.position += length;
  }

  writeMoney(value) {
    this.writeInt32LE(Math.floor(value * SHIFT_RIGHT_32));
    this.writeInt32LE(value & -1);
  }

}

var _default = WritableTrackingBuffer;
exports.default = _default;
module.exports = WritableTrackingBuffer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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