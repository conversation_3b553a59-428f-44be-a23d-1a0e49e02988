// Main JavaScript file

// DOM Elements
const body = document.body;
const navbarToggle = document.getElementById('navbar-toggle');
const navbarMenu = document.getElementById('navbar-menu');
const modals = document.querySelectorAll('.modal');
const modalTriggers = document.querySelectorAll('[data-modal]');
const modalCloses = document.querySelectorAll('.close');

// Toggle mobile menu
if (navbarToggle && navbarMenu) {
    navbarToggle.addEventListener('click', () => {
        navbarMenu.classList.toggle('active');
        navbarToggle.classList.toggle('active');
    });
}

// Modal functionality
if (modalTriggers.length > 0) {
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', () => {
            const modalId = trigger.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
                body.style.overflow = 'hidden';
            }
        });
    });
}

if (modalCloses.length > 0) {
    modalCloses.forEach(close => {
        close.addEventListener('click', () => {
            const modal = close.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                body.style.overflow = 'auto';
            }
        });
    });
}

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
            body.style.overflow = 'auto';
        }
    });
});

// Scroll to top button
const scrollTopBtn = document.getElementById('scroll-top');
if (scrollTopBtn) {
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            scrollTopBtn.style.display = 'block';
        } else {
            scrollTopBtn.style.display = 'none';
        }
    });

    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Tabs functionality
const tabButtons = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');

if (tabButtons.length > 0 && tabContents.length > 0) {
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.style.display = 'none');

            // Add active class to clicked button
            button.classList.add('active');

            // Show corresponding content
            const tabId = button.getAttribute('data-tab');
            const tabContent = document.getElementById(tabId);
            if (tabContent) {
                tabContent.style.display = 'block';
            }
        });
    });

    // Set default active tab
    if (tabButtons[0]) {
        tabButtons[0].click();
    }
}

// Form validation
const forms = document.querySelectorAll('form');
if (forms.length > 0) {
    forms.forEach(form => {
        form.addEventListener('submit', (e) => {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');

                    // Create error message if it doesn't exist
                    let errorMsg = field.nextElementSibling;
                    if (!errorMsg || !errorMsg.classList.contains('error-msg')) {
                        errorMsg = document.createElement('div');
                        errorMsg.classList.add('error-msg');
                        errorMsg.textContent = 'This field is required';
                        field.parentNode.insertBefore(errorMsg, field.nextSibling);
                    }
                } else {
                    field.classList.remove('error');

                    // Remove error message if it exists
                    const errorMsg = field.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('error-msg')) {
                        errorMsg.remove();
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });
    });
}

// Load components
document.addEventListener('DOMContentLoaded', () => {
    // Load header
    const headerContainer = document.getElementById('header-container');
    if (headerContainer) {
        fetch('components/header.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                headerContainer.innerHTML = data;
                // Reinitialize header-specific scripts
                initializeHeader();
            })
            .catch(error => {
                console.error('Error loading header:', error);
                // Fallback content for header
                headerContainer.innerHTML = `
                <header class="header">
                    <div class="container">
                        <div class="header-content">
                            <div class="logo">
                                <a href="index.html">
                                    <h1>JobFinder</h1>
                                </a>
                            </div>
                            <nav class="navbar">
                                <button id="navbar-toggle" class="navbar-toggle">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </button>
                                <ul id="navbar-menu" class="navbar-menu">
                                    <li><a href="index.html">Trang chủ</a></li>
                                    <li><a href="jobs.html">Việc làm</a></li>
                                    <li><a href="profile.html">Hồ sơ & CV</a></li>
                                    <li><a href="employer.html">Nhà tuyển dụng</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </header>`;
                initializeHeader();
            });
    }

    // Load footer
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
        fetch('components/footer.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                footerContainer.innerHTML = data;
            })
            .catch(error => {
                console.error('Error loading footer:', error);
                // Fallback content for footer
                footerContainer.innerHTML = `
                <footer class="footer">
                    <div class="container">
                        <div class="footer-content">
                            <div class="footer-logo">
                                <h2>JobFinder</h2>
                                <p>Nền tảng tìm kiếm việc làm hàng đầu Việt Nam</p>
                            </div>
                        </div>
                        <div class="footer-bottom">
                            <p>&copy; 2023 JobFinder. Tất cả quyền được bảo lưu.</p>
                        </div>
                    </div>
                </footer>`;
            });
    }

    // Load modals
    const modalsContainer = document.getElementById('modals-container');
    if (modalsContainer) {
        fetch('components/modals.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                modalsContainer.innerHTML = data;
                // Reinitialize modal-specific scripts
                initializeModals();
            })
            .catch(error => {
                console.error('Error loading modals:', error);
                // We don't add fallback modals as they're optional
                initializeModals();
            });
    }

    // Initialize tabs on page load
    const tabButtons = document.querySelectorAll('.tab-btn');
    if (tabButtons.length > 0 && tabButtons[0]) {
        tabButtons[0].click();
    }
});

// Initialize header-specific scripts
function initializeHeader() {
    const navbarToggle = document.getElementById('navbar-toggle');
    const navbarMenu = document.getElementById('navbar-menu');

    if (navbarToggle && navbarMenu) {
        navbarToggle.addEventListener('click', () => {
            navbarMenu.classList.toggle('active');
            navbarToggle.classList.toggle('active');
        });
    }
}

// Initialize modal-specific scripts
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const modalCloses = document.querySelectorAll('.close');

    if (modalTriggers.length > 0) {
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', () => {
                const modalId = trigger.getAttribute('data-modal');
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex';
                    body.style.overflow = 'hidden';
                }
            });
        });
    }

    if (modalCloses.length > 0) {
        modalCloses.forEach(close => {
            close.addEventListener('click', () => {
                const modal = close.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                    body.style.overflow = 'auto';
                }
            });
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.style.display = 'none';
                body.style.overflow = 'auto';
            }
        });
    });
}
