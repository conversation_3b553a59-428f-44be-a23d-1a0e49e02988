/* General styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f4f4f4;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Auth form styles */
.auth-form {
    max-width: 400px;
    margin: 40px auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.auth-form h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
    width: 100%;
}

.btn-primary:hover {
    background-color: #45a049;
}

.auth-links {
    text-align: center;
    margin-top: 15px;
}

.auth-links a {
    color: #4CAF50;
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

/* Error message styles */
.error-message {
    color: #f44336;
    margin-top: 5px;
    font-size: 14px;
}

/* Success message styles */
.success-message {
    color: #4CAF50;
    margin-top: 5px;
    font-size: 14px;
} 