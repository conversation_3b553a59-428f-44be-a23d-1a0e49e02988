/*! @azure/msal-common v13.3.1 2023-10-27 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Enumeration of operations that are instrumented by have their performance measured by the PerformanceClient.
 *
 * @export
 * @enum {number}
 */
var PerformanceEvents;
(function (PerformanceEvents) {
    /**
     * acquireTokenByCode API (msal-browser and msal-node).
     * Used to acquire tokens by trading an authorization code against the token endpoint.
     */
    PerformanceEvents["AcquireTokenByCode"] = "acquireTokenByCode";
    /**
     * acquireTokenByRefreshToken API (msal-browser and msal-node).
     * Used to renew an access token using a refresh token against the token endpoint.
     */
    PerformanceEvents["AcquireTokenByRefreshToken"] = "acquireTokenByRefreshToken";
    /**
     * acquireTokenSilent API (msal-browser and msal-node).
     * Used to silently acquire a new access token (from the cache or the network).
     */
    PerformanceEvents["AcquireTokenSilent"] = "acquireTokenSilent";
    /**
     * acquireTokenSilentAsync (msal-browser).
     * Internal API for acquireTokenSilent.
     */
    PerformanceEvents["AcquireTokenSilentAsync"] = "acquireTokenSilentAsync";
    /**
     * acquireTokenPopup (msal-browser).
     * Used to acquire a new access token interactively through pop ups
     */
    PerformanceEvents["AcquireTokenPopup"] = "acquireTokenPopup";
    /**
     * getPublicKeyThumbprint API in CryptoOpts class (msal-browser).
     * Used to generate a public/private keypair and generate a public key thumbprint for pop requests.
     */
    PerformanceEvents["CryptoOptsGetPublicKeyThumbprint"] = "cryptoOptsGetPublicKeyThumbprint";
    /**
     * signJwt API in CryptoOpts class (msal-browser).
     * Used to signed a pop token.
     */
    PerformanceEvents["CryptoOptsSignJwt"] = "cryptoOptsSignJwt";
    /**
     * acquireToken API in the SilentCacheClient class (msal-browser).
     * Used to read access tokens from the cache.
     */
    PerformanceEvents["SilentCacheClientAcquireToken"] = "silentCacheClientAcquireToken";
    /**
     * acquireToken API in the SilentIframeClient class (msal-browser).
     * Used to acquire a new set of tokens from the authorize endpoint in a hidden iframe.
     */
    PerformanceEvents["SilentIframeClientAcquireToken"] = "silentIframeClientAcquireToken";
    /**
     * acquireToken API in SilentRereshClient (msal-browser).
     * Used to acquire a new set of tokens from the token endpoint using a refresh token.
     */
    PerformanceEvents["SilentRefreshClientAcquireToken"] = "silentRefreshClientAcquireToken";
    /**
     * ssoSilent API (msal-browser).
     * Used to silently acquire an authorization code and set of tokens using a hidden iframe.
     */
    PerformanceEvents["SsoSilent"] = "ssoSilent";
    /**
     * getDiscoveredAuthority API in StandardInteractionClient class (msal-browser).
     * Used to load authority metadata for a request.
     */
    PerformanceEvents["StandardInteractionClientGetDiscoveredAuthority"] = "standardInteractionClientGetDiscoveredAuthority";
    /**
     * acquireToken APIs in msal-browser.
     * Used to make an /authorize endpoint call with native brokering enabled.
     */
    PerformanceEvents["FetchAccountIdWithNativeBroker"] = "fetchAccountIdWithNativeBroker";
    /**
     * acquireToken API in NativeInteractionClient class (msal-browser).
     * Used to acquire a token from Native component when native brokering is enabled.
     */
    PerformanceEvents["NativeInteractionClientAcquireToken"] = "nativeInteractionClientAcquireToken";
    /**
     * Time spent creating default headers for requests to token endpoint
     */
    PerformanceEvents["BaseClientCreateTokenRequestHeaders"] = "baseClientCreateTokenRequestHeaders";
    /**
     * Used to measure the time taken for completing embedded-broker handshake (PW-Broker).
     */
    PerformanceEvents["BrokerHandhshake"] = "brokerHandshake";
    /**
     * acquireTokenByRefreshToken API in BrokerClientApplication (PW-Broker) .
     */
    PerformanceEvents["AcquireTokenByRefreshTokenInBroker"] = "acquireTokenByRefreshTokenInBroker";
    /**
     * Time taken for token acquisition by broker
     */
    PerformanceEvents["AcquireTokenByBroker"] = "acquireTokenByBroker";
    /**
     * Time spent on the network for refresh token acquisition
     */
    PerformanceEvents["RefreshTokenClientExecuteTokenRequest"] = "refreshTokenClientExecuteTokenRequest";
    /**
     * Time taken for acquiring refresh token , records RT size
     */
    PerformanceEvents["RefreshTokenClientAcquireToken"] = "refreshTokenClientAcquireToken";
    /**
     * Time taken for acquiring cached refresh token
     */
    PerformanceEvents["RefreshTokenClientAcquireTokenWithCachedRefreshToken"] = "refreshTokenClientAcquireTokenWithCachedRefreshToken";
    /**
     * acquireTokenByRefreshToken API in RefreshTokenClient (msal-common).
     */
    PerformanceEvents["RefreshTokenClientAcquireTokenByRefreshToken"] = "refreshTokenClientAcquireTokenByRefreshToken";
    /**
     * Helper function to create token request body in RefreshTokenClient (msal-common).
     */
    PerformanceEvents["RefreshTokenClientCreateTokenRequestBody"] = "refreshTokenClientCreateTokenRequestBody";
    /**
     * acquireTokenFromCache (msal-browser).
     * Internal API for acquiring token from cache
     */
    PerformanceEvents["AcquireTokenFromCache"] = "acquireTokenFromCache";
    /**
     * acquireTokenBySilentIframe (msal-browser).
     * Internal API for acquiring token by silent Iframe
     */
    PerformanceEvents["AcquireTokenBySilentIframe"] = "acquireTokenBySilentIframe";
    /**
     * Internal API for initializing base request in BaseInteractionClient (msal-browser)
     */
    PerformanceEvents["InitializeBaseRequest"] = "initializeBaseRequest";
    /**
     * Internal API for initializing silent request in SilentCacheClient (msal-browser)
     */
    PerformanceEvents["InitializeSilentRequest"] = "initializeSilentRequest";
    PerformanceEvents["InitializeClientApplication"] = "initializeClientApplication";
    /**
     * Helper function in SilentIframeClient class (msal-browser).
     */
    PerformanceEvents["SilentIframeClientTokenHelper"] = "silentIframeClientTokenHelper";
    /**
     * SilentHandler
     */
    PerformanceEvents["SilentHandlerInitiateAuthRequest"] = "silentHandlerInitiateAuthRequest";
    PerformanceEvents["SilentHandlerMonitorIframeForHash"] = "silentHandlerMonitorIframeForHash";
    PerformanceEvents["SilentHandlerLoadFrame"] = "silentHandlerLoadFrame";
    /**
     * Helper functions in StandardInteractionClient class (msal-browser)
     */
    PerformanceEvents["StandardInteractionClientCreateAuthCodeClient"] = "standardInteractionClientCreateAuthCodeClient";
    PerformanceEvents["StandardInteractionClientGetClientConfiguration"] = "standardInteractionClientGetClientConfiguration";
    PerformanceEvents["StandardInteractionClientInitializeAuthorizationRequest"] = "standardInteractionClientInitializeAuthorizationRequest";
    PerformanceEvents["StandardInteractionClientInitializeAuthorizationCodeRequest"] = "standardInteractionClientInitializeAuthorizationCodeRequest";
    /**
     * getAuthCodeUrl API (msal-browser and msal-node).
     */
    PerformanceEvents["GetAuthCodeUrl"] = "getAuthCodeUrl";
    /**
     * Functions from InteractionHandler (msal-browser)
     */
    PerformanceEvents["HandleCodeResponseFromServer"] = "handleCodeResponseFromServer";
    PerformanceEvents["HandleCodeResponseFromHash"] = "handleCodeResponseFromHash";
    PerformanceEvents["UpdateTokenEndpointAuthority"] = "updateTokenEndpointAuthority";
    /**
     * APIs in Authorization Code Client (msal-common)
     */
    PerformanceEvents["AuthClientAcquireToken"] = "authClientAcquireToken";
    PerformanceEvents["AuthClientExecuteTokenRequest"] = "authClientExecuteTokenRequest";
    PerformanceEvents["AuthClientCreateTokenRequestBody"] = "authClientCreateTokenRequestBody";
    PerformanceEvents["AuthClientCreateQueryString"] = "authClientCreateQueryString";
    /**
     * Generate functions in PopTokenGenerator (msal-common)
     */
    PerformanceEvents["PopTokenGenerateCnf"] = "popTokenGenerateCnf";
    PerformanceEvents["PopTokenGenerateKid"] = "popTokenGenerateKid";
    /**
     * handleServerTokenResponse API in ResponseHandler (msal-common)
     */
    PerformanceEvents["HandleServerTokenResponse"] = "handleServerTokenResponse";
    /**
     * Authority functions
     */
    PerformanceEvents["AuthorityFactoryCreateDiscoveredInstance"] = "authorityFactoryCreateDiscoveredInstance";
    PerformanceEvents["AuthorityResolveEndpointsAsync"] = "authorityResolveEndpointsAsync";
    PerformanceEvents["AuthorityGetCloudDiscoveryMetadataFromNetwork"] = "authorityGetCloudDiscoveryMetadataFromNetwork";
    PerformanceEvents["AuthorityUpdateCloudDiscoveryMetadata"] = "authorityUpdateCloudDiscoveryMetadata";
    PerformanceEvents["AuthorityGetEndpointMetadataFromNetwork"] = "authorityGetEndpointMetadataFromNetwork";
    PerformanceEvents["AuthorityUpdateEndpointMetadata"] = "authorityUpdateEndpointMetadata";
    PerformanceEvents["AuthorityUpdateMetadataWithRegionalInformation"] = "authorityUpdateMetadataWithRegionalInformation";
    /**
     * Region Discovery functions
     */
    PerformanceEvents["RegionDiscoveryDetectRegion"] = "regionDiscoveryDetectRegion";
    PerformanceEvents["RegionDiscoveryGetRegionFromIMDS"] = "regionDiscoveryGetRegionFromIMDS";
    PerformanceEvents["RegionDiscoveryGetCurrentVersion"] = "regionDiscoveryGetCurrentVersion";
    PerformanceEvents["AcquireTokenByCodeAsync"] = "acquireTokenByCodeAsync";
    PerformanceEvents["GetEndpointMetadataFromNetwork"] = "getEndpointMetadataFromNetwork";
    PerformanceEvents["GetCloudDiscoveryMetadataFromNetworkMeasurement"] = "getCloudDiscoveryMetadataFromNetworkMeasurement";
    PerformanceEvents["HandleRedirectPromiseMeasurement"] = "handleRedirectPromiseMeasurement";
    PerformanceEvents["UpdateCloudDiscoveryMetadataMeasurement"] = "updateCloudDiscoveryMetadataMeasurement";
    PerformanceEvents["UsernamePasswordClientAcquireToken"] = "usernamePasswordClientAcquireToken";
    PerformanceEvents["NativeMessageHandlerHandshake"] = "nativeMessageHandlerHandshake";
    /**
     * Cache operations
     */
    PerformanceEvents["ClearTokensAndKeysWithClaims"] = "clearTokensAndKeysWithClaims";
})(PerformanceEvents || (PerformanceEvents = {}));
/**
 * State of the performance event.
 *
 * @export
 * @enum {number}
 */
var PerformanceEventStatus;
(function (PerformanceEventStatus) {
    PerformanceEventStatus[PerformanceEventStatus["NotStarted"] = 0] = "NotStarted";
    PerformanceEventStatus[PerformanceEventStatus["InProgress"] = 1] = "InProgress";
    PerformanceEventStatus[PerformanceEventStatus["Completed"] = 2] = "Completed";
})(PerformanceEventStatus || (PerformanceEventStatus = {}));
var IntFields = new Set([
    "accessTokenSize",
    "durationMs",
    "idTokenSize",
    "matsSilentStatus",
    "matsHttpStatus",
    "refreshTokenSize",
    "queuedTimeMs",
    "startTimeMs",
    "status",
]);

export { IntFields, PerformanceEventStatus, PerformanceEvents };
//# sourceMappingURL=PerformanceEvent.js.map
