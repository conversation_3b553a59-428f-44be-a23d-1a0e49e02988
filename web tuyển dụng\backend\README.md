# JobFinder Backend API

Backend API cho ứng dụng tuyển dụng JobFinder.

## Cài đặt

1. <PERSON><PERSON><PERSON> đặt các gói phụ thuộc:

```bash
npm install
```

2. Tạo file `.env` với nội dung sau (điều chỉnh thông tin kết nối database):

```
# Server Configuration
PORT=5000
NODE_ENV=development

# JWT Secret
JWT_SECRET=jobfinder_secret_key
JWT_EXPIRES_IN=7d

# Database Configuration
DB_SERVER=LAPTOP-D7NLK3DI\\MSSQLSERVER04
DB_NAME=jobfinder
DB_USER=sa
DB_PASSWORD=123456
DB_PORT=1433

# File Upload Paths
UPLOAD_DIR=uploads
CV_UPLOAD_DIR=uploads/cv
LOGO_UPLOAD_DIR=uploads/logos
```

3. Thiết lập cơ sở dữ liệu:

```bash
npm run setup-db
```

4. <PERSON><PERSON><PERSON> tra kết nối cơ sở dữ liệu:

```bash
node test-connection.js
```

## Chạy ứng dụng

Chạy ở chế độ phát triển:

```bash
npm run dev
```

Chạy ở chế độ sản phẩm:

```bash
npm start
```

## API Endpoints

### Xác thực

- `POST /api/auth/register` - Đăng ký người dùng mới
- `POST /api/auth/login` - Đăng nhập
- `GET /api/auth/profile` - Lấy thông tin người dùng
- `PUT /api/auth/profile` - Cập nhật thông tin người dùng
- `PUT /api/auth/change-password` - Đổi mật khẩu

### Công việc

- `GET /api/jobs` - Lấy danh sách công việc
- `GET /api/jobs/:id` - Lấy thông tin công việc theo ID
- `GET /api/jobs/search` - Tìm kiếm công việc
- `POST /api/jobs/apply/:id` - Ứng tuyển công việc
- `POST /api/jobs` - Tạo công việc mới (chỉ dành cho nhà tuyển dụng)
- `PUT /api/jobs/:id` - Cập nhật công việc (chỉ dành cho nhà tuyển dụng)
- `DELETE /api/jobs/:id` - Xóa công việc (chỉ dành cho nhà tuyển dụng)
- `GET /api/jobs/categories` - Lấy danh mục công việc

### Hồ sơ

- `GET /api/profiles` - Lấy hồ sơ người dùng
- `PUT /api/profiles/update` - Cập nhật hồ sơ
- `POST /api/profiles/cv` - Tải lên CV
- `GET /api/profiles/applications` - Lấy danh sách ứng tuyển của người dùng
