const { pool, sql } = require('../config/db');

class Profile {
  // Get profile by user ID
  static async getByUserId(userId) {
    try {
      await pool.connect();
      
      const request = pool.request();
      request.input('user_id', sql.Int, userId);
      
      const result = await request.query(`
        SELECT * FROM profiles
        WHERE user_id = @user_id
      `);
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }
  
  // Create or update profile
  static async createOrUpdate(userId, profileData) {
    try {
      await pool.connect();
      
      // Check if profile exists
      const checkRequest = pool.request();
      checkRequest.input('user_id', sql.Int, userId);
      
      const checkResult = await checkRequest.query(`
        SELECT id FROM profiles
        WHERE user_id = @user_id
      `);
      
      const profileExists = checkResult.recordset && checkResult.recordset.length > 0;
      
      const request = pool.request();
      request.input('user_id', sql.Int, userId);
      request.input('title', sql.NVarChar(100), profileData.title || null);
      request.input('summary', sql.NVarChar(sql.MAX), profileData.summary || null);
      request.input('experience', sql.NVarChar(sql.MAX), profileData.experience || null);
      request.input('education', sql.NVarChar(sql.MAX), profileData.education || null);
      request.input('skills', sql.NVarChar(sql.MAX), profileData.skills || null);
      request.input('cv_path', sql.NVarChar(255), profileData.cvPath || null);
      
      if (profileExists) {
        // Update existing profile
        await request.query(`
          UPDATE profiles
          SET title = @title,
              summary = @summary,
              experience = @experience,
              education = @education,
              skills = @skills,
              cv_path = ISNULL(@cv_path, cv_path),
              updated_at = GETDATE()
          WHERE user_id = @user_id
        `);
      } else {
        // Create new profile
        await request.query(`
          INSERT INTO profiles (user_id, title, summary, experience, education, skills, cv_path)
          VALUES (@user_id, @title, @summary, @experience, @education, @skills, @cv_path)
        `);
      }
      
      return await this.getByUserId(userId);
    } catch (error) {
      console.error('Create/update profile error:', error);
      throw error;
    }
  }
  
  // Update CV path
  static async updateCV(userId, cvPath) {
    try {
      await pool.connect();
      
      // Check if profile exists
      const checkRequest = pool.request();
      checkRequest.input('user_id', sql.Int, userId);
      
      const checkResult = await checkRequest.query(`
        SELECT id FROM profiles
        WHERE user_id = @user_id
      `);
      
      const profileExists = checkResult.recordset && checkResult.recordset.length > 0;
      
      const request = pool.request();
      request.input('user_id', sql.Int, userId);
      request.input('cv_path', sql.NVarChar(255), cvPath);
      
      if (profileExists) {
        // Update existing profile
        await request.query(`
          UPDATE profiles
          SET cv_path = @cv_path,
              updated_at = GETDATE()
          WHERE user_id = @user_id
        `);
      } else {
        // Create new profile with CV
        await request.query(`
          INSERT INTO profiles (user_id, cv_path)
          VALUES (@user_id, @cv_path)
        `);
      }
      
      return await this.getByUserId(userId);
    } catch (error) {
      console.error('Update CV error:', error);
      throw error;
    }
  }
  
  // Get user applications
  static async getUserApplications(userId) {
    try {
      await pool.connect();
      
      const request = pool.request();
      request.input('user_id', sql.Int, userId);
      
      const result = await request.query(`
        SELECT a.*, j.title as job_title, j.location as job_location, 
               c.name as company_name, c.logo_path as company_logo
        FROM applications a
        JOIN jobs j ON a.job_id = j.id
        JOIN companies c ON j.company_id = c.id
        WHERE a.user_id = @user_id
        ORDER BY a.created_at DESC
      `);
      
      return result.recordset || [];
    } catch (error) {
      console.error('Get user applications error:', error);
      throw error;
    }
  }
}

module.exports = Profile;
