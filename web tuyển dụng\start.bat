@echo off
echo Starting JobFinder System...

cd backend

echo Installing dependencies...
call npm install

echo Starting backend server...
start cmd /k "npm run dev"

echo Waiting for backend server to start...
timeout /t 5 /nobreak

echo Checking backend connection...
node test-connection.js
if errorlevel 1 (
    echo Failed to connect to backend server
    exit /b 1
)

cd ..
echo Opening frontend...
start "" "index.html"

echo System started successfully!
echo Backend running at http://localhost:5000
echo Frontend opened in default browser