/*! @azure/msal-common v13.3.1 2023-10-27 */
'use strict';
import { __extends } from '../../_virtual/_tslib.js';
import { PerformanceClient } from './PerformanceClient.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var StubPerformanceMeasurement = /** @class */ (function () {
    function StubPerformanceMeasurement() {
    }
    /* eslint-disable-next-line @typescript-eslint/no-empty-function */
    StubPerformanceMeasurement.prototype.startMeasurement = function () { };
    /* eslint-disable-next-line @typescript-eslint/no-empty-function */
    StubPerformanceMeasurement.prototype.endMeasurement = function () { };
    StubPerformanceMeasurement.prototype.flushMeasurement = function () {
        return null;
    };
    return StubPerformanceMeasurement;
}());
var StubPerformanceClient = /** @class */ (function (_super) {
    __extends(StubPerformanceClient, _super);
    function StubPerformanceClient() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    StubPerformanceClient.prototype.generateId = function () {
        return "callback-id";
    };
    StubPerformanceClient.prototype.startPerformanceMeasuremeant = function () {
        return new StubPerformanceMeasurement();
    };
    StubPerformanceClient.prototype.startPerformanceMeasurement = function () {
        return new StubPerformanceMeasurement();
    };
    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
    StubPerformanceClient.prototype.calculateQueuedTime = function (preQueueTime, currentTime) {
        return 0;
    };
    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
    StubPerformanceClient.prototype.addQueueMeasurement = function (eventName, correlationId, queueTime) {
        return;
    };
    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
    StubPerformanceClient.prototype.setPreQueueTime = function (eventName, correlationId) {
        return;
    };
    return StubPerformanceClient;
}(PerformanceClient));

export { StubPerformanceClient, StubPerformanceMeasurement };
//# sourceMappingURL=StubPerformanceClient.js.map
