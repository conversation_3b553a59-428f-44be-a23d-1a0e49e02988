const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Profile = require('../models/Profile');
const { auth } = require('../middleware/auth');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '..', process.env.CV_UPLOAD_DIR || 'uploads/cv');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'cv-' + uniqueSuffix + ext);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Accept only PDF, DOC, DOCX files
  const allowedFileTypes = ['.pdf', '.doc', '.docx'];
  const ext = path.extname(file.originalname).toLowerCase();
  
  if (allowedFileTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error('Chỉ chấp nhận file PDF, DOC, DOCX'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  }
});

// @route   GET /api/profiles
// @desc    Get user profile
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const profile = await Profile.getByUserId(userId);
    
    res.status(200).json({ profile: profile || {} });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy thông tin hồ sơ' });
  }
});

// @route   PUT /api/profiles/update
// @desc    Update user profile
// @access  Private
router.put('/update', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { title, summary, experience, education, skills } = req.body;
    
    const profile = await Profile.createOrUpdate(userId, {
      title,
      summary,
      experience,
      education,
      skills
    });
    
    res.status(200).json({ profile });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi cập nhật hồ sơ' });
  }
});

// @route   POST /api/profiles/cv
// @desc    Upload CV
// @access  Private
router.post('/cv', auth, upload.single('cv'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Vui lòng chọn file CV' });
    }
    
    const userId = req.user.id;
    const cvPath = '/' + path.relative(path.join(__dirname, '..'), req.file.path).replace(/\\/g, '/');
    
    const profile = await Profile.updateCV(userId, cvPath);
    
    res.status(200).json({ profile });
  } catch (error) {
    console.error('Upload CV error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tải lên CV' });
  }
});

// @route   GET /api/profiles/applications
// @desc    Get user applications
// @access  Private
router.get('/applications', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const applications = await Profile.getUserApplications(userId);
    
    res.status(200).json({ applications });
  } catch (error) {
    console.error('Get applications error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh sách ứng tuyển' });
  }
});

module.exports = router;
