/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Common Elements */
h1, h2, h3, h4, h5, h6 {
    color: #333;
    margin-bottom: 15px;
}

a {
    text-decoration: none;
    color: #4A90E2;
    transition: color 0.3s ease;
}

a:hover {
    color: #3A78C2;
}

button {
    cursor: pointer;
    border: none;
    outline: none;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.py-40 {
    padding-top: 40px;
    padding-bottom: 40px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    width: 100%;
    max-width: 400px;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #333;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff4d4f;
}

.modal-content h2 {
    color: #4A90E2;
    text-align: center;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4A90E2;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-btn {
    width: 100%;
    padding: 12px;
    background-color: #4A90E2;
    color: #fff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.modal-btn:hover {
    background-color: #3A78C2;
    transform: scale(1.05);
}

.modal-link {
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}

.modal-link a {
    color: #4A90E2;
    text-decoration: none;
}

.modal-link a:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .search-bar input {
        width: 100%;
    }
    
    .filters {
        flex-wrap: wrap;
    }
    
    .filters select {
        width: 48%;
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .filters select {
        width: 100%;
    }
    
    .search-bar {
        flex-direction: column;
    }
    
    .search-btn {
        width: 100%;
        margin-top: 10px;
    }
}
