{"version": 3, "file": "tokenCredential.js", "sourceRoot": "", "sources": ["../../src/tokenCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AA4GlC;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,WAAwB;IACpD,OAAO,CAAC,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,KAAK,QAAQ,CAAC;AACtE,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,WAAwB;IACjD,OAAO,WAAW,CAAC,SAAS,KAAK,KAAK,CAAC;AACzC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAAC,UAAmB;IACnD,mEAAmE;IACnE,gEAAgE;IAChE,sEAAsE;IACtE,qEAAqE;IACrE,sDAAsD;IACtD,MAAM,cAAc,GAAG,UAGtB,CAAC;IACF,OAAO,CACL,cAAc;QACd,OAAO,cAAc,CAAC,QAAQ,KAAK,UAAU;QAC7C,CAAC,cAAc,CAAC,WAAW,KAAK,SAAS,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CACjF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { TracingContext } from \"./tracing.js\";\nimport { HttpMethods } from \"@azure/core-util\";\n\n/**\n * Represents a credential capable of providing an authentication token.\n */\nexport interface TokenCredential {\n  /**\n   * Gets the token provided by this credential.\n   *\n   * This method is called automatically by Azure SDK client libraries. You may call this method\n   * directly, but you must also handle token caching and token refreshing.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  getToken(scopes: string | string[], options?: GetTokenOptions): Promise<AccessToken | null>;\n}\n\n/**\n * Defines options for TokenCredential.getToken.\n */\nexport interface GetTokenOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: {\n    /**\n     * The number of milliseconds a request can take before automatically being terminated.\n     */\n    timeout?: number;\n  };\n  /**\n   * Options used when tracing is enabled.\n   */\n  tracingOptions?: {\n    /**\n     * Tracing Context for the current request.\n     */\n    tracingContext?: TracingContext;\n  };\n  /**\n   * Claim details to perform the Continuous Access Evaluation authentication flow\n   */\n  claims?: string;\n  /**\n   * Indicates whether to enable the Continuous Access Evaluation authentication flow\n   */\n  enableCae?: boolean;\n  /**\n   * Allows specifying a tenantId. Useful to handle challenges that provide tenant Id hints.\n   */\n  tenantId?: string;\n\n  /**\n   * Options for Proof of Possession token requests\n   */\n  proofOfPossessionOptions?: {\n    /**\n     * The nonce value required for PoP token requests.\n     * This is typically retrieved from the WWW-Authenticate header of a 401 challenge response.\n     * This is used in combination with {@link resourceRequestUrl} and {@link resourceRequestMethod} to generate the PoP token.\n     */\n    nonce: string;\n    /**\n     * The HTTP method of the request.\n     * This is used in combination with {@link resourceRequestUrl} and {@link nonce} to generate the PoP token.\n     */\n    resourceRequestMethod: HttpMethods;\n    /**\n     * The URL of the request.\n     * This is used in combination with {@link resourceRequestMethod} and {@link nonce} to generate the PoP token.\n     */\n    resourceRequestUrl: string;\n  };\n}\n\n/**\n * Represents an access token with an expiration time.\n */\nexport interface AccessToken {\n  /**\n   * The access token returned by the authentication service.\n   */\n  token: string;\n\n  /**\n   * The access token's expiration timestamp in milliseconds, UNIX epoch time.\n   */\n  expiresOnTimestamp: number;\n\n  /**\n   * The timestamp when the access token should be refreshed, in milliseconds, UNIX epoch time.\n   */\n  refreshAfterTimestamp?: number;\n\n  /** Type of token - `Bearer` or `pop` */\n  tokenType?: \"Bearer\" | \"pop\";\n}\n\n/**\n * @internal\n * @param accessToken - Access token\n * @returns Whether a token is bearer type or not\n */\nexport function isBearerToken(accessToken: AccessToken): boolean {\n  return !accessToken.tokenType || accessToken.tokenType === \"Bearer\";\n}\n\n/**\n * @internal\n * @param accessToken - Access token\n * @returns Whether a token is Pop token or not\n */\nexport function isPopToken(accessToken: AccessToken): boolean {\n  return accessToken.tokenType === \"pop\";\n}\n\n/**\n * Tests an object to determine whether it implements TokenCredential.\n *\n * @param credential - The assumed TokenCredential to be tested.\n */\nexport function isTokenCredential(credential: unknown): credential is TokenCredential {\n  // Check for an object with a 'getToken' function and possibly with\n  // a 'signRequest' function.  We do this check to make sure that\n  // a ServiceClientCredentials implementor (like TokenClientCredentials\n  // in ms-rest-nodeauth) doesn't get mistaken for a TokenCredential if\n  // it doesn't actually implement TokenCredential also.\n  const castCredential = credential as {\n    getToken: unknown;\n    signRequest: unknown;\n  };\n  return (\n    castCredential &&\n    typeof castCredential.getToken === \"function\" &&\n    (castCredential.signRequest === undefined || castCredential.getToken.length > 0)\n  );\n}\n"]}