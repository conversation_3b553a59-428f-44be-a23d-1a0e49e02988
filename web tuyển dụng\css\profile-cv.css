/* Profile & CV Section Styles */
.profile-cv-section {
    background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    padding: 50px 0;
    border-radius: 0;
    position: relative;
    overflow: hidden;
}

.profile-cv-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../jobfinder-dots-pattern-final.svg');
    background-size: 300px;
    background-repeat: repeat;
    opacity: 0.05;
    z-index: 0;
}

.profile-cv-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.profile-cv-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.profile-cv-header h2 {
    font-size: 36px;
    font-weight: 700;
    color: #064e3b;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.profile-cv-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #064e3b, #10b981);
    border-radius: 2px;
}

.profile-cv-header p {
    font-size: 16px;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    margin-top: 20px;
}

.profile-cv-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.action-button {
    background: linear-gradient(135deg, #064e3b, #047857);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 16px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    max-width: 300px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #065f46, #059669);
}

.action-button:active {
    transform: translateY(-1px);
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%);
    transition: all 0.6s ease;
}

.action-button:hover::before {
    left: 100%;
}

.action-button i {
    font-size: 18px;
}

.search-container {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.search-box {
    display: flex;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.search-input {
    flex: 1;
    padding: 16px 20px;
    border: 1px solid #e0e0e0;
    border-right: none;
    border-radius: 8px 0 0 8px;
    font-size: 15px;
    outline: none;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #10b981;
}

.search-button {
    background: linear-gradient(135deg, #064e3b, #047857);
    color: white;
    border: none;
    padding: 0 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    background: linear-gradient(135deg, #065f46, #059669);
}

.filter-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.filter-button {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-button:hover {
    border-color: #10b981;
    color: #064e3b;
    background-color: #f0fdf4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.filter-button i {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.filter-button:hover i {
    transform: rotate(180deg);
}

.profile-cv-content {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
    min-height: 300px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state i {
    font-size: 60px;
    color: #d1d5db;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 20px;
    color: #4b5563;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 15px;
    color: #6b7280;
    max-width: 400px;
    margin: 0 auto;
    margin-bottom: 20px;
}

.empty-state .start-button {
    background: linear-gradient(135deg, #064e3b, #047857);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.empty-state .start-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive styles */
@media (max-width: 768px) {
    .profile-cv-actions {
        flex-direction: column;
        align-items: center;
    }

    .action-button {
        max-width: 100%;
    }

    .filter-container {
        gap: 10px;
    }

    .filter-button {
        padding: 10px 15px;
        font-size: 13px;
    }
}

/* Animation for buttons */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

.action-button:focus {
    animation: pulse 1.5s infinite;
}

/* CV Templates Section */
.cv-templates-section {
    margin-top: 50px;
}

.section-title {
    font-size: 24px;
    font-weight: 700;
    color: #064e3b;
    margin-bottom: 20px;
    position: relative;
    padding-left: 15px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 5px;
    background: linear-gradient(to bottom, #064e3b, #10b981);
    border-radius: 3px;
}

.templates-filter {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.template-filter-btn {
    padding: 8px 16px;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    font-size: 14px;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-filter-btn:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

.template-filter-btn.active {
    background-color: #064e3b;
    color: white;
    border-color: #064e3b;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.template-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #e5e7eb;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.template-image {
    height: 380px;
    overflow: hidden;
    position: relative;
}

.template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.template-card:hover .template-image img {
    transform: translateY(-10%);
}

.template-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: 20px;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.template-btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.preview-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.preview-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.use-btn {
    background-color: #10b981;
    color: white;
    border: none;
}

.use-btn:hover {
    background-color: #059669;
}

.template-info {
    padding: 15px;
    border-top: 1px solid #e5e7eb;
}

.template-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 5px;
}

.template-category {
    font-size: 14px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 5px;
}

.template-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #ef4444;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.template-badge.pro {
    background-color: #f59e0b;
}

.template-badge.free {
    background-color: #10b981;
}

.view-more-btn {
    background-color: transparent;
    color: #064e3b;
    border: 2px solid #064e3b;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
    width: fit-content;
}

.view-more-btn:hover {
    background-color: #064e3b;
    color: white;
}
