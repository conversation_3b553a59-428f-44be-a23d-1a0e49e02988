/* Jobs Page Styles */
.featured-jobs {
    padding: 40px 0;
    background-color: #f5f7fa;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.featured-jobs h2 {
    text-align: center;
    font-size: 32px;
    margin-bottom: 30px;
    color: #4A90E2;
}

.job-list {
    max-height: 800px;
    overflow-y: auto;
    padding: 0 10px;
}

.job-list::-webkit-scrollbar {
    width: 8px;
}

.job-list::-webkit-scrollbar-thumb {
    background-color: #4A90E2;
    border-radius: 4px;
}

.job-list::-webkit-scrollbar-track {
    background-color: #e0e0e0;
}

.job-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.job-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.job-item img {
    width: 70px;
    height: 70px;
    object-fit: contain;
    margin-right: 20px;
    border-radius: 8px;
    background-color: #f9f9f9;
    padding: 5px;
}

.job-info {
    flex: 1;
}

.job-info h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 8px;
}

.job-info .company {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
}

.job-info .level {
    font-size: 14px;
    color: #999;
    margin-bottom: 8px;
}

.job-info .skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.job-info .skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.job-info .details {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 14px;
    color: #00a550;
}

.job-info .details i {
    color: #00a550;
    margin-right: 5px;
}

.job-info .details .days-left {
    color: #00a550;
}

.job-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.job-actions .salary {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: bold;
}

.job-actions .apply-btn {
    background-color: #28a745;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-actions .apply-btn:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.job-actions .favorite {
    color: #ccc;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-actions .favorite:hover,
.job-actions .favorite.active {
    color: #ff4d4f;
    transform: scale(1.1);
}

.job-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: #ff4d4f;
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

/* Job Filters */
.job-filters {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 30px;
}

.job-filters h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 20px;
}

.filter-group label {
    display: block;
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter-option input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.filter-option span {
    font-size: 14px;
    color: #666;
}

.filter-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.filter-btn {
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-filter-btn {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.apply-filter-btn:hover {
    background-color: #3A78C2;
}

.reset-filter-btn {
    background-color: transparent;
    color: #666;
    border: 1px solid #ccc;
}

.reset-filter-btn:hover {
    background-color: #f5f5f5;
}

/* Job Detail Modal */
.job-detail-modal .modal-content {
    max-width: 800px;
    padding: 0;
}

.job-detail-header {
    display: flex;
    align-items: center;
    padding: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.job-detail-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-right: 20px;
    border-radius: 8px;
    background-color: #f9f9f9;
    padding: 5px;
}

.job-detail-title {
    flex: 1;
}

.job-detail-title h2 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.job-detail-title p {
    font-size: 16px;
    color: #666;
}

.job-detail-body {
    padding: 30px;
}

.job-detail-section {
    margin-bottom: 30px;
}

.job-detail-section h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.job-detail-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.job-detail-info-item {
    display: flex;
    flex-direction: column;
}

.job-detail-info-item span:first-child {
    font-size: 14px;
    color: #999;
    margin-bottom: 5px;
}

.job-detail-info-item span:last-child {
    font-size: 16px;
    color: #333;
    font-weight: bold;
}

.job-detail-description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
}

.job-detail-description ul {
    padding-left: 20px;
    margin: 15px 0;
}

.job-detail-description ul li {
    margin-bottom: 10px;
}

.job-detail-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.job-detail-skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
}

.job-detail-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background-color: #f9f9f9;
    border-top: 1px solid #f0f0f0;
}

.job-detail-footer .salary {
    font-size: 20px;
    font-weight: bold;
    color: #28a745;
}

.job-detail-actions {
    display: flex;
    gap: 15px;
}

.job-detail-apply-btn {
    background-color: #28a745;
    color: #fff;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-detail-apply-btn:hover {
    background-color: #218838;
    transform: translateY(-2px);
}

.job-detail-save-btn {
    background-color: transparent;
    color: #4A90E2;
    padding: 12px 25px;
    border: 1px solid #4A90E2;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-detail-save-btn:hover {
    background-color: #f0f7ff;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .job-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-item img {
        margin-bottom: 15px;
    }

    .job-info .details {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .job-actions {
        flex-direction: row;
        align-items: center;
        width: 100%;
        margin-top: 20px;
    }

    .job-detail-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-detail-logo {
        margin-bottom: 15px;
    }

    .job-detail-footer {
        flex-direction: column;
        gap: 15px;
    }

    .job-detail-actions {
        width: 100%;
    }

    .job-detail-apply-btn,
    .job-detail-save-btn {
        width: 100%;
    }
}
