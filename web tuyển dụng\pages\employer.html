<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhà tuyển dụng - JobFinder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/header-footer.css">
    <link rel="stylesheet" href="css/employer.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Employer Page -->
    <div class="employer-page">
        <div class="container">
            <h2>Trang nhà tuyển dụng</h2>

            <div class="employer-tabs">
                <button class="tab-btn" data-tab="post-job-tab">Đ<PERSON><PERSON> tin tuyển dụng</button>
                <button class="tab-btn" data-tab="job-postings-tab">Tin đã đăng</button>
                <button class="tab-btn" data-tab="applications-tab">Đơn ứng tuyển</button>
                <button class="tab-btn" data-tab="find-profiles-tab">Tìm hồ sơ</button>
            </div>

            <!-- Post Job Tab -->
            <div id="post-job-tab" class="tab-content">
                <h3>Đăng tin tuyển dụng mới</h3>

                <form id="post-job-form">
                    <div class="job-form-group">
                        <label for="job-title">Tiêu đề công việc</label>
                        <input type="text" id="job-title" required>
                    </div>

                    <div class="job-form-row">
                        <div class="job-form-group">
                            <label for="job-location">Địa điểm</label>
                            <input type="text" id="job-location" required>
                        </div>

                        <div class="job-form-group">
                            <label for="job-type">Loại hình</label>
                            <select id="job-type" required>
                                <option value="">-- Chọn loại hình --</option>
                                <option value="Toàn thời gian">Toàn thời gian</option>
                                <option value="Bán thời gian">Bán thời gian</option>
                                <option value="Thực tập">Thực tập</option>
                                <option value="Freelance">Freelance</option>
                                <option value="Remote">Remote</option>
                            </select>
                        </div>
                    </div>

                    <div class="job-form-row">
                        <div class="job-form-group">
                            <label for="job-level">Cấp bậc</label>
                            <select id="job-level" required>
                                <option value="">-- Chọn cấp bậc --</option>
                                <option value="Thực tập sinh">Thực tập sinh</option>
                                <option value="Mới tốt nghiệp">Mới tốt nghiệp</option>
                                <option value="Nhân viên">Nhân viên</option>
                                <option value="Trưởng nhóm">Trưởng nhóm</option>
                                <option value="Quản lý">Quản lý</option>
                                <option value="Giám đốc">Giám đốc</option>
                            </select>
                        </div>

                        <div class="job-form-group">
                            <label for="job-experience">Kinh nghiệm</label>
                            <select id="job-experience" required>
                                <option value="">-- Chọn kinh nghiệm --</option>
                                <option value="Không yêu cầu">Không yêu cầu</option>
                                <option value="Dưới 1 năm">Dưới 1 năm</option>
                                <option value="1 năm">1 năm</option>
                                <option value="2 năm">2 năm</option>
                                <option value="3 - 5 năm">3 - 5 năm</option>
                                <option value="Trên 5 năm">Trên 5 năm</option>
                            </select>
                        </div>
                    </div>

                    <div class="job-form-row">
                        <div class="job-form-group">
                            <label for="job-salary-min">Lương tối thiểu (VNĐ)</label>
                            <input type="number" id="job-salary-min" min="0" step="1000000">
                        </div>

                        <div class="job-form-group">
                            <label for="job-salary-max">Lương tối đa (VNĐ)</label>
                            <input type="number" id="job-salary-max" min="0" step="1000000">
                        </div>
                    </div>

                    <div class="job-form-group">
                        <label for="job-salary-hidden">
                            <input type="checkbox" id="job-salary-hidden">
                            Không hiển thị mức lương
                        </label>
                    </div>

                    <div class="job-form-group">
                        <label for="job-deadline">Hạn nộp hồ sơ</label>
                        <input type="date" id="job-deadline" required>
                    </div>

                    <div class="job-form-group">
                        <label for="job-description">Mô tả công việc</label>
                        <textarea id="job-description" required></textarea>
                    </div>

                    <div class="job-form-group">
                        <label for="job-requirements">Yêu cầu</label>
                        <textarea id="job-requirements" required></textarea>
                    </div>

                    <div class="job-form-group">
                        <label for="job-benefits">Quyền lợi</label>
                        <textarea id="job-benefits" required></textarea>
                    </div>

                    <div class="job-form-group">
                        <label for="job-skills">Kỹ năng (phân cách bằng dấu phẩy)</label>
                        <input type="text" id="job-skills" required>
                    </div>

                    <div class="job-form-group">
                        <label for="job-hot">
                            <input type="checkbox" id="job-hot">
                            Đánh dấu là tin hot
                        </label>
                    </div>

                    <button type="submit" class="job-form-submit">Đăng tin</button>
                </form>
            </div>

            <!-- Job Postings Tab -->
            <div id="job-postings-tab" class="tab-content">
                <h3>Tin tuyển dụng đã đăng</h3>

                <div id="job-postings-list">
                    <!-- Job postings will be loaded dynamically -->
                </div>
            </div>

            <!-- Applications Tab -->
            <div id="applications-tab" class="tab-content">
                <h3>Đơn ứng tuyển</h3>

                <div id="applications-list">
                    <!-- Applications will be loaded dynamically -->
                </div>
            </div>

            <!-- Find Profiles Tab -->
            <div id="find-profiles-tab" class="tab-content">
                <h3>Tìm kiếm hồ sơ ứng viên</h3>

                <form id="find-profiles-form">
                    <div class="profile-search-bar">
                        <input type="text" id="profile-search-input" placeholder="Nhập từ khóa, vị trí, kỹ năng...">
                        <button type="submit"><i class="fas fa-search"></i> Tìm kiếm</button>
                    </div>

                    <div class="profile-filters">
                        <select class="profile-filter" name="skills">
                            <option value="">Kỹ năng</option>
                            <option value="JavaScript">JavaScript</option>
                            <option value="Java">Java</option>
                            <option value="Python">Python</option>
                            <option value="React">React</option>
                            <option value="Node.js">Node.js</option>
                        </select>

                        <select class="profile-filter" name="experience">
                            <option value="">Kinh nghiệm</option>
                            <option value="0">Chưa có kinh nghiệm</option>
                            <option value="1">1 năm</option>
                            <option value="2">2 năm</option>
                            <option value="3-5">3 - 5 năm</option>
                            <option value="5">Trên 5 năm</option>
                        </select>

                        <select class="profile-filter" name="education">
                            <option value="">Học vấn</option>
                            <option value="Trung cấp">Trung cấp</option>
                            <option value="Cao đẳng">Cao đẳng</option>
                            <option value="Đại học">Đại học</option>
                            <option value="Thạc sĩ">Thạc sĩ</option>
                            <option value="Tiến sĩ">Tiến sĩ</option>
                        </select>
                    </div>
                </form>

                <div id="profile-results">
                    <!-- Profile results will be loaded dynamically -->
                </div>
            </div>

            <!-- Employer Dashboard -->
            <div class="employer-dashboard">
                <div class="dashboard-section">
                    <h3>Thống kê</h3>

                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <i class="fas fa-briefcase"></i>
                            <h4>10</h4>
                            <p>Tin đăng</p>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-users"></i>
                            <h4>25</h4>
                            <p>Đơn ứng tuyển</p>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-eye"></i>
                            <h4>1,250</h4>
                            <p>Lượt xem</p>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-check-circle"></i>
                            <h4>5</h4>
                            <p>Đã tuyển</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Modals -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/employer.js"></script>
</body>
</html>
