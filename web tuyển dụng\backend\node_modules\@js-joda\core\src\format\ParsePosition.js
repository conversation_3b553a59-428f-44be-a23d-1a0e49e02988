/*
 * @copyright (c) 2016, <PERSON> & <PERSON><PERSON><PERSON>
 * @copyright (c) 2007-present, <PERSON> & <PERSON>
 * @license BSD-3-<PERSON><PERSON> (see LICENSE in the root directory of this source tree)
 */

/**
 * @private
 */
export class ParsePosition {
    constructor(index) {
        this._index = index;
        this._errorIndex = -1;
    }

    getIndex(){
        return this._index;
    }

    setIndex(index){
        this._index = index;
    }

    getErrorIndex(){
        return this._errorIndex;
    }

    setErrorIndex(errorIndex){
        this._errorIndex = errorIndex;
    }
}
