/* <PERSON>er & Footer Styles */

/* Header Styles */
.header {
    background-color: #4A90E2;
    color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header-top {
    padding: 15px 0;
}

.logo h1 {
    color: #fff;
    font-size: 24px;
    margin: 0;
    font-weight: bold;
}

.navbar-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-menu li {
    margin-left: 20px;
}

.navbar-menu a {
    color: #fff;
    font-size: 16px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.navbar-menu a:hover {
    color: #f0f0f0;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
}

.dropdown-toggle i {
    margin-left: 5px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    min-width: 180px;
    display: none;
    z-index: 10;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    color: #333;
    padding: 10px 15px;
    display: block;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-menu a:hover {
    background-color: #f5f7fa;
    color: #4A90E2;
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.auth-link {
    color: #fff;
    font-weight: bold;
}

/* Footer Styles */
.footer {
    background-color: #fff;
    padding: 40px 0 20px;
    border-top: 1px solid #eee;
}

.footer-top {
    margin-bottom: 30px;
}

.footer-logo h2 {
    color: #4A90E2;
    font-size: 24px;
    margin-bottom: 10px;
}

.footer-logo p {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f5f7fa;
    border-radius: 50%;
    color: #4A90E2;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: #4A90E2;
    color: #fff;
}

.footer-sections {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #666;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #4A90E2;
}

.footer-bottom {
    border-top: 1px solid #eee;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    color: #999;
    font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .header-top {
        padding: 10px 0;
    }
    
    .navbar-toggle {
        display: block;
    }
    
    .navbar-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #4A90E2;
        padding: 20px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-menu.active {
        display: flex;
    }
    
    .navbar-menu li {
        margin: 10px 0;
    }
    
    .dropdown-menu {
        position: static;
        box-shadow: none;
        background-color: transparent;
        display: none;
        padding-left: 15px;
    }
    
    .dropdown.active .dropdown-menu {
        display: block;
    }
    
    .dropdown-menu a {
        color: #fff;
        border-bottom: none;
        padding: 8px 0;
    }
    
    .dropdown-menu a:hover {
        background-color: transparent;
        color: #f0f0f0;
    }
    
    .footer-sections {
        flex-direction: column;
        gap: 20px;
    }
    
    .footer-section {
        width: 100%;
    }
}

/* Header Layout */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar {
    display: flex;
    align-items: center;
}

.navbar-toggle {
    display: none;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
}

.navbar-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: #fff;
    margin: 5px 0;
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .navbar-toggle {
        display: block;
    }
    
    .navbar-menu {
        display: none;
    }
    
    .navbar-menu.active {
        display: flex;
    }
    
    .header-content {
        flex-wrap: wrap;
    }
}
