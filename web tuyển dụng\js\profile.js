// Profile JavaScript file

// API URL
const API_URL = 'http://localhost:5000/api';

// DOM Elements
const profileList = document.getElementById('profile-list');
const cvList = document.getElementById('cv-list');
const profileForm = document.getElementById('profile-form');
const cvForm = document.getElementById('cv-form');
const profileSearchForm = document.getElementById('profile-search-form');
const profileSearchInput = document.getElementById('profile-search-input');
const profileFilters = document.querySelectorAll('.profile-filter');
const profileDetailModal = document.getElementById('profile-detail-modal');
const profileDetailContent = document.getElementById('profile-detail-content');
const cvDetailModal = document.getElementById('cv-detail-modal');
const cvDetailContent = document.getElementById('cv-detail-content');

// Get user profiles
async function getProfiles(filters = {}) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            return [];
        }
        
        // Build query string from filters
        const queryParams = new URLSearchParams();
        
        if (filters.search) {
            queryParams.append('search', filters.search);
        }
        
        if (filters.skills) {
            queryParams.append('skills', filters.skills);
        }
        
        if (filters.experience) {
            queryParams.append('experience', filters.experience);
        }
        
        if (filters.education) {
            queryParams.append('education', filters.education);
        }
        
        const queryString = queryParams.toString();
        const url = queryString ? `${API_URL}/profiles?${queryString}` : `${API_URL}/profiles`;
        
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch profiles');
        }
    } catch (error) {
        console.error('Error fetching profiles:', error);
        return [];
    }
}

// Get profile by ID
async function getProfileById(profileId) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            return null;
        }
        
        const response = await fetch(`${API_URL}/profiles/${profileId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch profile details');
        }
    } catch (error) {
        console.error('Error fetching profile details:', error);
        return null;
    }
}

// Create or update profile
async function saveProfile(profileData, profileId = null) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để thực hiện chức năng này');
            return false;
        }
        
        const method = profileId ? 'PUT' : 'POST';
        const url = profileId ? `${API_URL}/profiles/${profileId}` : `${API_URL}/profiles`;
        
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(profileData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert(profileId ? 'Cập nhật hồ sơ thành công!' : 'Tạo hồ sơ thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Không thể lưu hồ sơ');
        }
    } catch (error) {
        console.error('Error saving profile:', error);
        alert(error.message);
        return false;
    }
}

// Delete profile
async function deleteProfile(profileId) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để thực hiện chức năng này');
            return false;
        }
        
        const confirmed = confirm('Bạn có chắc chắn muốn xóa hồ sơ này?');
        if (!confirmed) {
            return false;
        }
        
        const response = await fetch(`${API_URL}/profiles/${profileId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Xóa hồ sơ thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Không thể xóa hồ sơ');
        }
    } catch (error) {
        console.error('Error deleting profile:', error);
        alert(error.message);
        return false;
    }
}

// Get user CVs
async function getCVs() {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            return [];
        }
        
        const response = await fetch(`${API_URL}/cvs`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch CVs');
        }
    } catch (error) {
        console.error('Error fetching CVs:', error);
        return [];
    }
}

// Get CV by ID
async function getCVById(cvId) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            return null;
        }
        
        const response = await fetch(`${API_URL}/cvs/${cvId}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            return data;
        } else {
            throw new Error(data.message || 'Failed to fetch CV details');
        }
    } catch (error) {
        console.error('Error fetching CV details:', error);
        return null;
    }
}

// Create or update CV
async function saveCV(cvData, cvId = null) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để thực hiện chức năng này');
            return false;
        }
        
        const method = cvId ? 'PUT' : 'POST';
        const url = cvId ? `${API_URL}/cvs/${cvId}` : `${API_URL}/cvs`;
        
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(cvData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert(cvId ? 'Cập nhật CV thành công!' : 'Tạo CV thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Không thể lưu CV');
        }
    } catch (error) {
        console.error('Error saving CV:', error);
        alert(error.message);
        return false;
    }
}

// Delete CV
async function deleteCV(cvId) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để thực hiện chức năng này');
            return false;
        }
        
        const confirmed = confirm('Bạn có chắc chắn muốn xóa CV này?');
        if (!confirmed) {
            return false;
        }
        
        const response = await fetch(`${API_URL}/cvs/${cvId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            alert('Xóa CV thành công!');
            return true;
        } else {
            throw new Error(data.message || 'Không thể xóa CV');
        }
    } catch (error) {
        console.error('Error deleting CV:', error);
        alert(error.message);
        return false;
    }
}

// Render profiles
function renderProfiles(profiles) {
    if (!profileList) return;
    
    // Clear profile list
    profileList.innerHTML = '';
    
    if (profiles.length === 0) {
        profileList.innerHTML = '<div class="no-profiles">Không có hồ sơ nào</div>';
        return;
    }
    
    // Create profile cards
    profiles.forEach(profile => {
        const profileCard = document.createElement('div');
        profileCard.className = 'profile-card';
        
        // Create skills HTML
        const skillsHTML = profile.skills.map(skill => `<span>${skill}</span>`).join('');
        
        profileCard.innerHTML = `
            <div class="profile-card-header">
                <h3>${profile.title}</h3>
                <p>${profile.name}</p>
            </div>
            <div class="profile-card-body">
                <div class="profile-card-info">
                    <p><i class="fas fa-map-marker-alt"></i> ${profile.location}</p>
                    <p><i class="fas fa-briefcase"></i> ${profile.experience} năm kinh nghiệm</p>
                    <p><i class="fas fa-graduation-cap"></i> ${profile.education}</p>
                    <p><i class="fas fa-phone"></i> ${profile.phone}</p>
                    <p><i class="fas fa-envelope"></i> ${profile.email}</p>
                </div>
                <div class="profile-card-skills">
                    ${skillsHTML}
                </div>
                <div class="profile-card-actions">
                    <button class="profile-card-edit" data-profile-id="${profile._id}">Chỉnh sửa</button>
                    <button class="profile-card-view" data-profile-id="${profile._id}">Xem chi tiết</button>
                </div>
            </div>
        `;
        
        // Add event listeners
        const editBtn = profileCard.querySelector('.profile-card-edit');
        editBtn.addEventListener('click', () => {
            showProfileForm(profile._id);
        });
        
        const viewBtn = profileCard.querySelector('.profile-card-view');
        viewBtn.addEventListener('click', () => {
            showProfileDetail(profile._id);
        });
        
        profileList.appendChild(profileCard);
    });
}

// Render CVs
function renderCVs(cvs) {
    if (!cvList) return;
    
    // Clear CV list
    cvList.innerHTML = '';
    
    if (cvs.length === 0) {
        cvList.innerHTML = '<div class="no-cvs">Không có CV nào</div>';
        return;
    }
    
    // Create CV cards
    cvs.forEach(cv => {
        const cvCard = document.createElement('div');
        cvCard.className = 'cv-card';
        
        // Create skills HTML
        const skillsHTML = cv.skills.map(skill => `<span>${skill}</span>`).join('');
        
        cvCard.innerHTML = `
            <h4>${cv.title}</h4>
            <div class="cv-card-section">
                <h5>Thông tin cá nhân</h5>
                <p>Họ tên: ${cv.name}</p>
                <p>Email: ${cv.email}</p>
                <p>Số điện thoại: ${cv.phone}</p>
                <p>Địa chỉ: ${cv.address}</p>
            </div>
            <div class="cv-card-section">
                <h5>Học vấn</h5>
                <p>${cv.education}</p>
            </div>
            <div class="cv-card-section">
                <h5>Kinh nghiệm</h5>
                <p>${cv.experience}</p>
            </div>
            <div class="cv-card-section">
                <h5>Kỹ năng</h5>
                <div class="cv-card-skills">
                    ${skillsHTML}
                </div>
            </div>
            <div class="cv-card-actions">
                <button class="cv-card-edit" data-cv-id="${cv._id}">Chỉnh sửa</button>
                <button class="cv-card-download" data-cv-id="${cv._id}">Tải xuống</button>
                <button class="cv-card-delete" data-cv-id="${cv._id}">Xóa</button>
            </div>
        `;
        
        // Add event listeners
        const editBtn = cvCard.querySelector('.cv-card-edit');
        editBtn.addEventListener('click', () => {
            showCVForm(cv._id);
        });
        
        const downloadBtn = cvCard.querySelector('.cv-card-download');
        downloadBtn.addEventListener('click', () => {
            downloadCV(cv._id);
        });
        
        const deleteBtn = cvCard.querySelector('.cv-card-delete');
        deleteBtn.addEventListener('click', async () => {
            const success = await deleteCV(cv._id);
            if (success) {
                // Refresh CV list
                const cvs = await getCVs();
                renderCVs(cvs);
            }
        });
        
        cvList.appendChild(cvCard);
    });
}

// Show profile form
async function showProfileForm(profileId = null) {
    try {
        // Get profile modal
        const profileModal = document.getElementById('profile-modal');
        if (!profileModal) return;
        
        // Get form elements
        const titleInput = document.getElementById('profile-title');
        const nameInput = document.getElementById('profile-name');
        const emailInput = document.getElementById('profile-email');
        const phoneInput = document.getElementById('profile-phone');
        const locationInput = document.getElementById('profile-location');
        const experienceInput = document.getElementById('profile-experience');
        const educationInput = document.getElementById('profile-education');
        const skillsInput = document.getElementById('profile-skills');
        const summaryInput = document.getElementById('profile-summary');
        const profileIdInput = document.getElementById('profile-id');
        
        // Clear form
        if (titleInput) titleInput.value = '';
        if (nameInput) nameInput.value = '';
        if (emailInput) emailInput.value = '';
        if (phoneInput) phoneInput.value = '';
        if (locationInput) locationInput.value = '';
        if (experienceInput) experienceInput.value = '';
        if (educationInput) educationInput.value = '';
        if (skillsInput) skillsInput.value = '';
        if (summaryInput) summaryInput.value = '';
        if (profileIdInput) profileIdInput.value = '';
        
        // Set form title
        const formTitle = profileModal.querySelector('h2');
        if (formTitle) {
            formTitle.textContent = profileId ? 'Chỉnh sửa hồ sơ' : 'Tạo hồ sơ mới';
        }
        
        // If editing, get profile data
        if (profileId) {
            const profile = await getProfileById(profileId);
            
            if (profile) {
                if (titleInput) titleInput.value = profile.title;
                if (nameInput) nameInput.value = profile.name;
                if (emailInput) emailInput.value = profile.email;
                if (phoneInput) phoneInput.value = profile.phone;
                if (locationInput) locationInput.value = profile.location;
                if (experienceInput) experienceInput.value = profile.experience;
                if (educationInput) educationInput.value = profile.education;
                if (skillsInput) skillsInput.value = profile.skills.join(', ');
                if (summaryInput) summaryInput.value = profile.summary;
                if (profileIdInput) profileIdInput.value = profile._id;
            }
        }
        
        // Show modal
        profileModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    } catch (error) {
        console.error('Error showing profile form:', error);
        alert('Không thể tải thông tin hồ sơ');
    }
}

// Show CV form
async function showCVForm(cvId = null) {
    try {
        // Get CV modal
        const cvModal = document.getElementById('cv-modal');
        if (!cvModal) return;
        
        // Get form elements
        const titleInput = document.getElementById('cv-title');
        const nameInput = document.getElementById('cv-name');
        const emailInput = document.getElementById('cv-email');
        const phoneInput = document.getElementById('cv-phone');
        const addressInput = document.getElementById('cv-address');
        const educationInput = document.getElementById('cv-education');
        const experienceInput = document.getElementById('cv-experience');
        const skillsInput = document.getElementById('cv-skills');
        const objectiveInput = document.getElementById('cv-objective');
        const cvIdInput = document.getElementById('cv-id');
        
        // Clear form
        if (titleInput) titleInput.value = '';
        if (nameInput) nameInput.value = '';
        if (emailInput) emailInput.value = '';
        if (phoneInput) phoneInput.value = '';
        if (addressInput) addressInput.value = '';
        if (educationInput) educationInput.value = '';
        if (experienceInput) experienceInput.value = '';
        if (skillsInput) skillsInput.value = '';
        if (objectiveInput) objectiveInput.value = '';
        if (cvIdInput) cvIdInput.value = '';
        
        // Set form title
        const formTitle = cvModal.querySelector('h2');
        if (formTitle) {
            formTitle.textContent = cvId ? 'Chỉnh sửa CV' : 'Tạo CV mới';
        }
        
        // If editing, get CV data
        if (cvId) {
            const cv = await getCVById(cvId);
            
            if (cv) {
                if (titleInput) titleInput.value = cv.title;
                if (nameInput) nameInput.value = cv.name;
                if (emailInput) emailInput.value = cv.email;
                if (phoneInput) phoneInput.value = cv.phone;
                if (addressInput) addressInput.value = cv.address;
                if (educationInput) educationInput.value = cv.education;
                if (experienceInput) experienceInput.value = cv.experience;
                if (skillsInput) skillsInput.value = cv.skills.join(', ');
                if (objectiveInput) objectiveInput.value = cv.objective;
                if (cvIdInput) cvIdInput.value = cv._id;
            }
        }
        
        // Show modal
        cvModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    } catch (error) {
        console.error('Error showing CV form:', error);
        alert('Không thể tải thông tin CV');
    }
}

// Show profile detail
async function showProfileDetail(profileId) {
    try {
        // Get profile details
        const profile = await getProfileById(profileId);
        
        if (!profile) {
            alert('Không thể tải thông tin hồ sơ');
            return;
        }
        
        // Create skills HTML
        const skillsHTML = profile.skills.map(skill => `<span>${skill}</span>`).join('');
        
        // Update modal content
        profileDetailContent.innerHTML = `
            <div class="profile-detail-header">
                <h2>${profile.title}</h2>
                <p>${profile.name}</p>
            </div>
            <div class="profile-detail-section">
                <h3>Thông tin cá nhân</h3>
                <p><strong>Email:</strong> ${profile.email}</p>
                <p><strong>Số điện thoại:</strong> ${profile.phone}</p>
                <p><strong>Địa điểm:</strong> ${profile.location}</p>
                <p><strong>Kinh nghiệm:</strong> ${profile.experience} năm</p>
                <p><strong>Học vấn:</strong> ${profile.education}</p>
            </div>
            <div class="profile-detail-section">
                <h3>Tóm tắt</h3>
                <p>${profile.summary}</p>
            </div>
            <div class="profile-detail-section">
                <h3>Kỹ năng</h3>
                <div class="profile-detail-skills">
                    ${skillsHTML}
                </div>
            </div>
            <div class="profile-detail-actions">
                <button class="profile-detail-edit" data-profile-id="${profile._id}">Chỉnh sửa</button>
                <button class="profile-detail-close">Đóng</button>
            </div>
        `;
        
        // Add event listener to edit button
        const editBtn = profileDetailContent.querySelector('.profile-detail-edit');
        editBtn.addEventListener('click', () => {
            // Hide profile detail modal
            profileDetailModal.style.display = 'none';
            
            // Show profile form
            showProfileForm(profile._id);
        });
        
        // Add event listener to close button
        const closeBtn = profileDetailContent.querySelector('.profile-detail-close');
        closeBtn.addEventListener('click', () => {
            profileDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
        
        // Show modal
        profileDetailModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    } catch (error) {
        console.error('Error showing profile detail:', error);
        alert('Không thể tải thông tin hồ sơ');
    }
}

// Download CV
async function downloadCV(cvId) {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để thực hiện chức năng này');
            return;
        }
        
        // Redirect to download endpoint
        window.open(`${API_URL}/cvs/${cvId}/download?token=${token}`, '_blank');
    } catch (error) {
        console.error('Error downloading CV:', error);
        alert('Không thể tải xuống CV');
    }
}

// Initialize profile page
async function initProfilePage() {
    try {
        // Check if user is logged in
        const token = localStorage.getItem('token');
        if (!token) {
            alert('Vui lòng đăng nhập để xem hồ sơ của bạn');
            window.location.href = '/';
            return;
        }
        
        // Get profiles and CVs
        const profiles = await getProfiles();
        const cvs = await getCVs();
        
        // Render profiles and CVs
        renderProfiles(profiles);
        renderCVs(cvs);
        
        // Initialize search and filters
        initProfileSearch();
    } catch (error) {
        console.error('Error initializing profile page:', error);
    }
}

// Initialize profile search and filters
function initProfileSearch() {
    // Profile search form submission
    if (profileSearchForm) {
        profileSearchForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const filters = {
                search: profileSearchInput.value
            };
            
            // Add filter values
            profileFilters.forEach(filter => {
                if (filter.value) {
                    filters[filter.name] = filter.value;
                }
            });
            
            const profiles = await getProfiles(filters);
            renderProfiles(profiles);
        });
    }
    
    // Filter change events
    profileFilters.forEach(filter => {
        filter.addEventListener('change', async () => {
            const filters = {
                search: profileSearchInput ? profileSearchInput.value : ''
            };
            
            // Add filter values
            profileFilters.forEach(f => {
                if (f.value) {
                    filters[f.name] = f.value;
                }
            });
            
            const profiles = await getProfiles(filters);
            renderProfiles(profiles);
        });
    });
}

// Profile form submission
if (profileForm) {
    profileForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const profileId = document.getElementById('profile-id').value;
        const title = document.getElementById('profile-title').value;
        const name = document.getElementById('profile-name').value;
        const email = document.getElementById('profile-email').value;
        const phone = document.getElementById('profile-phone').value;
        const location = document.getElementById('profile-location').value;
        const experience = document.getElementById('profile-experience').value;
        const education = document.getElementById('profile-education').value;
        const skillsString = document.getElementById('profile-skills').value;
        const summary = document.getElementById('profile-summary').value;
        
        // Convert skills string to array
        const skills = skillsString.split(',').map(skill => skill.trim()).filter(skill => skill);
        
        const profileData = {
            title,
            name,
            email,
            phone,
            location,
            experience,
            education,
            skills,
            summary
        };
        
        const success = await saveProfile(profileData, profileId || null);
        
        if (success) {
            // Close modal
            const profileModal = document.getElementById('profile-modal');
            if (profileModal) {
                profileModal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
            
            // Refresh profile list
            const profiles = await getProfiles();
            renderProfiles(profiles);
        }
    });
}

// CV form submission
if (cvForm) {
    cvForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const cvId = document.getElementById('cv-id').value;
        const title = document.getElementById('cv-title').value;
        const name = document.getElementById('cv-name').value;
        const email = document.getElementById('cv-email').value;
        const phone = document.getElementById('cv-phone').value;
        const address = document.getElementById('cv-address').value;
        const education = document.getElementById('cv-education').value;
        const experience = document.getElementById('cv-experience').value;
        const skillsString = document.getElementById('cv-skills').value;
        const objective = document.getElementById('cv-objective').value;
        
        // Convert skills string to array
        const skills = skillsString.split(',').map(skill => skill.trim()).filter(skill => skill);
        
        const cvData = {
            title,
            name,
            email,
            phone,
            address,
            education,
            experience,
            skills,
            objective
        };
        
        const success = await saveCV(cvData, cvId || null);
        
        if (success) {
            // Close modal
            const cvModal = document.getElementById('cv-modal');
            if (cvModal) {
                cvModal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
            
            // Refresh CV list
            const cvs = await getCVs();
            renderCVs(cvs);
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Check if we're on the profile page
    if (profileList || cvList) {
        initProfilePage();
    }
    
    // Close profile detail modal
    const closeProfileDetail = document.querySelector('#profile-detail-modal .close');
    if (closeProfileDetail) {
        closeProfileDetail.addEventListener('click', () => {
            profileDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
    }
    
    // Close CV detail modal
    const closeCVDetail = document.querySelector('#cv-detail-modal .close');
    if (closeCVDetail) {
        closeCVDetail.addEventListener('click', () => {
            cvDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        });
    }
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === profileDetailModal) {
            profileDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        if (e.target === cvDetailModal) {
            cvDetailModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
});
