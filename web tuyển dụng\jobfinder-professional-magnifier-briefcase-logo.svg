<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="briefcaseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="8" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="8" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Decorative Elements -->
  <circle cx="75" cy="25" r="12" fill="#10b981" opacity="0.2"/>
  <circle cx="25" cy="75" r="15" fill="#10b981" opacity="0.1"/>
  
  <!-- Magnifying Glass -->
  <g transform="translate(20, 25) scale(0.9)">
    <!-- Glass Handle -->
    <rect x="30" y="25" width="20" height="8" rx="4" fill="white" transform="rotate(45, 30, 25)"/>
    
    <!-- Glass Circle -->
    <circle cx="20" cy="20" r="15" fill="none" stroke="white" stroke-width="4"/>
    
    <!-- Glass Reflection -->
    <path d="M12,15 Q20,25 28,15" stroke="white" stroke-width="1.5" fill="none"/>
    
    <!-- Glass Interior -->
    <circle cx="20" cy="20" r="12" fill="url(#glassGradient)" opacity="0.2"/>
  </g>
  
  <!-- Briefcase -->
  <g transform="translate(50, 35)">
    <!-- Briefcase Body -->
    <rect x="0" y="10" width="30" height="25" rx="3" fill="url(#briefcaseGradient)"/>
    
    <!-- Briefcase Top -->
    <rect x="5" y="0" width="20" height="10" rx="2" fill="url(#briefcaseGradient)"/>
    
    <!-- Briefcase Handle -->
    <rect x="12" y="5" width="6" height="3" rx="1.5" fill="#064e3b"/>
    
    <!-- Briefcase Details -->
    <rect x="5" y="15" width="20" height="1" fill="#064e3b" opacity="0.5"/>
    <rect x="5" y="20" width="20" height="1" fill="#064e3b" opacity="0.5"/>
    <rect x="5" y="25" width="20" height="1" fill="#064e3b" opacity="0.5"/>
    <rect x="5" y="30" width="20" height="1" fill="#064e3b" opacity="0.5"/>
  </g>
  
  <!-- Reflective Highlight -->
  <path d="M10,10 L90,10 L90,30 Q50,40 10,30 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
  
  <!-- Bottom Accent -->
  <rect x="10" y="85" width="80" height="5" rx="2.5" fill="url(#glassGradient)" opacity="0.8"/>
</svg>
