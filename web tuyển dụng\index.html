<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="JobFinder - Nơi kết nối bạn với cơ hội việc làm phù hợp nhất từ các công ty hàng đầu tại Việt Nam.">
    <meta name="keywords" content="việc làm, tuyển dụng, <PERSON><PERSON><PERSON>, công vi<PERSON><PERSON>, ngh<PERSON> nghi<PERSON>, t<PERSON><PERSON> vi<PERSON>, h<PERSON> sơ, <PERSON><PERSON>">
    <title>JobFinder - Tìm kiếm việc làm</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="stylesheet" href="css/job-styles.css">
    <link rel="stylesheet" href="css/recommended-jobs.css">
    <link rel="stylesheet" href="css/job-poster.css">
    <link rel="stylesheet" href="css/job-card-compact.css">
    <link rel="stylesheet" href="css/job-card-horizontal.css">
    <link rel="stylesheet" href="css/advanced-search.css">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(90deg, #ecfdf5, #e1e9e5, #52d296);
            padding: 0px 0;
            min-height: 60px;
            position: sticky;
            top: 0;
            z-index: 90;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo .logo-icon {
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            vertical-align: middle;
            filter: drop-shadow(2px 3px 5px rgba(0, 0, 0, 0.2));
            border-radius: 6px;
            padding: 0;
            background: linear-gradient(135deg, #296845, #15774b);
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .logo .logo-icon i {
            font-size: 24px;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
            margin-left: 10px;
        }

        .logo-text span {
            position: relative;
            display: inline-flex;
            align-items: center;
            text-transform: uppercase;
        }

        .logo-text .job {
            font-family: 'Poppins', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 32px;
            font-weight: 900;
            letter-spacing: 1px;
            background: linear-gradient(to right, #064e3b, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo-text .finder {
            font-family: 'Poppins', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-left: 2px;
            color: #047857;
            position: relative;
            top: 1px;
        }

        .logo-text span::after {
            content: '';
            position: absolute;
            right: -90px;
            top: 50%;
            transform: translateY(-50%);
            width: 80px;
            height: 50px;
            background-image: url('jobfinder-dots-pattern-final.svg');
            background-repeat: no-repeat;
            background-size: contain;
            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.1));
        }

        .logo-text .est {
            font-size: 11px;
            font-weight: 600;
            color: rgba(6, 78, 59, 0.8);
            letter-spacing: 0.8px;
            margin-top: 2px;
            text-transform: uppercase;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .logo hr {
            display: none;
        }

        .nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-links a {
            color: #064e3b;
            text-decoration: none;
            font-size: 15px;
            transition: all 0.3s ease;
            padding: 20px 15px;
            position: relative;
            font-weight: 600;
            opacity: 0.9;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: #047857;
            opacity: 1;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background-color: #047857;
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after,
        .nav-links a.active::after {
            width: 30px;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-buttons a, .nav-buttons button {
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 14px;
            text-decoration: none;
            transition: background-color 0.3s ease, transform 0.2s ease;
            border: none;
            cursor: pointer;
        }

        .nav-buttons .login-btn {
            background-color: transparent;
            color: #064e3b;
            border: 1px solid rgba(6, 78, 59, 0.5);
        }

        .nav-buttons .login-btn:hover {
            background-color: rgba(6, 78, 59, 0.05);
            transform: translateY(-2px);
            border-color: #064e3b;
        }

        .nav-buttons .register-btn {
            background-color: #065f46;
            color: #ffffff;
            font-weight: 700;
        }

        .nav-buttons .register-btn:hover {
            background-color: #047857;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(6, 78, 59, 0.2);
        }

        /* Hero Section */
        .hero {
            background-image: url('https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
            color: #064e3b;
            text-align: center;
            padding: 80px 0;
            border-bottom: 4px solid #a7f3d0;
            min-height: 400px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(234, 249, 242, 0.85), rgba(209, 250, 229, 0.85));
            backdrop-filter: blur(2px);
            z-index: 1;
        }

        .hero .container {
            position: relative;
            z-index: 2;
        }

        @keyframes shineEffect {
            0% {
                background-position: -100% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .hero h1 {
            font-size: 40px;
            font-weight: 800;
            margin-bottom: 25px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: -0.5px;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.2;
            background: linear-gradient(90deg,
                #064e3b 0%,
                #047857 25%,
                #10b981 35%,
                #ffffff 45%,
                #10b981 55%,
                #047857 65%,
                #064e3b 100%);
            background-size: 200% auto;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
            width: 100%;
            text-align: center;
            animation: shineEffect 5s ease-in-out infinite;
        }

        .search-bar {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 2;
        }

        .search-bar input {
            width: 550px;
            padding: 18px 28px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .search-bar input:focus {
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.25);
            transform: translateY(-2px);
            border-color: rgba(99, 102, 241, 0.3);
        }

        .search-btn {
            background-color: #047857;
            color: #fff;
            padding: 18px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 15px rgba(4, 120, 87, 0.15);
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            letter-spacing: 0.5px;
        }

        .search-btn:hover {
            background-color: #065f46;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(4, 120, 87, 0.25);
        }

        .search-btn i {
            margin-right: 10px;
        }

        /* Filters */
        .filters {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 5px;
        }

        .filters select {
            padding: 12px 24px 12px 20px;
            border: 1px solid rgba(6, 78, 59, 0.1);
            border-radius: 10px;
            font-size: 15px;
            background-color: rgba(255, 255, 255, 0.9);
            color: #064e3b;
            cursor: pointer;
            outline: none;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-weight: 500;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23064e3b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
            background-repeat: no-repeat;
            background-position: right 10px center;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .filters select:hover {
            box-shadow: 0 6px 15px rgba(6, 78, 59, 0.1);
            transform: translateY(-2px);
            border-color: rgba(6, 78, 59, 0.2);
        }

        /* Featured Jobs Section */
        .featured-jobs {
            padding: 40px 0;
            background-color: #f5f7fa;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .featured-jobs h2 {
            text-align: center;
            font-size: 32px;
            margin-bottom: 30px;
            color: #ffffff;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .job-list {
            max-height: 600px;
            overflow-y: auto;
            padding: 0 10px;
        }

        .job-list::-webkit-scrollbar {
            width: 8px;
        }

        .job-list::-webkit-scrollbar-thumb {
            background-color: #4A90E2;
            border-radius: 4px;
        }

        .job-list::-webkit-scrollbar-track {
            background-color: #e0e0e0;
        }

        .job-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .job-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4A90E2;
        }

        .job-item img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            margin-right: 20px;
            border-radius: 8px;
            padding: 5px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            transition: transform 0.3s ease;
        }

        .job-item:hover img {
            transform: scale(1.05);
        }

        .job-info {
            flex: 1;
        }

        .job-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            transition: color 0.3s ease;
        }

        .job-item:hover .job-info h3 {
            color: #4A90E2;
        }

        .job-info .company {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .job-info .level {
            font-size: 12px;
            color: #999;
            margin-bottom: 5px;
        }

        .job-info .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 5px;
        }

        .job-info .skills span {
            background-color: #f0f7ff;
            color: #4A90E2;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        .job-info .skills span:hover {
            background-color: #4A90E2;
            color: white;
        }

        .job-info .details {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            color: #666;
        }

        .job-info .details i {
            color: #4A90E2;
            margin-right: 5px;
        }

        .job-info .details .days-left {
            color: #28a745;
        }

        .job-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .job-actions .salary {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        .job-actions .apply-btn {
            background-color: #4A90E2;
            color: #fff;
            padding: 10px 18px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(74, 144, 226, 0.3);
        }

        .job-actions .apply-btn:hover {
            background-color: #3a7bc8;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(74, 144, 226, 0.4);
        }

        .job-actions .favorite {
            color: #ccc;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 50%;
            background-color: transparent;
        }

        .job-actions .favorite:hover {
            color: #ff4d4f;
            background-color: rgba(255, 77, 79, 0.1);
            transform: scale(1.1);
        }

        .job-tag {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: #ff4d4f;
            color: #fff;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .job-item {
            position: relative;
        }

        /* Section Header Styles */
        .section-header {
            text-align: center;
            margin-bottom: 25px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
        }

        .section-divider {
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, #10b981, #064e3b);
            margin: 10px auto 0;
            border-radius: 3px;
        }

        .section-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: #064e3b;
            margin-bottom: 6px;
            position: relative;
            display: inline-block;
            padding-bottom: 6px;
        }

        .section-header h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, #064e3b, #10b981);
            border-radius: 3px;
        }

        .section-subtitle {
            font-size: 14px;
            color: #4b5563;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.4;
        }

        /* Benefits Section */
        .benefits {
            padding: 40px 0;
            background: #ffffff;
            position: relative;
            z-index: 1;
        }

        .benefits-wrapper {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
            max-width: 1000px;
            margin: 0 auto;
        }

        .benefit-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            padding: 25px 15px;
            width: calc(25% - 20px);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-bottom: 3px solid #10b981;
            border: 1px solid rgba(0, 0, 0, 0.08);
        }

        .benefit-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #064e3b, #10b981);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(16, 185, 129, 0.2);
            background-color: #fafffe;
        }

        .benefit-card:hover::before {
            opacity: 1;
        }

        .benefit-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #10b981, #064e3b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            transition: all 0.3s ease;
        }

        .benefit-icon i {
            font-size: 22px;
            color: #ffffff;
            transition: all 0.3s ease;
        }

        .benefit-card:hover .benefit-icon {
            background: linear-gradient(135deg, #064e3b, #10b981);
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
        }

        .benefit-card:hover .benefit-icon i {
            color: white;
            transform: scale(1.1);
        }

        .benefit-card h3 {
            font-size: 18px;
            font-weight: 700;
            color: #064e3b;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }

        .benefit-card h3::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background-color: #10b981;
            transition: width 0.3s ease;
        }

        .benefit-card:hover h3::after {
            width: 60px;
        }

        .benefit-card p {
            font-size: 15px;
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* Stats Section */
        .stats-section {
            padding: 15px 0;
            background: #064e3b;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            perspective: 1000px;
        }

        .stats-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cpath fill="%23ffffff10" d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z"%3E%3C/path%3E%3C/svg%3E');
            opacity: 0.1;
        }

        .stats-wrapper {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
            max-width: 800px;
            margin: 0 auto;
            transform-style: preserve-3d;
        }

        .stat-item {
            text-align: center;
            padding: 10px 8px;
            width: calc(25% - 10px);
            position: relative;
            transition: all 0.4s ease;
            transform: translateZ(0);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .stat-item:hover {
            transform: translateY(-5px) translateZ(20px) rotateX(5deg);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-item::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 70%;
            width: 1px;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .stat-item:last-child::after {
            display: none;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 3px;
            background: linear-gradient(to right, #ffffff, #d1fae5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: inline-block;
            position: relative;
            transform: translateZ(10px);
            text-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }

        .stat-number::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.3);
            transition: width 0.3s ease;
        }

        .stat-item:hover .stat-number::after {
            width: 50px;
            background-color: rgba(255, 255, 255, 0.6);
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            transform: translateZ(5px);
            transition: all 0.3s ease;
        }

        .stat-item:hover .stat-label {
            color: rgba(255, 255, 255, 1);
            transform: translateZ(15px);
        }

        /* Testimonials Section */
        .testimonials-section {
            padding: 40px 0;
            background-color: #f9fafb;
            position: relative;
        }

        .testimonials-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Cpath fill="%2310b98105" d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z"%3E%3C/path%3E%3C/svg%3E');
            opacity: 0.5;
            z-index: 0;
        }

        .testimonials-wrapper {
            display: flex;
            justify-content: space-between;
            gap: 25px;
            flex-wrap: wrap;
            max-width: 1100px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .testimonial-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            padding: 20px;
            width: calc(33.333% - 25px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(16, 185, 129, 0.1);
        }

        .testimonial-card::before {
            content: '\201C';
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 80px;
            color: rgba(16, 185, 129, 0.1);
            font-family: Georgia, serif;
            line-height: 1;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
        }

        .testimonial-content {
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .testimonial-content p {
            font-size: 14px;
            line-height: 1.6;
            color: #4b5563;
            font-style: italic;
            position: relative;
            padding-left: 5px;
            max-height: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .testimonial-content p::before {
            content: '\201C';
            font-size: 24px;
            color: #10b981;
            position: absolute;
            left: -10px;
            top: -5px;
        }

        .testimonial-content p::after {
            content: '\201D';
            font-size: 24px;
            color: #10b981;
            position: absolute;
            margin-left: 5px;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .author-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #10b981;
            box-shadow: 0 3px 10px rgba(16, 185, 129, 0.2);
        }

        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author-info h4 {
            font-size: 15px;
            font-weight: 700;
            color: #064e3b;
            margin-bottom: 2px;
            position: relative;
            display: inline-block;
        }

        .author-info h4::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background-color: #10b981;
            transition: width 0.3s ease;
        }

        .testimonial-card:hover .author-info h4::after {
            width: 100%;
        }

        .author-info p {
            font-size: 12px;
            color: #6b7280;
            font-style: normal;
        }

        /* Top Categories Section */
        .categories-section {
            padding: 30px 0;
            background-color: white;
            position: relative;
            overflow: hidden;
        }

        .categories-section h2 {
            color: #10b981;
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .categories-wrapper {
            display: flex;
            gap: 12px;
            position: relative;
            overflow: hidden;
            opacity: 1;
        }

        .category-card {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 15px 12px;
            width: calc(25% - 12px);
            min-width: 180px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .category-icon {
            width: 45px;
            height: 45px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .category-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .category-title {
            font-size: 13px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .job-count {
            font-size: 12px;
            color: #10b981;
            font-weight: 500;
        }

        .category-nav {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            gap: 10px;
        }

        .category-nav-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #64748b;
        }

        .category-nav-btn:hover {
            background-color: #10b981;
            color: white;
            border-color: #10b981;
        }

        /* Featured Companies Section */
        .partners-section {
            padding: 30px 0;
            background: linear-gradient(135deg, #f8fafc, #f0f9ff);
            position: relative;
            overflow: hidden;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .partners-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;charset=utf8,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%2310b98105" fill-opacity="0.05" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="3"/%3E%3Ccircle cx="13" cy="13" r="3"/%3E%3C/g%3E%3C/svg%3E');
            opacity: 0.5;
            z-index: 0;
        }

        .partners-description {
            text-align: center;
            max-width: 700px;
            margin: 0 auto 15px;
        }

        .partners-description p {
            font-size: 14px;
            color: #4b5563;
            line-height: 1.5;
        }

        .partners-container {
            position: relative;
            overflow: hidden;
            padding: 10px 0;
            margin: 0 auto;
            max-width: 1000px;
            mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
            -webkit-mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
        }

        .partners-track {
            display: flex;
            width: calc(180px * 18); /* Số lượng logo * kích thước mỗi logo */
            animation: scroll 40s linear infinite;
        }

        .partners-track:hover {
            animation-play-state: paused;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(calc(-180px * 9)); /* Một nửa số lượng logo */
            }
        }

        .partners-wrapper {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-shrink: 0;
        }

        .partner-logo {
            width: 150px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
            padding: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(16, 185, 129, 0.1);
            flex-shrink: 0;
            backdrop-filter: blur(5px);
        }

        @media (max-width: 1200px) {
            .partners-container {
                max-width: 800px;
            }
        }

        @media (max-width: 992px) {
            .partners-container {
                max-width: 700px;
            }
            .partner-logo {
                width: 130px;
                height: 55px;
            }
        }

        @media (max-width: 768px) {
            .partners-container {
                max-width: 600px;
            }
            .partner-logo {
                width: 120px;
                height: 50px;
            }
            .partner-logo img {
                max-height: 30px;
            }
        }

        @media (max-width: 576px) {
            .partners-container {
                max-width: 400px;
            }
            .partner-logo {
                width: 100px;
                height: 45px;
            }
            .partner-logo img {
                max-height: 25px;
            }
        }

        .partner-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #10b981, #064e3b);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .partner-logo::after {
            content: attr(data-name);
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
            z-index: 10;
        }

        .partner-logo:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            border-color: rgba(16, 185, 129, 0.3);
            background-color: rgba(255, 255, 255, 0.95);
        }

        .partner-logo:hover::after {
            opacity: 1;
            bottom: -20px;
        }

        .partner-logo:hover::before {
            opacity: 1;
        }

        .partner-logo img {
            max-width: 80%;
            max-height: 35px;
            object-fit: contain;
            transition: all 0.3s ease;
            filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.08));
        }

        .partner-logo:hover img {
            transform: scale(1.05);
            filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.12));
        }

        /* Employer Page */
        .employer-page {
            padding: 40px 0;
            background-color: #f5f7fa;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .employer-page h2 {
            text-align: center;
            font-size: 32px;
            margin-bottom: 30px;
            color: #4A90E2;
        }

        .employer-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            background-color: #e0e0e0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .tab-btn.active,
        .tab-btn:hover {
            background-color: #4A90E2;
            color: #fff;
        }

        .tab-content {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .tab-content h3 {
            color: #4A90E2;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #064e3b;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(6, 78, 59, 0.2);
            border-radius: 8px;
            font-size: 15px;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            color: #333;
            outline: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            background-color: rgba(255, 255, 255, 0.9);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #10b981;
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.15);
            transform: translateY(-1px);
            background-color: #fff;
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-style: italic;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .profile-list {
            margin-top: 20px;
        }

        .profile-item {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .profile-item h4 {
            color: #333;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .profile-item p {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .profile-item .modal-btn {
            padding: 8px 15px;
            font-size: 14px;
        }

        /* Profile Page */
        .profile-page {
            padding: 40px 0;
            background-color: #f5f7fa;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .profile-page h2 {
            text-align: center;
            font-size: 32px;
            margin-bottom: 30px;
            color: #4A90E2;
        }

        .profile-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .profile-actions .modal-btn {
            padding: 10px 20px;
            font-size: 16px;
        }

        .profile-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .profile-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .profile-card:hover {
            transform: translateY(-3px);
        }

        .profile-card h3 {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
        }

        .profile-card p {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .profile-card .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .profile-card .skills span {
            background-color: #e0e0e0;
            color: #333;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .profile-card .actions {
            display: flex;
            gap: 10px;
        }

        .profile-card .actions button {
            padding: 8px 15px;
            font-size: 14px;
        }

        .cv-details {
            margin-top: 20px;
        }

        .cv-details h4 {
            font-size: 18px;
            color: #4A90E2;
            margin-bottom: 10px;
        }

        .cv-details p {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        /* Modal Overrides for Profile/CV */
        .profile-modal .modal-content {
            max-width: 600px;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #054288, #0a66c2);
            color: #fff;
            padding: 30px 0 20px;
            margin-top: 40px;
            position: relative;
            overflow: hidden;
            background-image: url('https://img.freepik.com/free-vector/abstract-business-professional-background-banner-design-multipurpose_1340-16856.jpg');
            background-size: cover;
            background-position: center;
            background-blend-mode: overlay;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(5, 66, 136, 0.95), rgba(10, 102, 194, 0.95));
            z-index: 1;
            background-image: url('data:image/svg+xml;charset=utf8,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="rgba(255,255,255,0.05)" fill-rule="evenodd"/%3E%3C/svg%3E');
            background-size: 150px;
            opacity: 0.8;
        }

        .footer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, #4A90E2, #63B3ED, #4A90E2);
            z-index: 2;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            position: relative;
            z-index: 2;
            flex-wrap: wrap;
        }

        .footer-section {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .footer-section h4 {
            font-size: 16px;
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 8px;
            font-weight: 700;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(to right, #63B3ED, #10b981);
            border-radius: 2px;
        }

        .footer-section p {
            font-size: 13px;
            margin-bottom: 10px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .footer-section a {
            color: #fff;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            position: relative;
        }

        .footer-section a:hover {
            color: #63B3ED;
            transform: translateX(5px);
        }

        .footer-section a::before {
            content: '→';
            position: absolute;
            left: -20px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .footer-section a:hover::before {
            opacity: 1;
            left: -15px;
        }

        .footer-section i {
            margin-right: 8px;
            color: #63B3ED;
        }

        .social-links {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .social-links a {
            color: #fff;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .social-links a:hover {
            background-color: #63B3ED;
            transform: translateY(-5px);
            color: #fff;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
        }

        .social-links a i {
            font-size: 18px;
            transition: all 0.3s ease;
            margin-right: 0;
        }

        .social-links a:hover i {
            transform: scale(1.2);
        }

        /* Footer additional styles */
        .footer-job-stats {
            display: flex;
            gap: 10px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .footer-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 6px;
            min-width: 80px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .footer-stat-item:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.15);
        }

        .footer-stat-item i {
            font-size: 18px;
            margin-bottom: 6px;
            color: #63B3ED;
        }

        .footer-stat-item span {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 3px;
            color: #fff;
        }

        .footer-stat-item p {
            font-size: 11px;
            margin-bottom: 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-newsletter {
            margin-top: 25px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }

        .footer-newsletter h5 {
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .newsletter-form {
            display: flex;
            gap: 10px;
        }

        .newsletter-form input {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.9);
        }

        .newsletter-form button {
            background-color: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .newsletter-form button:hover {
            background-color: #0e9f6e;
            transform: translateY(-2px);
        }

        .footer-app-download {
            margin-top: 25px;
        }

        .footer-app-download h5 {
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .app-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .app-button {
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 6px 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .app-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .app-button i {
            font-size: 18px;
            margin-right: 8px;
        }

        .app-button-text {
            display: flex;
            flex-direction: column;
        }

        .app-button-text span {
            font-size: 9px;
            text-transform: uppercase;
        }

        .app-button-text strong {
            font-size: 12px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            margin-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
            position: relative;
            z-index: 2;
        }

        .footer-bottom p {
            margin: 0;
            font-size: 12px;
            opacity: 0.9;
        }

        .footer-bottom-links {
            display: flex;
            gap: 12px;
        }

        .footer-bottom-links a {
            color: #fff;
            font-size: 12px;
            opacity: 0.8;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-bottom-links a:hover {
            opacity: 1;
            color: #63B3ED;
        }

        .footer-job-illustration {
            position: absolute;
            bottom: 0;
            right: 5%;
            width: 200px;
            opacity: 0.2;
            z-index: 1;
            pointer-events: none;
        }

        .footer-job-illustration img {
            width: 100%;
            height: auto;
            filter: brightness(0) invert(1);
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
            }

            .footer-section {
                margin-bottom: 30px;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-bottom-links {
                justify-content: center;
            }

            .footer-job-illustration {
                display: none;
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fff;
            padding: 35px 40px;
            border-radius: 12px;
            width: 100%;
            max-width: 450px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease;
            border-top: 5px solid #10b981;
            overflow: hidden;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            color: #4b5563;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 50%;
            background-color: rgba(6, 78, 59, 0.05);
            z-index: 10;
        }

        .close:hover {
            color: #064e3b;
            background-color: rgba(6, 78, 59, 0.1);
            transform: rotate(90deg);
        }

        .modal-content h2 {
            color: #064e3b;
            text-align: center;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 700;
            position: relative;
            padding-bottom: 12px;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .modal-content h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(to right, #064e3b, #10b981);
            border-radius: 3px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            border-color: #4A90E2;
        }

        .modal-btn {
            width: 100%;
            padding: 14px 20px;
            background-color: #065f46;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(6, 95, 70, 0.2);
            letter-spacing: 0.5px;
            text-transform: uppercase;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            position: relative;
            overflow: hidden;
        }

        .modal-btn:hover {
            background-color: #047857;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(6, 95, 70, 0.3);
        }

        .modal-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(6, 95, 70, 0.2);
        }

        .modal-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .modal-btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }

        .modal-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #4b5563;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .modal-link a {
            color: #059669;
            text-decoration: none;
            font-weight: 600;
            position: relative;
            transition: all 0.3s ease;
        }

        .modal-link a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: #059669;
            transition: width 0.3s ease;
        }

        .modal-link a:hover {
            color: #10b981;
        }

        .modal-link a:hover::after {
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <div class="logo-text">
                    <span><span class="job">JOB</span><span class="finder">FINDER</span></span>
                    <span class="est">Tìm việc thông minh</span>
                </div>
            </div>
            <nav class="nav">
                <div class="nav-links">
                    <a href="#" onclick="showSection('home')" class="active">Trang chủ</a>
                    <a href="#" onclick="showSection('jobs')">Việc làm</a>
                    <a href="#" onclick="showSection('profile')">Hồ sơ & CV</a>
                    <a href="#" onclick="showSection('employer')">Đăng tuyển & Tìm hồ sơ</a>
                </div>
                <div class="nav-buttons">
                    <button class="login-btn">Đăng nhập</button>
                    <button class="register-btn">Đăng ký</button>
                </div>
            </nav>
        </div>
    </header>
    <hr>

    <!-- Home Section -->
    <div id="home">
        <!-- Hero Section with Search Bar -->
        <section class="hero">
            <div class="container">
                <h1>Tìm Kiếm Công Việc Trong Mơ Của Bạn</h1>
                <div class="search-bar">
                    <input type="text" id="main-search-input" placeholder="Nhập từ khóa (VD: Software Engineer, Hà Nội...)" aria-label="Tìm kiếm công việc">
                    <button class="search-btn" id="main-search-btn"><i class="fas fa-search"></i> Tìm kiếm</button>
                </div>
                <!-- Advanced Filters -->
                <div class="filters">
                    <select aria-label="Vị trí làm việc">
                        <option value="">Vị trí làm việc</option>
                        <option value="Software Engineering">Software Engineering</option>
                        <option value="AI Engineer">AI Engineer</option>
                        <option value="Information Security">Information Security</option>
                        <option value="Software development">Software development</option>
                        <option value="QA Engineer">QA Engineer</option>
                    </select>
                    <select aria-label="Địa điểm">
                        <option value="">Địa điểm</option>
                        <option value="QUY NHƠN">QUY NHƠN</option>
                        <option value="TP.HCM">TP.HCM</option>
                        <option value="HÀ NỘI">HÀ NỘI</option>
                        <option value="ĐÀ NẴNG">ĐÀ NẴNG</option>
                    </select>
                    <select aria-label="Mức lương">
                        <option value="">Mức lương</option>
                        <option value="Dưới 10 triệu">Dưới 10 triệu</option>
                        <option value="10 - 20 triệu">10 - 20 triệu</option>
                        <option value="Trên 20 triệu">Trên 20 triệu</option>
                    </select>
                    <select aria-label="Năm kinh nghiệm">
                        <option value="">Năm kinh nghiệm</option>
                        <option value="1 năm">1 năm</option>
                        <option value="2 năm">2 năm</option>
                        <option value="3 năm">3 năm</option>
                        <option value="Trên 3 năm">Trên 3 năm</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- Benefits Section -->
        <section class="benefits">
            <div class="container">
                <div class="section-header">
                    <h2>Tại Sao Chọn JobFinder?</h2>
                    <p class="section-subtitle">Chúng tôi cung cấp nền tảng tìm việc hiện đại với nhiều tính năng vượt trội</p>
                </div>

                <div class="benefits-wrapper">
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3>Tiện Lợi</h3>
                        <p>Giao diện thân thiện, dễ sử dụng giúp bạn tìm việc mọi lúc mọi nơi trên mọi thiết bị.</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3>Nhanh Chóng</h3>
                        <p>Tin tuyển dụng được đăng tải liên tục, giúp bạn nhanh chóng tìm được công việc phù hợp.</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Uy Tín</h3>
                        <p>Kết nối bạn với những công ty uy tín, đảm bảo môi trường làm việc chuyên nghiệp.</p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-th-large"></i>
                        </div>
                        <h3>Đa Dạng</h3>
                        <p>Cung cấp nhiều cơ hội việc làm từ nhiều ngành nghề, phù hợp với mọi đối tượng.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Top Categories Section -->
        <section class="categories-section">
            <div class="container">
                <div style="position: relative;">
                    <h2>Mảng làm việc hot</h2>
                    <div class="category-nav">
                        <button class="category-nav-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="category-nav-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>

                <div class="categories-wrapper">
                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/1688/1688451.png" alt="AI & Machine Learning">
                        </div>
                        <div class="category-title">AI & Machine Learning</div>
                        <div class="job-count">8.230 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/6295/6295417.png" alt="Blockchain">
                        </div>
                        <div class="category-title">Blockchain & Web3</div>
                        <div class="job-count">5.951 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/2920/2920349.png" alt="Cloud Computing">
                        </div>
                        <div class="category-title">Cloud Computing</div>
                        <div class="job-count">7.826 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/2092/2092757.png" alt="Cybersecurity">
                        </div>
                        <div class="category-title">An toàn mạng & Bảo mật</div>
                        <div class="job-count">6.629 việc làm</div>
                    </div>
                </div>

                <div class="categories-wrapper" style="margin-top: 12px;">
                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/2721/2721304.png" alt="DevOps">
                        </div>
                        <div class="category-title">DevOps & CI/CD</div>
                        <div class="job-count">4.974 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/2282/2282188.png" alt="Mobile Development">
                        </div>
                        <div class="category-title">Phát triển ứng dụng di động</div>
                        <div class="job-count">6.608 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/1927/1927656.png" alt="Data Science">
                        </div>
                        <div class="category-title">Data Science & Analytics</div>
                        <div class="job-count">5.763 việc làm</div>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <img src="https://cdn-icons-png.flaticon.com/512/8637/8637114.png" alt="Full Stack">
                        </div>
                        <div class="category-title">Full Stack Development</div>
                        <div class="job-count">9.010 việc làm</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="container">
                <div class="stats-wrapper">
                    <div class="stat-item">
                        <div class="stat-number"><span class="counter" data-target="10">0</span>+</div>
                        <div class="stat-label">Việc làm mỗi ngày</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><span class="counter" data-target="5">0</span>+</div>
                        <div class="stat-label">Công ty đối tác</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><span class="counter" data-target="5">0</span>+</div>
                        <div class="stat-label">Ứng viên thành công</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><span class="counter" data-target="0">0</span>%</div>
                        <div class="stat-label">Tỷ lệ hài lòng</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="testimonials-section">
            <div class="container">
                <div class="section-header">
                    <h2>Người Dùng Nói Gì Về Chúng Tôi?</h2>
                    <p class="section-subtitle">Những đánh giá từ ứng viên và nhà tuyển dụng đã sử dụng dịch vụ của chúng tôi</p>
                    <div class="section-divider"></div>
                </div>

                <div class="testimonials-wrapper">
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"Tôi đã tìm được công việc mơ ước chỉ sau 2 tuần sử dụng JobFinder. Giao diện thân thiện, dễ sử dụng và nhiều công việc phù hợp với kỹ năng của tôi."</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="avt.png" alt="Nguyễn Thị Minh Trang">
                            </div>
                            <div class="author-info">
                                <h4>Nguyễn Thị Minh Trang</h4>
                                <p>Senior Software Engineer tại FPT Software</p>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"Là một nhà tuyển dụng, tôi rất hài lòng với chất lượng ứng viên từ JobFinder. Chúng tôi đã tuyển được nhiều nhân tài xuất sắc thông qua nền tảng này."</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="avt.png" alt="Trần Minh Quân">
                            </div>
                            <div class="author-info">
                                <h4>Trần Minh Quân</h4>
                                <p>Giám đốc Nhân sự tại VNG Corporation</p>
                            </div>
                        </div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"Tính năng tạo CV và quản lý hồ sơ của JobFinder rất hữu ích. Tôi có thể dễ dàng ứng tuyển nhiều vị trí mà không cần phải nhập lại thông tin nhiều lần."</p>
                        </div>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="avt.png" alt="Lê Thị Thanh Hương">
                            </div>
                            <div class="author-info">
                                <h4>Lê Thị Thanh Hương</h4>
                                <p>Chuyên viên Marketing tại Shopee Việt Nam</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Companies Section -->
        <section class="partners-section">
            <div class="container">
                <div class="section-header">
                    <h2>Một số nhà tuyển dụng nổi bật</h2>
                    <p class="section-subtitle"></p>
                    <div class="section-divider"></div>
                    <div class="partners-description">
                    </div>
                </div>
                    <div class="partners-track">
                        <!-- Set 1 -->
                        <div class="partners-wrapper">
                            <div class="partner-logo" data-name="FPT Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/FPT_logo_2010.svg/1200px-FPT_logo_2010.svg.png" alt="FPT">
                            </div>
                            <div class="partner-logo" data-name="Tập đoàn Viettel">
                                <img src="viettel.jpg">
                            </div>
                            <div class="partner-logo" data-name="Tập đoàn Vingroup">
                                <img src="vin.jpg" alt="Vingroup">
                            </div>



                            <div class="partner-logo" data-name="Tập đoàn VNPT">
                                <img src="VNPT.png" alt="VNPT">
                            </div>
                            <div class="partner-logo" data-name="Intel Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7d/Intel_logo_%282006-2020%29.svg/1200px-Intel_logo_%282006-2020%29.svg.png" alt="Intel">
                            </div>
                            <div class="partner-logo" data-name="Google LLC">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/1200px-Google_2015_logo.svg.png" alt="Google">
                            </div>
                            <div class="partner-logo" data-name="Samsung Electronics">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Samsung_Logo.svg/2560px-Samsung_Logo.svg.png" alt="Samsung">
                            </div>
                        </div>

                        <!-- Set 2 (Duplicate for infinite scroll) -->
                        <div class="partners-wrapper">
                            <div class="partner-logo" data-name="Microsoft Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Microsoft_logo.svg/2048px-Microsoft_logo.svg.png" alt="Microsoft">
                            </div>
                            <div class="partner-logo" data-name="Apple Inc.">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/Apple_logo_black.svg/1667px-Apple_logo_black.svg.png" alt="Apple">
                            </div>
                            <div class="partner-logo" data-name="IBM Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/51/IBM_logo.svg/2560px-IBM_logo.svg.png" alt="IBM">
                            </div>

                            <div class="partner-logo" data-name="TMA Solutions">
                                <img src="TMA.png" alt="TMA Solutions">
                            </div>
                            <div class="partner-logo" data-name="Meta Platforms, Inc.">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Facebook_Logo_%282019%29.png/1200px-Facebook_Logo_%282019%29.png" alt="Facebook">
                            </div>
                            <div class="partner-logo" data-name="LinkedIn Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/81/LinkedIn_icon.svg/2048px-LinkedIn_icon.svg.png" alt="LinkedIn">
                            </div>
                        </div>

                        <!-- Set 1 (Duplicate again for smoother infinite scroll) -->
                        <div class="partners-wrapper">
                            <div class="partner-logo" data-name="FPT Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/11/FPT_logo_2010.svg/1200px-FPT_logo_2010.svg.png" alt="FPT">
                            </div>
                            <div class="partner-logo" data-name="Tập đoàn Viettel">
                                <img src="viettel.jpg" alt="Viettel">
                            </div>
                            <div class="partner-logo" data-name="Tập đoàn Vingroup">
                                <img src="vin.jpg">
                            </div>

                            <div class="partner-logo" data-name="TMA Solutions">
                                <img src="TMA.png" alt="TMA Solutions">
                            </div>
                            <div class="partner-logo" data-name="Tập đoàn VNPT">
                                <img src="VNPT.png" alt="VNPT">
                            </div>
                            <div class="partner-logo" data-name="Intel Corporation">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/7d/Intel_logo_%282006-2020%29.svg/1200px-Intel_logo_%282006-2020%29.svg.png" alt="Intel">
                            </div>
                            <div class="partner-logo" data-name="Google LLC">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/1200px-Google_2015_logo.svg.png" alt="Google">
                            </div>
                            <div class="partner-logo" data-name="Samsung Electronics">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/2/24/Samsung_Logo.svg/2560px-Samsung_Logo.svg.png" alt="Samsung">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Jobs Section -->
    <section id="jobs" class="featured-jobs">
        <div class="container jobs-container">
            <div class="jobs-main">
                <h2>Công Việc Nổi Bật</h2>
                <p class="job-subtitle">Khám phá những cơ hội việc làm hấp dẫn từ các công ty hàng đầu tại Việt Nam</p>

                <!-- Advanced Search Section -->
                <div class="advanced-search-container">
                    <div class="advanced-search-header">
                        <h3 class="advanced-search-title"><i class="fas fa-search"></i> Tìm kiếm việc làm</h3>
                        <button class="advanced-search-toggle" id="advancedSearchToggle">Tìm kiếm nâng cao <i class="fas fa-chevron-down"></i></button>
                    </div>
                    <div class="advanced-search-form">
                        <div class="search-input-group">
                            <label for="jobKeyword">Từ khóa</label>
                            <i class="fas fa-keyboard"></i>
                            <input type="search" id="jobKeyword" placeholder="Nhập từ khóa, vị trí, công ty..." />
                        </div>
                        <div class="search-input-group">
                            <label for="jobLocation">Địa điểm</label>
                            <select id="jobLocation" onchange="toggleOtherLocation()">
                                <option value="">Tất cả địa điểm</option>
                                <option value="hanoi">Hà Nội</option>
                                <option value="hcm">TP. Hồ Chí Minh</option>
                                <option value="danang">Đà Nẵng</option>
                                <option value="cantho">Cần Thơ</option>
                                <option value="haiphong">Hải Phòng</option>
                                <option value="nhatrang">Nha Trang</option>
                                <option value="vungtau">Vũng Tàu</option>
                                <option value="binhduong">Bình Dương</option>
                                <option value="dongnai">Đồng Nai</option>
                                <option value="hue">Huế</option>
                                <option value="quangninh">Quảng Ninh</option>
                                <option value="remote">Làm việc từ xa</option>
                                <option value="other">Tỉnh thành khác</option>
                            </select>
                        </div>
                        <div class="search-input-group" id="otherLocationGroup" style="display: none;">
                            <label for="otherLocation">Nhập tỉnh thành</label>
                            <i class="fas fa-map-marker-alt"></i>
                            <input type="text" id="otherLocation" placeholder="Nhập tên tỉnh thành..." />
                        </div>
                        <div class="search-input-group">
                            <label for="jobCategory">Chuyên ngành IT</label>
                            <select id="jobCategory">
                                <option value="">Tất cả chuyên ngành IT</option>
                                <option value="developer">Lập trình viên</option>
                                <option value="devops">DevOps Engineer</option>
                                <option value="qa">QA/Tester</option>
                                <option value="mobile">Mobile Developer</option>
                                <option value="frontend">Frontend Developer</option>
                                <option value="backend">Backend Developer</option>
                                <option value="fullstack">Fullstack Developer</option>
                                <option value="data">Data Engineer/Scientist</option>
                                <option value="security">An ninh mạng</option>
                                <option value="system">Quản trị hệ thống</option>
                            </select>
                        </div>
                        <div class="search-input-group">
                            <label for="jobExperience">Kinh nghiệm</label>
                            <select id="jobExperience">
                                <option value="">Tất cả kinh nghiệm</option>
                                <option value="fresher">Mới tốt nghiệp</option>
                                <option value="1year">1 năm</option>
                                <option value="2year">2 năm</option>
                                <option value="3year">3 năm</option>
                                <option value="5year">Trên 5 năm</option>
                            </select>
                        </div>
                    </div>

                    <div class="advanced-options" id="advancedOptions">
                        <div class="advanced-options-grid">
                            <div class="search-input-group">
                                <label for="jobSalary">Mức lương</label>
                                <select id="jobSalary">
                                    <option value="">Tất cả mức lương</option>
                                    <option value="under10">Dưới 10 triệu</option>
                                    <option value="10to15">10 - 15 triệu</option>
                                    <option value="15to25">15 - 25 triệu</option>
                                    <option value="25to35">25 - 35 triệu</option>
                                    <option value="35to50">35 - 50 triệu</option>
                                    <option value="over50">Trên 50 triệu</option>
                                    <option value="500to1000">500 - 1,000 USD</option>
                                    <option value="1000to2000">1,000 - 2,000 USD</option>
                                    <option value="2000to3500">2,000 - 3,500 USD</option>
                                    <option value="over3500">Trên 3,500 USD</option>
                                </select>
                            </div>
                            <div class="search-input-group">
                                <label for="jobLevel">Cấp bậc</label>
                                <select id="jobLevel">
                                    <option value="">Tất cả cấp bậc</option>
                                    <option value="intern">Thực tập sinh</option>
                                    <option value="fresher">Fresher</option>
                                    <option value="junior">Junior</option>
                                    <option value="middle">Middle</option>
                                    <option value="senior">Senior</option>
                                    <option value="lead">Team Lead</option>
                                    <option value="manager">Manager</option>
                                    <option value="director">Director</option>
                                    <option value="cto">CTO</option>
                                </select>
                            </div>
                            <div class="search-input-group">
                                <label for="jobType">Hình thức</label>
                                <select id="jobType">
                                    <option value="">Tất cả hình thức</option>
                                    <option value="fulltime">Toàn thời gian</option>
                                    <option value="parttime">Bán thời gian</option>
                                    <option value="remote">Làm việc từ xa</option>
                                    <option value="hybrid">Làm việc kết hợp</option>
                                    <option value="contract">Hợp đồng</option>
                                    <option value="freelance">Freelance</option>
                                    <option value="intern">Thực tập</option>
                                </select>
                            </div>
                            <div class="search-input-group">
                                <label for="jobPosted">Đăng tuyển</label>
                                <select id="jobPosted">
                                    <option value="">Tất cả thời gian</option>
                                    <option value="today">Hôm nay</option>
                                    <option value="24h">24 giờ qua</option>
                                    <option value="3days">3 ngày qua</option>
                                    <option value="7days">7 ngày qua</option>
                                    <option value="14days">14 ngày qua</option>
                                    <option value="30days">30 ngày qua</option>
                                </select>
                            </div>
                            <div class="search-input-group">
                                <label for="jobSkills">Kỹ năng</label>
                                <select id="jobSkills">
                                    <option value="">Tất cả kỹ năng</option>
                                    <option value="java">Java</option>
                                    <option value="javascript">JavaScript</option>
                                    <option value="python">Python</option>
                                    <option value="csharp">C#</option>
                                    <option value="php">PHP</option>
                                    <option value="react">React</option>
                                    <option value="angular">Angular</option>
                                    <option value="vue">Vue.js</option>
                                    <option value="nodejs">Node.js</option>
                                    <option value="dotnet">.NET</option>
                                    <option value="sql">SQL</option>
                                    <option value="nosql">NoSQL</option>
                                    <option value="aws">AWS</option>
                                    <option value="azure">Azure</option>
                                    <option value="docker">Docker</option>
                                    <option value="kubernetes">Kubernetes</option>
                                    <option value="devops">DevOps</option>
                                </select>
                            </div>
                        </div>

                        <div class="filter-tags">
                            <div class="filter-tag">DevOps Engineer <i class="fas fa-times"></i></div>
                            <div class="filter-tag">Hà Nội <i class="fas fa-times"></i></div>
                            <div class="filter-tag">2 năm kinh nghiệm <i class="fas fa-times"></i></div>
                            <div class="filter-tag">Docker <i class="fas fa-times"></i></div>
                            <div class="filter-tag">CI/CD <i class="fas fa-times"></i></div>
                            <div class="filter-tag">800 - 3,500 USD <i class="fas fa-times"></i></div>
                        </div>
                    </div>

                    <div class="search-actions">
                        <button class="search-btn" id="advanced-search-btn"><i class="fas fa-search"></i> Tìm kiếm</button>
                        <button class="reset-btn" id="reset-search-btn">Xóa bộ lọc</button>
                    </div>

                    <div class="search-results-info">
                        Tìm thấy <strong>0</strong> việc làm phù hợp với tiêu chí của bạn
                    </div>
                </div>
                <!-- End Advanced Search Section -->
                <div class="job-list">
                <!-- Job Card Horizontal - Kỹ Sư Vận Hành Và Phát Triển -->
                <div class="job-card-horizontal">
                    <img src="viettel.jpg" alt="Viettel Logo" class="job-card-logo">
                    <div class="job-card-content">
                        <div class="job-card-header">
                            <div>
                                <h3 class="job-card-title">Kỹ Sư Vận Hành Và Phát Triển (Devops Engineer) <i class="fas fa-check-circle verified"></i></h3>
                                <div class="job-card-company">TẬP ĐOÀN CÔNG NGHIỆP - VIỄN THÔNG QUÂN ĐỘI</div>
                                <div class="job-card-update">Cập nhật 59 phút trước</div>
                            </div>
                            <div class="job-card-salary">800 - 3,500 USD</div>
                        </div>
                        <div class="job-card-details">
                            <div class="job-card-detail"><i class="fas fa-map-marker-alt"></i> Hà Nội</div>
                            <div class="job-card-detail"><i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển</div>
                        </div>
                        <div class="job-card-footer">
                            <div class="job-card-tags">
                                <span class="job-card-tag">DevOps</span>
                                <span class="job-card-tag">CI/CD</span>
                                <span class="job-card-tag">Docker</span>
                            </div>
                            <div class="job-card-actions">
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="top-badge">TOP</div>
                </div>
                <!-- Job Card Horizontal - Java Engineer -->
                <div class="job-card-horizontal">
                    <img src="viettel.jpg" alt="Viettel Logo" class="job-card-logo">
                    <div class="job-card-content">
                        <div class="job-card-header">
                            <div>
                                <h3 class="job-card-title">Java Engineer <i class="fas fa-check-circle verified"></i></h3>
                                <div class="job-card-company">TẬP ĐOÀN CÔNG NGHIỆP - VIỄN THÔNG QUÂN ĐỘI</div>
                                <div class="job-card-update">Cập nhật 1 giờ trước</div>
                            </div>
                            <div class="job-card-salary">800 - 3,500 USD</div>
                        </div>
                        <div class="job-card-details">
                            <div class="job-card-detail"><i class="fas fa-map-marker-alt"></i> Hà Nội</div>
                            <div class="job-card-detail"><i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển</div>
                        </div>
                        <div class="job-card-footer">
                            <div class="job-card-tags">
                                <span class="job-card-tag">Java</span>
                                <span class="job-card-tag">Spring</span>
                                <span class="job-card-tag">Microservices</span>
                            </div>
                            <div class="job-card-actions">
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                    <div class="top-badge">TOP</div>
                </div>
                <!-- Job Card Horizontal - Chuyên Viên Hệ Thống ERP -->
                <div class="job-card-horizontal">
                    <img src="erp.webp" alt="Lihua Logo" class="job-card-logo">
                    <div class="job-card-content">
                        <div class="job-card-header">
                            <div>
                                <h3 class="job-card-title">Chuyên Viên Hệ Thống ERP, Dingtalk</h3>
                                <div class="job-card-company">CÔNG TY TNHH CÔNG NGHỆ MÔI TRƯỜNG LIHUA (VIỆT NAM)</div>
                                <div class="job-card-update">Cập nhật 1 giờ trước</div>
                            </div>
                            <div class="job-card-salary">Thỏa thuận</div>
                        </div>
                        <div class="job-card-details">
                            <div class="job-card-detail"><i class="fas fa-map-marker-alt"></i> Hưng Yên</div>
                            <div class="job-card-detail"><i class="fas fa-clock"></i> Còn 17 ngày để ứng tuyển</div>
                        </div>
                        <div class="job-card-footer">
                            <div class="job-card-tags">
                                <span class="job-card-tag">ERP</span>
                                <span class="job-card-tag">Dingtalk</span>
                                <span class="job-card-tag">Hệ thống</span>
                            </div>
                            <div class="job-card-actions">
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Job Card Horizontal - Senior Automation Test Engineer -->
                <div class="job-card-horizontal">
                    <img src="senior.webp" alt="Crossian Logo" class="job-card-logo">
                    <div class="job-card-content">
                        <div class="job-card-header">
                            <div>
                                <h3 class="job-card-title">Senior Automation Test Engineer, Global E-Commerce Data Platform</h3>
                                <div class="job-card-company">Crossian</div>
                                <div class="job-card-update">Cập nhật 1 giờ trước</div>
                            </div>
                            <div class="job-card-salary">Thỏa thuận</div>
                        </div>
                        <div class="job-card-details">
                            <div class="job-card-detail"><i class="fas fa-map-marker-alt"></i> Hà Nội</div>
                            <div class="job-card-detail"><i class="fas fa-clock"></i> Còn 12 ngày để ứng tuyển</div>
                        </div>
                        <div class="job-card-footer">
                            <div class="job-card-tags">
                                <span class="job-card-tag">Automation</span>
                                <span class="job-card-tag">Testing</span>
                                <span class="job-card-tag">E-Commerce</span>
                            </div>
                            <div class="job-card-actions">
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Job Card Horizontal - Senior Software Tester -->
                <div class="job-card-horizontal">
                    <img src="senior.webp" alt="Crossian Logo" class="job-card-logo">
                    <div class="job-card-content">
                        <div class="job-card-header">
                            <div>
                                <h3 class="job-card-title">Senior Software Tester, Global E-Commerce Data Platform</h3>
                                <div class="job-card-company">Crossian</div>
                                <div class="job-card-update">Cập nhật 1 giờ trước</div>
                            </div>
                            <div class="job-card-salary">Thỏa thuận</div>
                        </div>
                        <div class="job-card-details">
                            <div class="job-card-detail"><i class="fas fa-map-marker-alt"></i> Hà Nội</div>
                            <div class="job-card-detail"><i class="fas fa-clock"></i> Còn 12 ngày để ứng tuyển</div>
                        </div>
                        <div class="job-card-footer">
                            <div class="job-card-tags">
                                <span class="job-card-tag">Testing</span>
                                <span class="job-card-tag">QA</span>
                                <span class="job-card-tag">E-Commerce</span>
                            </div>
                            <div class="job-card-actions">
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Job 1 -->
                <div class="job-item">
                    <img src="QA.webp" alt="THK Holdings Logo">
                    <div class="job-info">
                        <h3>QA Engineer</h3>
                        <p class="company">Công Ty Trách Nhiệm Hữu Hạn THK Holdings Việt Nam</p>
                        <p class="level">Cập nhật 2 phút trước</p>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hồ Chí Minh</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 19 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 2 -->
                <div class="job-item">
                    <img src="hanyang-logo.svg" alt="Hanyang Digitech Logo">
                    <div class="job-info">
                        <h3>Nhân Viên IT</h3>
                        <p class="company">Công ty TNHH Hanyang Digitech Vina</p>
                        <p class="level">Cập nhật 12 phút trước</p>
                        <div class="skills">
                            <span>Mạng máy tính</span>
                            <span>Khắc Phục Sự Cố Máy Tính</span>
                            <span>Hệ Điều Hành Windows MacOS Linux</span>
                            <span>2+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Phú Thọ</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 3 -->
                <div class="job-item">
                    <img src="middle.webp" alt="eUP Technology Logo">
                    <div class="job-info">
                        <h3>Middle Android Developer</h3>
                        <p class="company">Công ty Cổ phần Công nghệ eUP</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Android</span>
                            <span>Android SDK</span>
                            <span>Lập Trình Android</span>
                            <span>15+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 1 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 4 -->
                <div class="job-item">
                    <img src="kone-logo.svg" alt="KONE Việt Nam Logo">
                    <div class="job-info">
                        <h3>Chương Trình Đào Tạo Nghề Thang Máy/Thang Cuốn - Tài Hà Nội</h3>
                        <p class="company">Công Ty TNHH Kone Việt Nam</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Khả Năng Phân Tích Và Giải Quyết Vấn Đề</span>
                            <span>Kỹ Năng Về Cơ Khí</span>
                            <span>Kiến Thức Về Điện Tử</span>
                            <span>2+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 49 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 5 -->
                <div class="job-item">
                    <img src="fpt.webp" alt="FPT Software Logo">
                    <div class="job-info">
                        <h3>Kỹ Sư Hệ Thống Data Center (System Engineer)</h3>
                        <p class="company">Công Ty TNHH MTV Viễn Thông Quốc Tế FPT</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Quản Trị Hệ Thống Linux Windows</span>
                            <span>Kiến Trức Hệ Thống Máy Chủ</span>
                            <span>Mạng Máy Tính (networking): Tcp, Ip, Routing, Switching, Vlan, Firewall</span>
                            <span>2+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hồ Chí Minh</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 15 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 6 -->
                <div class="job-item">
                    <img src="pavagroup.webp" alt="Company Logo">
                    <div class="job-info">
                        <h3>Nhân Viên Kỹ Thuật Thiết Bị An Ninh (Thu Nhập 800$ - 1,200$)</h3>
                        <p class="company">Công Ty Cổ Phần Pvagroup</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">800$ - 1,200$</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 7 -->
                <div class="job-item">
                    <img src="vetc.webp" alt="Company Logo">
                    <div class="job-info">
                        <h3>Chuyên Viên Đánh Giá An Ninh Thông Tin - Pentester (2 Năm Kinh Nghiệm) (Thu Nhập Tối Đa 1,300 USD)</h3>
                        <p class="company">Công Ty TNHH Thu Phí Tự Động VETC</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 22 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Tối đa 1,300 USD</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 8 -->
                <div class="job-item">
                    <img src="kid.webp" alt="Company Logo">
                    <div class="job-info">
                        <h3>Mobile Developer (Có Kinh Nghiệm)</h3>
                        <p class="company">Công Ty CP Kids Plaza</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Phát Triển Ứng Dụng Di Động (android Hoặc Ios)</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hồ Chí Minh</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 15 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 9 -->
                <div class="job-item">
                    <img src="fpt.webp" alt="FPT Software Logo">
                    <div class="job-info">
                        <h3>Fresher SAP Functional (Tiếng Pháp/Tiếng Đức)</h3>
                        <p class="company">FPT Software</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Làm việc nhóm</span>
                            <span>Tiếng Pháp</span>
                            <span>Tiếng Đức</span>
                            <span>4+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 41 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">6 - 8 triệu</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 10 -->
                <div class="job-item">
                    <img src="tek-experts-vietnam-62a553577f2a7.webp" alt="Company Logo">
                    <div class="job-info">
                        <h3>Microsoft Support Engineer - CRM/ERP Software - Thu Nhập Hấp Dẫn</h3>
                        <p class="company">Tek Experts Việt Nam</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 22 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">Thỏa thuận</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
                <!-- Job 11 -->
                <div class="job-item">
                    <img src="scp.webp" alt="Company Logo">
                    <div class="job-info">
                        <h3>Product Manager (ICT Products) - Quản Lý Sản Phẩm</h3>
                        <p class="company">Công Ty Cổ Phần Máy Tính Vinh Xuân</p>
                        <p class="level">Cập nhật 14 phút trước</p>
                        <div class="skills">
                            <span>Quản Lý Dự Án</span>
                            <span>Phân Tích Thị Trường</span>
                            <span>Quản Lý Sản Phẩm</span>
                            <span>5+</span>
                        </div>
                        <div class="details">
                            <span><i class="fas fa-map-marker-alt"></i> Hà Nội</span>
                            <span class="days-left"><i class="fas fa-clock"></i> Còn 21 ngày để ứng tuyển</span>
                        </div>
                    </div>
                    <div class="job-actions">
                        <div class="job-card-salary">12 - 20 triệu</div>
                        <button class="job-card-apply">Ứng tuyển</button>
                        <div class="job-card-save"><i class="far fa-heart"></i></div>
                    </div>
                </div>
            </div>
            <div class="jobs-sidebar">
                <div class="recommended-jobs">
                    <div class="recommended-jobs-header">
                        <h3>Công việc phù hợp với bạn</h3>
                        <a href="#" class="view-all-link">Xem tất cả <i class="fas fa-chevron-right"></i></a>
                    </div>
                    <div class="recommended-jobs-list">
                        <!-- Recommended Job 1 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Kỹ Sư Vận Hành Và Phát Triển (Devops Engineer)</a>
                            <div class="recommended-job-company">
                                <img src="viettel.jpg" alt="Viettel Logo">
                                <span>TẬP ĐOÀN CÔNG NGHIỆP - VIỄN THÔNG QUÂN ĐỘI</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>DevOps</span>
                                <span>CI/CD</span>
                                <span>Docker</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">800 - 3,500 USD</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 2 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Java Engineer</a>
                            <div class="recommended-job-company">
                                <img src="viettel.jpg" alt="Viettel Logo">
                                <span>TẬP ĐOÀN CÔNG NGHIỆP - VIỄN THÔNG QUÂN ĐỘI</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Java</span>
                                <span>Spring</span>
                                <span>Microservices</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 16 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">800 - 3,500 USD</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 3 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Chuyên Viên Hệ Thống ERP, Dingtalk</a>
                            <div class="recommended-job-company">
                                <img src="erp.webp" alt="Lihua Logo">
                                <span>CÔNG TY TNHH CÔNG NGHỆ MÔI TRƯỜNG LIHUA (VIỆT NAM)</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>ERP</span>
                                <span>Dingtalk</span>
                                <span>Hệ thống</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hưng Yên
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 17 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">Thỏa thuận</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 4 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Senior Automation Test Engineer, Global E-Commerce Data Platform</a>
                            <div class="recommended-job-company">
                                <img src="senior.webp" alt="Crossian Logo">
                                <span>Crossian</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Automation</span>
                                <span>Testing</span>
                                <span>E-Commerce</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 12 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">Thỏa thuận</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 5 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Java Developer (SQL, Spring, ReactJS)</a>
                            <div class="recommended-job-company">
                                <img src="goline.jpg" alt="Goline Logo">
                                <span>Công ty Cổ phần Công nghệ Tài chính Goline</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Java</span>
                                <span>SQL</span>
                                <span>Spring</span>
                                <span>ReactJS</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 29 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">500 - 1,200 USD</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 6 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Chuyên Viên Số Hóa Chai LPG</a>
                            <div class="recommended-job-company">
                                <img src="gas.webp" alt="Gas South Logo">
                                <span>Công ty Cổ phần Kinh doanh Khí miền Nam</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Số hóa</span>
                                <span>LPG</span>
                                <span>Quản lý</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hồ Chí Minh
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 15 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">Thỏa thuận</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 7 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">iOS Developer (Swift) - App/Game</a>
                            <div class="recommended-job-company">
                                <img src="volio.webp" alt="Volio Logo">
                                <span>Công ty TNHH Volio Việt Nam</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>iOS</span>
                                <span>Swift</span>
                                <span>Git</span>
                                <span>+3</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 17 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">25 - 35 triệu</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 8 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Test Inter</a>
                            <div class="recommended-job-company">
                                <img src="goline.jpg" alt="Goline Logo">
                                <span>Công ty Cổ phần Công nghệ Tài chính Goline</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Testing</span>
                                <span>QA</span>
                                <span>Manual Test</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 29 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">Thỏa thuận</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                        <!-- Recommended Job 9 -->
                        <div class="recommended-job-item">
                            <a href="#" class="recommended-job-title">Fresher SAP Functional (Tiếng Pháp/Tiếng Đức)</a>
                            <div class="recommended-job-company">
                                <img src="fpt.webp" alt="FPT Software Logo">
                                <span>FPT Software</span>
                            </div>
                            <div class="recommended-job-tags">
                                <span>Làm việc nhóm</span>
                                <span>Tiếng Pháp</span>
                                <span>Tiếng Đức</span>
                                <span>+4</span>
                            </div>
                            <div class="recommended-job-footer">
                                <div class="recommended-job-location">
                                    <i class="fas fa-map-marker-alt"></i> Hà Nội
                                </div>
                                <div class="recommended-job-days">
                                    <i class="fas fa-clock"></i> Còn 41 ngày để ứng tuyển
                                </div>
                            </div>
                            <div class="recommended-job-actions">
                                <div class="job-card-salary">6 - 8 triệu</div>
                                <button class="job-card-apply">Ứng tuyển</button>
                                <div class="job-card-save"><i class="far fa-heart"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Section -->
    <section id="profile" class="profile-page" style="display: none;">
        <div class="container">
            <h2>Hồ sơ & CV</h2>
            <div class="profile-actions">
                <button class="modal-btn" onclick="openProfileModal('create')">Tạo hồ sơ mới</button>
                <button class="modal-btn" onclick="openCVModal()">Tạo CV mới</button>
            </div>
            <div class="search-bar">
                <input type="text" id="profileSearch" placeholder="Tìm theo tên, kỹ năng..." aria-label="Tìm hồ sơ">
                <button class="search-btn" onclick="searchProfiles()"><i class="fas fa-search"></i> Tìm</button>
            </div>
            <div class="filters">
                <select id="skillFilter" aria-label="Kỹ năng">
                    <option value="">Tất cả kỹ năng</option>
                    <option value="Python">Python</option>
                    <option value="Java">Java</option>
                    <option value="JavaScript">JavaScript</option>
                </select>
                <select id="experienceFilter" aria-label="Kinh nghiệm">
                    <option value="">Tất cả kinh nghiệm</option>
                    <option value="1">1-2 năm</option>
                    <option value="3">3-5 năm</option>
                    <option value="5">Trên 5 năm</option>
                </select>
                <select id="locationFilter" aria-label="Địa điểm">
                    <option value="">Tất cả địa điểm</option>
                    <option value="Hà Nội">Hà Nội</option>
                    <option value="TP.HCM">TP.HCM</option>
                    <option value="Đà Nẵng">Đà Nẵng</option>
                </select>
            </div>
            <div id="profileList" class="profile-list"></div>
        </div>
    </section>

    <!-- Employer Section -->
    <section id="employer" class="employer-page" style="display: none;">
        <div class="container">
            <h2>Đăng tuyển & Tìm hồ sơ</h2>
            <div class="employer-tabs">
                <button class="tab-btn active" onclick="showEmployerTab('post-job')">Đăng tuyển</button>
                <button class="tab-btn" onclick="showEmployerTab('find-profiles')">Tìm hồ sơ</button>
            </div>
            <div id="post-job" class="tab-content">
                <h3>Đăng tin tuyển dụng</h3>
                <form id="jobPostForm">
                    <div class="form-group">
                        <label for="jobTitle">Tiêu đề công việc</label>
                        <input type="text" id="jobTitle" placeholder="VD: Software Engineer" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Địa điểm</label>
                        <select id="location" required>
                            <option value="">Chọn địa điểm</option>
                            <option value="HCM">TP.HCM</option>
                            <option value="HN">Hà Nội</option>
                            <option value="DN">Đà Nẵng</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="salary">Mức lương</label>
                        <input type="text" id="salary" placeholder="VD: 15 - 25 triệu">
                    </div>
                    <div class="form-group">
                        <label for="description">Mô tả công việc</label>
                        <textarea id="description" placeholder="Mô tả chi tiết..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="skills">Kỹ năng yêu cầu</label>
                        <input type="text" id="skills" placeholder="VD: Python, Java, SQL">
                    </div>
                    <button type="submit" class="modal-btn">Đăng tin</button>
                </form>
            </div>
            <div id="find-profiles" class="tab-content" style="display: none;">
                <h3>Tìm hồ sơ ứng viên</h3>
                <div class="search-bar">
                    <input type="text" placeholder="Tìm theo kỹ năng, vị trí..." aria-label="Tìm hồ sơ">
                    <button class="search-btn"><i class="fas fa-search"></i> Tìm</button>
                </div>
                <div class="filters">
                    <select aria-label="Kỹ năng">
                        <option value="">Kỹ năng</option>
                        <option value="Python">Python</option>
                        <option value="Java">Java</option>
                        <option value="Marketing">Marketing</option>
                    </select>
                    <select aria-label="Kinh nghiệm">
                        <option value="">Kinh nghiệm</option>
                        <option value="1">1 năm</option>
                        <option value="3">3+ năm</option>
                    </select>
                    <select aria-label="Địa điểm">
                        <option value="">Địa điểm</option>
                        <option value="HCM">TP.HCM</option>
                        <option value="HN">Hà Nội</option>
                    </select>
                </div>
                <div class="profile-list">
                    <div class="profile-item">
                        <h4>Nguyễn Văn A</h4>
                        <p>Kỹ năng: Python, Django, AWS</p>
                        <p>Kinh nghiệm: 3 năm</p>
                        <p>Địa điểm: Hà Nội</p>
                        <button class="modal-btn">Xem hồ sơ</button>
                    </div>
                    <div class="profile-item">
                        <h4>Trần Thị B</h4>
                        <p>Kỹ năng: Java, Spring, MySQL</p>
                        <p>Kinh nghiệm: 2 năm</p>
                        <p>Địa điểm: TP.HCM</p>
                        <button class="modal-btn">Xem hồ sơ</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h4>JobFinder</h4>
                    </div>
                    <p>Nền tảng tìm kiếm việc làm tại Việt Nam, kết nối ứng viên với các công ty hàng đầu.</p>
                    <div class="footer-job-stats">
                        <div class="footer-stat-item">
                            <i class="fas fa-briefcase"></i>
                            <span>10+</span>
                            <p>Việc làm/ngày</p>
                        </div>
                        <div class="footer-stat-item">
                            <i class="fas fa-building"></i>
                            <span>5+</span>
                            <p>Đối tác</p>
                        </div>
                        <div class="footer-stat-item">
                            <i class="fas fa-user-tie"></i>
                            <span>5+</span>
                            <p>Ứng viên</p>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Liên Kết Nhanh</h4>
                    <p><a href="#" onclick="showSection('home')"><i class="fas fa-chevron-right"></i> Trang chủ</a></p>
                    <p><a href="#" onclick="showSection('jobs')"><i class="fas fa-chevron-right"></i> Tìm việc làm</a></p>
                    <p><a href="#" onclick="showSection('profile')"><i class="fas fa-chevron-right"></i> Tạo hồ sơ</a></p>
                    <p><a href="#" onclick="showSection('employer')"><i class="fas fa-chevron-right"></i> Nhà tuyển dụng</a></p>
                </div>
                <div class="footer-section">
                    <h4>Liên Hệ</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone-alt"></i> 0965635705</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Tải Ứng Dụng</h4>
                    <div class="app-buttons">
                        <a href="#" class="app-button">
                            <i class="fab fa-google-play"></i>
                            <div class="app-button-text">
                                <span>GET IT ON</span>
                                <strong>Google Play</strong>
                            </div>
                        </a>
                        <a href="#" class="app-button">
                            <i class="fab fa-apple"></i>
                            <div class="app-button-text">
                                <span>Download on</span>
                                <strong>App Store</strong>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© 2025 JobFinder. Tất cả các quyền được bảo lưu.</p>
                <div class="footer-bottom-links">
                    <a href="#">Điều khoản</a>
                    <a href="#">Bảo mật</a>
                    <a href="#">Trợ giúp</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close">×</span>
            <h2>Đăng Nhập</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" name="email" placeholder="Nhập email của bạn" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Mật Khẩu</label>
                    <input type="password" id="loginPassword" name="password" placeholder="Nhập mật khẩu" required>
                </div>
                <button type="submit" class="modal-btn">Đăng Nhập</button>
                <p class="modal-link">Chưa có tài khoản? <a href="#" id="showRegister">Đăng ký ngay</a></p>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close">×</span>
            <h2>Đăng Ký</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerName">Họ và Tên</label>
                    <input type="text" id="registerName" name="name" placeholder="Nhập họ và tên" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <input type="email" id="registerEmail" name="email" placeholder="Nhập email của bạn" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Mật Khẩu</label>
                    <input type="password" id="registerPassword" name="password" placeholder="Nhập mật khẩu" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Xác Nhận Mật Khẩu</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Xác nhận mật khẩu" required>
                </div>
                <button type="submit" class="modal-btn">Đăng Ký</button>
                <p class="modal-link">Đã có tài khoản? <a href="#" id="showLogin">Đăng nhập ngay</a></p>
            </form>
        </div>
    </div>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal profile-modal">
        <div class="modal-content">
            <span class="close">×</span>
            <h2 id="profileModalTitle">Tạo hồ sơ mới</h2>
            <form id="profileForm">
                <input type="hidden" id="profileId">
                <div class="form-group">
                    <label for="profileName">Họ và Tên</label>
                    <input type="text" id="profileName" placeholder="Nhập họ và tên" required>
                </div>
                <div class="form-group">
                    <label for="profileEmail">Email</label>
                    <input type="email" id="profileEmail" placeholder="Nhập email" required>
                </div>
                <div class="form-group">
                    <label for="profileSkills">Kỹ năng</label>
                    <input type="text" id="profileSkills" placeholder="VD: Python, Java" required>
                </div>
                <div class="form-group">
                    <label for="profileExperience">Kinh nghiệm (năm)</label>
                    <input type="number" id="profileExperience" placeholder="VD: 3" required>
                </div>
                <div class="form-group">
                    <label for="profileLocation">Địa điểm</label>
                    <select id="profileLocation" required>
                        <option value="">Chọn địa điểm</option>
                        <option value="Hà Nội">Hà Nội</option>
                        <option value="TP.HCM">TP.HCM</option>
                        <option value="Đà Nẵng">Đà Nẵng</option>
                    </select>
                </div>
                <button type="submit" class="modal-btn">Lưu hồ sơ</button>
            </form>
        </div>
    </div>

    <!-- CV Modal -->
    <div id="cvModal" class="modal profile-modal">
        <div class="modal-content">
            <span class="close">×</span>
            <h2>Tạo CV mới</h2>
            <form id="cvForm">
                <div class="form-group">
                    <label for="cvName">Họ và Tên</label>
                    <input type="text" id="cvName" placeholder="Nhập họ và tên" required>
                </div>
                <div class="form-group">
                    <label for="cvEducation">Học vấn</label>
                    <textarea id="cvEducation" placeholder="VD: Cử nhân CNTT, ĐH Bách Khoa" required></textarea>
                </div>
                <div class="form-group">
                    <label for="cvExperience">Kinh nghiệm làm việc</label>
                    <textarea id="cvExperience" placeholder="VD: Software Engineer tại FPT, 2020-2023" required></textarea>
                </div>
                <div class="form-group">
                    <label for="cvSkills">Kỹ năng</label>
                    <input type="text" id="cvSkills" placeholder="VD: Python, Java, SQL" required>
                </div>
                <button type="submit" class="modal-btn">Lưu CV</button>
            </form>
        </div>
    </div>

    <!-- View CV Modal -->
    <div id="viewCVModal" class="modal profile-modal">
        <div class="modal-content">
            <span class="close">×</span>
            <h2>Chi tiết CV</h2>
            <div id="cvDetails"></div>
            <button class="modal-btn" onclick="closeModal('viewCVModal')">Đóng</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Counter Animation for Stats Section
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter');
            const speed = 200; // The lower the slower

            const isInViewport = function(elem) {
                const bounding = elem.getBoundingClientRect();
                return (
                    bounding.top >= 0 &&
                    bounding.left >= 0 &&
                    bounding.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                    bounding.right <= (window.innerWidth || document.documentElement.clientWidth)
                );
            };

            const animateCounters = function() {
                counters.forEach(counter => {
                    if (isInViewport(counter)) {
                        const target = parseInt(counter.innerText.replace(/,/g, ''));
                        const count = parseInt(counter.getAttribute('data-count') || 0);
                        const increment = Math.trunc(target / speed);

                        if (count < target) {
                            counter.setAttribute('data-count', count + increment);
                            counter.innerText = (count + increment).toLocaleString();
                            setTimeout(animateCounters, 1);
                        } else {
                            counter.innerText = target.toLocaleString();
                        }
                    }
                });
            };

            // Initialize counters with 0
            counters.forEach(counter => {
                counter.setAttribute('data-count', 0);
                counter.innerText = '0';
            });

            // Start animation when scrolled into view
            window.addEventListener('scroll', function() {
                animateCounters();
            });

            // Initial check in case elements are already in viewport
            animateCounters();
        });

        // Base URL for API
const API_URL = 'http://localhost:5000/api';

// Modal Functionality
const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');
const profileModal = document.getElementById('profileModal');
const cvModal = document.getElementById('cvModal');
const viewCVModal = document.getElementById('viewCVModal');
const loginBtn = document.querySelector('.login-btn');
const registerBtn = document.querySelector('.register-btn');
const showRegisterLink = document.getElementById('showRegister');
const showLoginLink = document.getElementById('showLogin');
const closeButtons = document.querySelectorAll('.close');

loginBtn.addEventListener('click', (e) => {
    e.preventDefault();
    loginModal.style.display = 'flex';
});

registerBtn.addEventListener('click', (e) => {
    e.preventDefault();
    registerModal.style.display = 'flex';
});

showRegisterLink.addEventListener('click', (e) => {
    e.preventDefault();
    loginModal.style.display = 'none';
    registerModal.style.display = 'flex';
});

showLoginLink.addEventListener('click', (e) => {
    e.preventDefault();
    registerModal.style.display = 'none';
    loginModal.style.display = 'flex';
});

closeButtons.forEach(button => {
    button.addEventListener('click', () => {
        loginModal.style.display = 'none';
        registerModal.style.display = 'none';
        profileModal.style.display = 'none';
        cvModal.style.display = 'none';
        viewCVModal.style.display = 'none';
    });
});

window.addEventListener('click', (e) => {
    if (e.target === loginModal) loginModal.style.display = 'none';
    if (e.target === registerModal) registerModal.style.display = 'none';
    if (e.target === profileModal) profileModal.style.display = 'none';
    if (e.target === cvModal) cvModal.style.display = 'none';
    if (e.target === viewCVModal) viewCVModal.style.display = 'none';
});

// Authentication
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    try {
        const response = await fetch(`${API_URL}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
        });
        const data = await response.json();
        if (response.ok) {
            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));
            alert('Đăng nhập thành công!');
            loginModal.style.display = 'none';
            updateUIForLoggedInUser(data.user);
        } else {
            alert(data.message);
        }
    } catch (error) {
        alert('Đã có lỗi xảy ra!');
    }
});

document.getElementById('registerForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const role = 'candidate'; // Default role, can be extended to allow employer registration

    if (password !== confirmPassword) {
        alert('Mật khẩu xác nhận không khớp!');
        return;
    }

    try {
        const response = await fetch(`${API_URL}/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name, email, password, role }),
        });
        const data = await response.json();
        if (response.ok) {
            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));
            alert('Đăng ký thành công!');
            registerModal.style.display = 'none';
            updateUIForLoggedInUser(data.user);
        } else {
            alert(data.message);
        }
    } catch (error) {
        alert('Đã có lỗi xảy ra!');
    }
});

// Update UI for logged-in user
function updateUIForLoggedInUser(user) {
    const navButtons = document.querySelector('.nav-buttons');
    navButtons.innerHTML = `
        <span>Xin chào, ${user.name}</span>
        <button class="logout-btn">Đăng xuất</button>
    `;
    document.querySelector('.logout-btn').addEventListener('click', () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.reload();
    });

    // Show employer tab only for employers
    const employerLink = document.querySelector('a[onclick="showSection(\'employer\')"]');
    if (user.role === 'employer') {
        employerLink.style.display = 'block';
    } else {
        employerLink.style.display = 'none';
    }
}

// Check if user is logged in on page load
document.addEventListener('DOMContentLoaded', () => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (user) {
        updateUIForLoggedInUser(user);
    }
});

// Section Toggling
function showSection(sectionId) {
    const sections = ['home', 'jobs', 'profile', 'employer'];
    sections.forEach(id => {
        const section = document.getElementById(id);
        if (section) {
            section.style.display = id === sectionId ? 'block' : 'none';
        }
    });
    document.querySelectorAll('.nav-links a').forEach(link => {
        link.classList.toggle('active', link.getAttribute('onclick') === `showSection('${sectionId}')`);
    });

    // Fetch data for specific sections
    if (sectionId === 'jobs') fetchJobs();
    if (sectionId === 'profile') fetchProfiles();
}

// Employer Tabs
function showEmployerTab(tabId) {
    document.querySelectorAll('.employer-page .tab-content').forEach(content => {
        content.style.display = content.id === tabId ? 'block' : 'none';
    });
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.getAttribute('onclick').includes(tabId));
    });
}

// Global jobs array
let allJobs = [];

// Job Fetching
async function fetchJobs(useAdvancedSearch = false) {
    let search, location, salary, skills, experience, jobType, jobPosted;

    if (useAdvancedSearch) {
        search = document.getElementById('jobKeyword')?.value || '';
        location = document.getElementById('jobLocation')?.value || '';
        salary = document.getElementById('jobSalary')?.value || '';
        skills = document.getElementById('jobSkills')?.value || '';
        experience = document.getElementById('jobExperience')?.value || '';
        jobType = document.getElementById('jobType')?.value || '';
        jobPosted = document.getElementById('jobPosted')?.value || '';

        if (location === 'other') {
            location = document.getElementById('otherLocation')?.value || '';
        }
    } else {
        search = document.getElementById('main-search-input')?.value || '';
        location = document.querySelector('#locationFilter')?.value || '';
        experience = document.querySelector('#experienceFilter')?.value || '';
    }

    try {
        // Fetch jobs from API
        const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${API_CONFIG.ENDPOINTS.JOBS.LIST}`);
        if (!response.ok) {
            throw new Error('Failed to fetch jobs');
        }
        allJobs = await response.json();

        // Filter jobs
        const filteredJobs = getFilteredJobs(search, location, salary, skills, experience, jobType, jobPosted);
        renderJobs(filteredJobs);

        // Update results count
        const searchResultsInfo = document.querySelector('.search-results-info strong');
        if (searchResultsInfo) {
            searchResultsInfo.textContent = filteredJobs.length;
        }
    } catch (error) {
        console.error('Error fetching jobs:', error);
    }
}

// Filter jobs function
function getFilteredJobs(search = '', location = '', salary = '', skills = '', experience = '', jobType = '', jobPosted = '') {
    return allJobs.filter(job => {
        const matchesSearch = !search ||
            job.title.toLowerCase().includes(search.toLowerCase()) ||
            job.description.toLowerCase().includes(search.toLowerCase());

        const matchesLocation = !location ||
            job.location.toLowerCase().includes(location.toLowerCase());

        const matchesSalary = !salary ||
            (job.salaryMin <= parseInt(salary) && job.salaryMax >= parseInt(salary));

        const matchesSkills = !skills ||
            skills.toLowerCase().split(',').every(skill =>
                job.skills.some(jobSkill =>
                    jobSkill.toLowerCase().includes(skill.trim().toLowerCase())
                )
            );

        const matchesExperience = !experience ||
            job.experience === experience;

        const matchesJobType = !jobType ||
            job.type === jobType;

        const matchesJobPosted = !jobPosted ||
            isWithinDateRange(job.postedDate, jobPosted);

        return matchesSearch && matchesLocation && matchesSalary &&
               matchesSkills && matchesExperience && matchesJobType &&
               matchesJobPosted;
    });
}

// Helper function for date filtering
function isWithinDateRange(postedDate, range) {
    const date = new Date(postedDate);
    const now = new Date();
    const daysDiff = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    switch(range) {
        case '24h': return daysDiff <= 1;
        case '7d': return daysDiff <= 7;
        case '14d': return daysDiff <= 14;
        case '30d': return daysDiff <= 30;
        default: return true;
    }
}

// Job Post Form Submission
document.getElementById('jobPostForm')?.addEventListener('submit', async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Vui lòng đăng nhập!');
        return;
    }

    const jobData = {
        title: document.getElementById('jobTitle').value,
        company: document.getElementById('jobTitle').value, // Replace with actual company input if added
        location: document.getElementById('location').value,
        salary: document.getElementById('salary').value,
        description: document.getElementById('description').value,
        skills: document.getElementById('skills').value,
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
    };

    try {
        const response = await fetch(`${API_URL}/jobs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token,
            },
            body: JSON.stringify(jobData),
        });
        if (response.ok) {
            alert('Tin tuyển dụng đã được đăng!');
            document.getElementById('jobPostForm').reset();
            fetchJobs();
        } else {
            const data = await response.json();
            alert(data.message);
        }
    } catch (error) {
        alert('Đã có lỗi xảy ra!');
    }
});

// Profile and CV Functionality
let profiles = [];

function openProfileModal(mode, profile = {}) {
    document.getElementById('profileModalTitle').textContent = mode === 'edit' ? 'Chỉnh sửa hồ sơ' : 'Tạo hồ sơ mới';
    document.getElementById('profileId').value = profile._id || '';
    document.getElementById('profileName').value = profile.name || '';
    document.getElementById('profileEmail').value = profile.email || '';
    document.getElementById('profileSkills').value = profile.skills?.join(', ') || '';
    document.getElementById('profileExperience').value = profile.experience || '';
    document.getElementById('profileLocation').value = profile.location || '';
    profileModal.style.display = 'flex';
}

function openCVModal() {
    document.getElementById('cvName').value = '';
    document.getElementById('cvEducation').value = '';
    document.getElementById('cvExperience').value = '';
    document.getElementById('cvSkills').value = '';
    cvModal.style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

async function fetchProfiles() {
    const token = localStorage.getItem('token');
    try {
        const response = await fetch(`${API_URL}/profiles`, {
            headers: token ? { 'x-auth-token': token } : {},
        });
        profiles = await response.json();
        renderProfiles(profiles);
    } catch (error) {
        console.error('Error fetching profiles:', error);
    }
}

function renderProfiles(profilesToRender) {
    const profileList = document.getElementById('profileList');
    profileList.innerHTML = '';
    profilesToRender.forEach(profile => {
        const profileCard = document.createElement('div');
        profileCard.className = 'profile-card';
        profileCard.innerHTML = `
            <h3>${profile.name}</h3>
            <p>Email: ${profile.email}</p>
            <div class="skills">
                ${profile.skills.map(skill => `<span>${skill}</span>`).join('')}
            </div>
            <p>Kinh nghiệm: ${profile.experience} năm</p>
            <p>Địa điểm: ${profile.location}</p>
            <div class="actions">
                <button class="modal-btn" onclick='openProfileModal("edit", ${JSON.stringify(profile)})'>Chỉnh sửa</button>
                ${profile.user.cvs?.length ? `<button class="modal-btn" onclick='viewCV(${JSON.stringify(profile.user.cvs[0])})'>Xem CV</button>` : ''}
            </div>
        `;
        profileList.appendChild(profileCard);
    });
}

async function searchProfiles() {
    const searchTerm = document.getElementById('profileSearch').value;
    const skillFilter = document.getElementById('skillFilter').value;
    const experienceFilter = document.getElementById('experienceFilter').value;
    const locationFilter = document.getElementById('locationFilter').value;

    try {
        const response = await fetch(
            `${API_URL}/profiles?search=${searchTerm}&skills=${skillFilter}&experience=${experienceFilter}&location=${locationFilter}`
        );
        const filteredProfiles = await response.json();
        renderProfiles(filteredProfiles);
    } catch (error) {
        console.error('Error searching profiles:', error);
    }
}

document.getElementById('profileForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Vui lòng đăng nhập!');
        return;
    }

    const profileData = {
        name: document.getElementById('profileName').value,
        email: document.getElementById('profileEmail').value,
        skills: document.getElementById('profileSkills').value,
        experience: parseInt(document.getElementById('profileExperience').value),
        location: document.getElementById('profileLocation').value,
    };

    const method = document.getElementById('profileId').value ? 'PUT' : 'POST';
    const url = document.getElementById('profileId').value
        ? `${API_URL}/profiles/${document.getElementById('profileId').value}`
        : `${API_URL}/profiles`;

    try {
        const response = await fetch(url, {
            method,
            headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token,
            },
            body: JSON.stringify(profileData),
        });
        if (response.ok) {
            alert('Hồ sơ đã được lưu!');
            closeModal('profileModal');
            fetchProfiles();
        } else {
            const data = await response.json();
            alert(data.message);
        }
    } catch (error) {
        alert('Đã có lỗi xảy ra!');
    }
});

document.getElementById('cvForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Vui lòng đăng nhập!');
        return;
    }

    const cvData = {
        name: document.getElementById('cvName').value,
        education: document.getElementById('cvEducation').value,
        experience: document.getElementById('cvExperience').value,
        skills: document.getElementById('cvSkills').value,
    };

    try {
        const response = await fetch(`${API_URL}/cvs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token,
            },
            body: JSON.stringify(cvData),
        });
        if (response.ok) {
            alert('CV đã được lưu!');
            closeModal('cvModal');
            fetchProfiles();
        } else {
            const data = await response.json();
            alert(data.message);
        }
    } catch (error) {
        alert('Đã có lỗi xảy ra!');
    }
});

function viewCV(cv) {
    const cvDetails = document.getElementById('cvDetails');
    cvDetails.innerHTML = `
        <h4>Họ và Tên</h4>
        <p>${cv.name}</p>
        <h4>Học vấn</h4>
        <p>${cv.education}</p>
        <h4>Kinh nghiệm làm việc</h4>
        <p>${cv.experience}</p>
        <h4>Kỹ năng</h4>
        <p>${cv.skills.join(', ')}</p>
    `;
    viewCVModal.style.display = 'flex';
}

// Counter Animation
let hasAnimated = false;

function animateCounters() {
    if (hasAnimated) return; // Chỉ chạy animation một lần

    const counters = document.querySelectorAll('.counter');
    const speed = 200;

    counters.forEach(counter => {
        const text = counter.parentElement.textContent;
        const isPercentage = text.includes('%');

        // Xác định giá trị mục tiêu
        let finalTarget = parseInt(counter.getAttribute('data-target'));

        // Đặt giá trị mặc định nếu không có data-target
        if (!finalTarget) {
            if (isPercentage) {
                finalTarget = 95;
            } else if (text.includes('Việc làm')) {
                finalTarget = 10;
            } else if (text.includes('Công ty')) {
                finalTarget = 5;
            } else if (text.includes('Ứng viên')) {
                finalTarget = 5;
            }
            counter.setAttribute('data-target', finalTarget);
        }

        // Bắt đầu từ 0
        let count = 0;

        // Hàm cập nhật số đếm
        const updateCount = () => {
            const increment = finalTarget / speed;

            if (count < finalTarget) {
                count += increment;
                counter.innerText = Math.ceil(count);
                requestAnimationFrame(updateCount);
            } else {
                counter.innerText = finalTarget;
            }
        };

        // Bắt đầu đếm
        updateCount();
    });

    hasAnimated = true; // Đánh dấu đã chạy animation
}

// Intersection Observer for counter animation
const statsSection = document.querySelector('.stats-section');
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            setTimeout(() => {
                animateCounters();
            }, 500); // Đợi 500ms để đảm bảo DOM đã sẵn sàng
            observer.unobserve(entry.target);
        }
    });
}, { threshold: 0.2 }); // Giảm ngưỡng để kích hoạt sớm hơn

if (statsSection) {
    observer.observe(statsSection);
}

// Đảm bảo animation chạy ngay cả khi không có Intersection Observer
setTimeout(() => {
    if (!hasAnimated) {
        animateCounters();
    }
}, 1000);

// Category Navigation with Auto Slide
const categoryNavBtns = document.querySelectorAll('.category-nav-btn');
const categoriesWrappers = document.querySelectorAll('.categories-wrapper');

if (categoryNavBtns.length === 2 && categoriesWrappers.length === 2) {
    let currentCategoryPage = 0;
    const totalPages = 2;
    let autoSlideInterval;
    const autoSlideDelay = 8000; // 8 seconds

    // Add transition effect to wrappers
    categoriesWrappers.forEach(wrapper => {
        wrapper.style.transition = 'opacity 0.8s ease-in-out';
    });

    // Hide second page initially
    categoriesWrappers[1].style.opacity = '0';
    categoriesWrappers[1].style.display = 'none';

    // Previous button
    categoryNavBtns[0].addEventListener('click', () => {
        if (currentCategoryPage > 0) {
            currentCategoryPage--;
            updateCategoryDisplay();
            resetAutoSlide();
        }
    });

    // Next button
    categoryNavBtns[1].addEventListener('click', () => {
        if (currentCategoryPage < totalPages - 1) {
            currentCategoryPage++;
            updateCategoryDisplay();
            resetAutoSlide();
        }
    });

    function updateCategoryDisplay() {
        // Fade out current page
        categoriesWrappers.forEach((wrapper) => {
            wrapper.style.opacity = '0';
        });

        // After fade out, change display and fade in new page
        setTimeout(() => {
            categoriesWrappers.forEach((wrapper, index) => {
                if (index === currentCategoryPage) {
                    wrapper.style.display = 'flex';
                    setTimeout(() => {
                        wrapper.style.opacity = '1';
                    }, 100);
                } else {
                    wrapper.style.display = 'none';
                }
            });
        }, 800);
    }

    function autoSlide() {
        currentCategoryPage = (currentCategoryPage + 1) % totalPages;
        updateCategoryDisplay();
    }

    function startAutoSlide() {
        autoSlideInterval = setInterval(autoSlide, autoSlideDelay);
    }

    function resetAutoSlide() {
        clearInterval(autoSlideInterval);
        startAutoSlide();
    }

    // Start auto slide
    startAutoSlide();

    // Pause auto slide when hovering over categories
    const categoriesSection = document.querySelector('.categories-section');
    categoriesSection.addEventListener('mouseenter', () => {
        clearInterval(autoSlideInterval);
    });

    categoriesSection.addEventListener('mouseleave', () => {
        startAutoSlide();
    });
}

// Function to toggle other location input field
function toggleOtherLocation() {
    const locationSelect = document.getElementById('jobLocation');
    const otherLocationGroup = document.getElementById('otherLocationGroup');

    if (locationSelect && otherLocationGroup) {
        if (locationSelect.value === 'other') {
            otherLocationGroup.style.display = 'block';
            // Add animation
            otherLocationGroup.style.animation = 'fadeIn 0.3s ease';
        } else {
            otherLocationGroup.style.display = 'none';
        }
    }
}

// Thêm event listener cho các nút tìm kiếm
document.addEventListener('DOMContentLoaded', function() {
    // Nút tìm kiếm chính
    const mainSearchBtn = document.getElementById('main-search-btn');
    if (mainSearchBtn) {
        mainSearchBtn.addEventListener('click', function() {
            fetchJobs(false);
            showSection('jobs');
        });
    }

    // Thêm sự kiện Enter cho ô tìm kiếm chính
    const mainSearchInput = document.getElementById('main-search-input');
    if (mainSearchInput) {
        mainSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                fetchJobs(false);
                showSection('jobs');
            }
        });
    }

    // Nút tìm kiếm nâng cao
    const advancedSearchBtn = document.getElementById('advanced-search-btn');
    if (advancedSearchBtn) {
        advancedSearchBtn.addEventListener('click', function() {
            fetchJobs(true);
        });
    }

    // Thêm sự kiện Enter cho ô tìm kiếm nâng cao
    const jobKeyword = document.getElementById('jobKeyword');
    if (jobKeyword) {
        jobKeyword.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                fetchJobs(true);
            }
        });
    }

    // Nút xóa bộ lọc
    const resetSearchBtn = document.getElementById('reset-search-btn');
    if (resetSearchBtn) {
        resetSearchBtn.addEventListener('click', function() {
            // Xóa các trường tìm kiếm
            document.getElementById('jobKeyword').value = '';
            document.getElementById('jobLocation').value = '';
            document.getElementById('jobSalary').value = '';
            document.getElementById('jobSkills').value = '';
            document.getElementById('jobExperience').value = '';
            document.getElementById('jobType').value = '';
            document.getElementById('jobPosted').value = '';

            // Ẩn trường nhập địa điểm khác
            document.getElementById('otherLocationGroup').style.display = 'none';

            // Xóa các thẻ lọc
            document.querySelector('.filter-tags').innerHTML = '';

            // Hiển thị tất cả công việc
            fetchJobs(true);
        });
    }

    // Thêm sự kiện thay đổi cho các dropdown để tìm kiếm tự động
    const filterDropdowns = document.querySelectorAll('#locationFilter, #experienceFilter');
    filterDropdowns.forEach(dropdown => {
        dropdown.addEventListener('change', function() {
            fetchJobs(false);
        });
    });
});

// Cập nhật hàm renderJobs để hiển thị các công việc với logo
function renderJobs(jobs) {
    const jobList = document.querySelector('.job-list');
    jobList.innerHTML = '';

    if (jobs.length === 0) {
        jobList.innerHTML = '<div class="no-jobs">Không tìm thấy công việc phù hợp</div>';
        return;
    }

    jobs.forEach(job => {
        const jobItem = document.createElement('div');
        jobItem.className = 'job-item';
        jobItem.innerHTML = `
            <img src="${job.logo || 'placeholder.jpg'}" alt="${job.company} Logo">
            <div class="job-info">
                <h3>${job.title}</h3>
                <p class="company">${job.company}</p>
                <p class="level">Cập nhật ${new Date(job.createdAt).toLocaleString()}</p>
                <div class="skills">
                    ${job.skills.map(skill => `<span>${skill}</span>`).join('')}
                </div>
                <div class="details">
                    <span><i class="fas fa-map-marker-alt"></i> ${job.location}</span>
                    <span class="days-left"><i class="fas fa-clock"></i> ${job.deadline ? `Còn ${Math.ceil((new Date(job.deadline) - new Date()) / (1000 * 60 * 60 * 24))} ngày` : 'Không thời hạn'}</span>
                </div>
            </div>
            <div class="job-actions">
                <span class="salary">${job.salary || 'Thỏa thuận'}</span>
                <button class="apply-btn">Ứng tuyển</button>
                <i class="fas fa-heart favorite"></i>
            </div>
        `;
        jobList.appendChild(jobItem);
    });
}

// Initial State
showSection('home');

// Hiển thị các công việc mặc định khi vào trang Jobs
document.addEventListener('DOMContentLoaded', function() {
    // Thêm event listener cho nút "Việc làm" trong menu
    const jobsNavLink = document.querySelector('.nav-links a[onclick="showSection(\'jobs\')"]');
    if (jobsNavLink) {
        jobsNavLink.addEventListener('click', function() {
            fetchJobs(false);
        });
    }
});

// Advanced Search Toggle
document.addEventListener('DOMContentLoaded', function() {
    const advancedSearchToggle = document.getElementById('advancedSearchToggle');
    const advancedOptions = document.getElementById('advancedOptions');

    if (advancedSearchToggle && advancedOptions) {
        advancedSearchToggle.addEventListener('click', function() {
            advancedOptions.classList.toggle('active');
            this.classList.toggle('active');

            // Change icon direction
            const icon = this.querySelector('i');
            if (advancedOptions.classList.contains('active')) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        });
    }

    // Initialize other location field
    toggleOtherLocation();

    // Filter tag removal
    const filterTags = document.querySelectorAll('.filter-tag');
    filterTags.forEach(tag => {
        const closeIcon = tag.querySelector('i');
        if (closeIcon) {
            closeIcon.addEventListener('click', function(e) {
                e.stopPropagation();
                tag.remove();
            });
        }
    });

    // Reset button functionality
    const resetBtn = document.querySelector('.reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            // Reset all form inputs
            const searchForm = document.querySelector('.advanced-search-form');
            const advancedForm = document.querySelector('.advanced-options-grid');

            if (searchForm) {
                const inputs = searchForm.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    }
                });
            }

            if (advancedForm) {
                const inputs = advancedForm.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'text' || input.type === 'search') {
                        input.value = '';
                    } else if (input.tagName === 'SELECT') {
                        input.selectedIndex =  p
                    }
                });
            }

            // Remove all filter tags
            const filterTags = document.querySelectorAll('.filter-tag');
            filterTags.forEach(tag => {
                tag.remove();
                
            });
        });
    }
});
    </script>
</body>
</html>