#định dạng bằng toán tử %:(thay giá trị của %s thành % value out str)
#mỗi toán tử %s tương ứng với 1 giá trị trong chuỗi (và giá trị trong chuỗi được cách nhau = dấu phẩy)
a = 'My name is %s %s' %("<PERSON>","Hau")
print(a)
b = "%s %s" 
#có thể gán vào  1 biến khác và tái sử dụng nó 
result = b %('Van','Hau')
print (result)
#có thể thay %d(int) or %f(float) but phải đúng thứ tự
c = "%s is %d problems" %('That',1)
print(c)
#định dạng bằng chuỗi f(f-string)
