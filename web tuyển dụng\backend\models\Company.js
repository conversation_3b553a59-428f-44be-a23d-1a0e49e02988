const mongoose = require('mongoose');

const companySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true
    },
    logo: {
        type: String
    },
    website: {
        type: String,
        trim: true
    },
    industry: {
        type: String,
        required: true
    },
    size: {
        type: String,
        enum: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
    },
    foundedYear: {
        type: Number
    },
    location: {
        type: String,
        required: true
    },
    contactEmail: {
        type: String,
        required: true,
        trim: true
    },
    contactPhone: {
        type: String,
        trim: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    isVerified: {
        type: Boolean,
        default: false
    }
});

// Index for faster queries
companySchema.index({ name: 1 }, { unique: true });
companySchema.index({ industry: 1 });
companySchema.index({ location: 1 });

module.exports = mongoose.model('Company', companySchema); 