/* Job Listing Styles */

.job-listing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.job-listing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.job-listing-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    position: relative;
    padding-bottom: 0.5rem;
}

.job-listing-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-full);
}

.job-listing-view-all {
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
}

.job-listing-view-all:hover {
    color: var(--primary-dark);
    transform: translateX(3px);
}

.job-listing-view-all i {
    font-size: 0.875rem;
}

/* Job Card Styles */
.job-card {
    display: flex;
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    position: relative;
}

.job-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-lightest);
}

.job-card-left {
    width: 100px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-lighter);
    border-right: 1px solid var(--border-color);
}

.job-card-logo {
    width: 70px;
    height: 70px;
    object-fit: contain;
    border-radius: var(--radius-md);
    background-color: var(--bg-white);
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.job-card-middle {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.job-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.job-card-company {
    font-size: 1rem;
    color: var(--text-medium);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.job-card-company i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.job-card-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 0.5rem;
}

.job-card-detail {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #00a550;
}

.job-card-detail i {
    margin-right: 0.5rem;
    color: #00a550;
    width: 1rem;
    text-align: center;
}

.job-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.job-card-tag {
    background-color: var(--primary-lightest);
    color: var(--primary-dark);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.job-card-right {
    width: 180px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    background-color: var(--bg-lighter);
    border-left: 1px solid var(--border-color);
}

.job-card-salary {
    font-weight: 600;
    color: var(--success-color);
    font-size: 1.125rem;
    text-align: center;
}

.job-card-apply {
    background-color: var(--primary-color);
    color: var(--bg-white);
    padding: 0.625rem 1.25rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-normal);
    text-align: center;
    display: block;
    width: 100%;
}

.job-card-apply:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.job-card-deadline {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.job-card-deadline i {
    margin-right: 0.25rem;
    color: var(--warning-color);
}

/* Responsive styles */
@media (max-width: 992px) {
    .job-card {
        flex-direction: column;
    }

    .job-card-left {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 1rem;
    }

    .job-card-right {
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--border-color);
        flex-direction: row;
        justify-content: space-between;
        padding: 1rem;
    }

    .job-card-salary {
        text-align: left;
    }

    .job-card-apply {
        width: auto;
    }
}

@media (max-width: 768px) {
    .job-card-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .job-card-right {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-card-apply {
        width: 100%;
    }
}
