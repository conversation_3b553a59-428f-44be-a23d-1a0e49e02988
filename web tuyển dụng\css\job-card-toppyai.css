/* Job Card TopPyAI Style */

:root {
    --primary-green: #00a550;
    --primary-light-green: #e6f7ee;
    --primary-dark-green: #008040;
    --text-dark: #333333;
    --text-medium: #555555;
    --text-light: #777777;
    --border-color: #e0e0e0;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-lighter: #f2f2f2;
    --hot-color: #ff4d4d;
    --top-color: #00a550;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

.job-listing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.job-listing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.job-listing-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-dark);
    position: relative;
}

.job-listing-view-all {
    color: var(--primary-green);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.job-listing-view-all:hover {
    color: var(--primary-dark-green);
}

.job-listing-view-all i {
    font-size: 0.875rem;
}

/* Filter Bar */
.filter-bar {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 0.5rem;
}

.filter-label {
    display: flex;
    align-items: center;
    color: var(--text-medium);
    margin-right: 1rem;
}

.filter-options {
    display: flex;
    gap: 0.5rem;
}

.filter-option {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    color: var(--text-medium);
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-option:hover {
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.filter-option.active {
    background-color: var(--primary-green);
    color: white;
    border-color: var(--primary-green);
}

.filter-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
}

.filter-nav-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-nav-btn:hover {
    background-color: var(--primary-light-green);
    border-color: var(--primary-green);
    color: var(--primary-green);
}

/* Job Card */
.job-card-toppyai {
    display: flex;
    align-items: center;
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    position: relative;
    transition: all 0.2s ease;
}

.job-card-toppyai:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-green);
}

.job-card-logo {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-sm);
    object-fit: contain;
    border: 1px solid var(--border-color);
    padding: 0.25rem;
    background-color: var(--bg-white);
    margin-right: 1rem;
}

.job-card-content {
    flex: 1;
    min-width: 0;
}

.job-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.job-card-company {
    font-size: 0.875rem;
    color: var(--text-medium);
    margin-bottom: 0.5rem;
}

.job-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.job-card-tag {
    font-size: 0.75rem;
    color: var(--text-medium);
    background-color: var(--bg-lighter);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.job-card-info {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-left: 1rem;
}

.job-card-salary {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-right: 1rem;
    white-space: nowrap;
}

.job-card-location {
    font-size: 0.875rem;
    color: var(--primary-green);
    white-space: nowrap;
}

.job-card-save {
    margin-left: 1rem;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    border: 1px solid var(--border-color);
    background-color: var(--bg-white);
    cursor: pointer;
    transition: all 0.2s ease;
}

.job-card-save:hover {
    color: var(--primary-green);
    border-color: var(--primary-green);
    background-color: var(--primary-light-green);
}

.job-card-save.saved {
    color: var(--primary-green);
    border-color: var(--primary-green);
    background-color: var(--primary-light-green);
}

.job-card-badge {
    position: absolute;
    top: -10px;
    left: 10px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 4px;
    color: white;
}

.job-card-badge.hot {
    background-color: var(--hot-color);
}

.job-card-badge.top {
    background-color: var(--top-color);
}

/* Responsive */
@media (max-width: 768px) {
    .job-card-toppyai {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-card-logo {
        margin-bottom: 0.5rem;
    }

    .job-card-info {
        width: 100%;
        margin-left: 0;
        padding-left: 0;
        margin-top: 0.5rem;
        justify-content: space-between;
    }

    .job-card-save {
        margin-left: auto;
    }
}
