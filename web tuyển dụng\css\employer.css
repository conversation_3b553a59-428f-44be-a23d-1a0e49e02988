/* Employer Page Styles */
.employer-page {
    padding: 40px 0;
    background-color: #f5f7fa;
}

.employer-page h2 {
    text-align: center;
    font-size: 32px;
    margin-bottom: 30px;
    color: #4A90E2;
}

.employer-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.tab-btn {
    padding: 12px 25px;
    background-color: #fff;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #4A90E2;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.tab-content {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.tab-content h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

/* Post Job Form */
.job-form-group {
    margin-bottom: 20px;
}

.job-form-group label {
    display: block;
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.job-form-group input,
.job-form-group select,
.job-form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.job-form-group input:focus,
.job-form-group select:focus,
.job-form-group textarea:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.job-form-group textarea {
    resize: vertical;
    min-height: 150px;
}

.job-form-group .hint {
    font-size: 14px;
    color: #999;
    margin-top: 5px;
}

.job-form-row {
    display: flex;
    gap: 20px;
}

.job-form-row .job-form-group {
    flex: 1;
}

.job-form-submit {
    background-color: #4A90E2;
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 30px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    width: 100%;
}

.job-form-submit:hover {
    background-color: #3A78C2;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Find Profiles */
.profile-search-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.profile-search-bar input {
    flex: 1;
    padding: 12px 20px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
}

.profile-search-bar input:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.profile-search-bar button {
    background-color: #4A90E2;
    color: #fff;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-search-bar button:hover {
    background-color: #3A78C2;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.profile-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.profile-filters select {
    flex: 1;
    padding: 12px 20px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    cursor: pointer;
}

.profile-filters select:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.profile-item {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.profile-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.profile-item h4 {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
}

.profile-item p {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.profile-item .skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.profile-item .skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
}

.profile-item .view-btn {
    background-color: #4A90E2;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-item .view-btn:hover {
    background-color: #3A78C2;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Employer Dashboard */
.employer-dashboard {
    margin-top: 40px;
}

.dashboard-section {
    margin-bottom: 40px;
}

.dashboard-section h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.stat-card i {
    font-size: 30px;
    color: #4A90E2;
    margin-bottom: 15px;
}

.stat-card h4 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 14px;
    color: #666;
}

.job-postings {
    margin-bottom: 30px;
}

.job-posting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.job-posting-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.job-posting-info {
    flex: 1;
}

.job-posting-info h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

.job-posting-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.job-posting-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
}

.status-active {
    background-color: #e6f7ee;
    color: #28a745;
}

.status-expired {
    background-color: #fff5f5;
    color: #dc3545;
}

.status-draft {
    background-color: #f0f7ff;
    color: #4A90E2;
}

.job-posting-actions {
    display: flex;
    gap: 10px;
}

.job-posting-actions button {
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-posting-edit {
    background-color: #f0f7ff;
    color: #4A90E2;
    border: 1px solid #4A90E2;
}

.job-posting-edit:hover {
    background-color: #e0f0ff;
}

.job-posting-view {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.job-posting-view:hover {
    background-color: #3A78C2;
}

.job-posting-delete {
    background-color: #fff;
    color: #dc3545;
    border: 1px solid #dc3545;
}

.job-posting-delete:hover {
    background-color: #fff5f5;
}

.applications {
    margin-bottom: 30px;
}

.application-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.application-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.application-candidate {
    flex: 1;
    display: flex;
    align-items: center;
}

.application-candidate img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
}

.application-candidate-info h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 5px;
}

.application-candidate-info p {
    font-size: 14px;
    color: #666;
}

.application-job {
    flex: 1;
}

.application-job h4 {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.application-job p {
    font-size: 14px;
    color: #666;
}

.application-date {
    font-size: 14px;
    color: #999;
    margin-right: 20px;
}

.application-actions {
    display: flex;
    gap: 10px;
}

.application-actions button {
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.application-view {
    background-color: #4A90E2;
    color: #fff;
    border: none;
}

.application-view:hover {
    background-color: #3A78C2;
}

.application-approve {
    background-color: #28a745;
    color: #fff;
    border: none;
}

.application-approve:hover {
    background-color: #218838;
}

.application-reject {
    background-color: #fff;
    color: #dc3545;
    border: 1px solid #dc3545;
}

.application-reject:hover {
    background-color: #fff5f5;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .employer-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        width: 100%;
    }
    
    .job-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .profile-search-bar {
        flex-direction: column;
    }
    
    .profile-filters {
        flex-direction: column;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .job-posting-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .job-posting-status {
        margin: 15px 0;
    }
    
    .job-posting-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .application-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .application-candidate {
        margin-bottom: 15px;
    }
    
    .application-job {
        margin-bottom: 15px;
    }
    
    .application-actions {
        width: 100%;
        justify-content: space-between;
    }
}
