<?xml version="1.0" encoding="UTF-8"?>
<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient background -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#2196F3"/>
      <stop offset="100%" stop-color="#0D47A1"/>
    </linearGradient>
    <filter id="shadow" x="-2" y="0" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
  
  <!-- Main circle with gradient -->
  <circle cx="20" cy="20" r="20" fill="url(#paint0_linear)"/>
  
  <!-- Modern abstract J shape -->
  <path d="M15 10C15 10 15 16 15 20C15 24 17 26 20 26C23 26 25 24 25 20" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Search icon integrated -->
  <circle cx="25" cy="15" r="5" stroke="#FFFFFF" stroke-width="2"/>
  <path d="M29 19L33 23" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"/>
  
  <!-- Decorative elements -->
  <circle cx="20" cy="20" r="12" stroke="#FFFFFF" stroke-width="0.5" stroke-dasharray="2 2"/>
  
  <!-- Accent dot -->
  <circle cx="25" cy="15" r="1.5" fill="#FFCC00"/>
</svg>
