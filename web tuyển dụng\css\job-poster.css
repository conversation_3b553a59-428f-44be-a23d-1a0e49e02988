/* Job Poster Styles */
.job-poster {
    background: #008080;
    border-radius: 8px;
    padding: 0;
    margin-top: 20px;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    color: #fff;
    width: 100%;
    height: calc(100% - 20px);
    display: flex;
    flex-direction: column;
}

.job-poster::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Cpath d="M0 0h20L0 20z"%3E%3C/path%3E%3Cpath d="M20 0v20H0z"%3E%3C/path%3E%3C/g%3E%3C/svg%3E');
    background-size: 20px 20px;
    opacity: 0.3;
    z-index: 0;
}

.poster-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    padding: 15px;
    text-align: center;
    flex: 1;
}

.poster-image {
    width: 100%;
    height: 220px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    margin-bottom: 15px;
}

.poster-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.job-poster:hover .poster-image img {
    transform: scale(1.05);
}

.poster-text {
    flex: 1;
}

.poster-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.3;
    text-align: center;
}

.poster-description {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    opacity: 0.9;
    text-align: center;
}

.poster-stats {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.poster-stat {
    display: flex;
    align-items: center;
    gap: 8px;
}

.poster-stat i {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
}

.poster-stat span {
    font-size: 15px;
    font-weight: 500;
}

.poster-cta {
    display: inline-block;
    background-color: #fff;
    color: #008080;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    margin: 15px auto;
    display: block;
    width: 80%;
}

.poster-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    background-color: #f0f0f0;
}

.poster-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: rgba(255, 255, 255, 0.9);
    color: #008080;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 5px;
    z-index: 10;
}

.poster-badge i {
    font-size: 12px;
}

@media (max-width: 768px) {
    .poster-content {
        flex-direction: column;
        text-align: center;
    }

    .poster-image {
        margin: 0 auto 20px;
    }

    .poster-stats {
        justify-content: center;
    }
}
