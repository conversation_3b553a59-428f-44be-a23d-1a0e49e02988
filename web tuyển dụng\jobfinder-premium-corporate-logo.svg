<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="logoClip">
      <rect x="10" y="10" width="80" height="80" rx="6" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="6" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Abstract Building/Network Pattern -->
  <g opacity="0.1">
    <line x1="20" y1="20" x2="80" y2="20" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="30" x2="80" y2="30" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="40" x2="80" y2="40" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="50" x2="80" y2="50" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="60" x2="80" y2="60" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="70" x2="80" y2="70" stroke="white" stroke-width="0.5"/>
    <line x1="20" y1="80" x2="80" y2="80" stroke="white" stroke-width="0.5"/>
  </g>
  
  <!-- Main Icon: Building/Network with Connection Nodes -->
  <g transform="translate(25, 20)">
    <!-- Central Building -->
    <rect x="15" y="0" width="20" height="60" rx="2" fill="white"/>
    
    <!-- Windows -->
    <rect x="20" y="5" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="12" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="19" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="26" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="33" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="40" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="47" width="10" height="3" rx="1" fill="#064e3b"/>
    <rect x="20" y="54" width="10" height="3" rx="1" fill="#064e3b"/>
    
    <!-- Connection Lines -->
    <line x1="15" y1="10" x2="0" y2="10" stroke="white" stroke-width="2"/>
    <line x1="15" y1="30" x2="0" y2="30" stroke="white" stroke-width="2"/>
    <line x1="15" y1="50" x2="0" y2="50" stroke="white" stroke-width="2"/>
    <line x1="35" y1="10" x2="50" y2="10" stroke="white" stroke-width="2"/>
    <line x1="35" y1="30" x2="50" y2="30" stroke="white" stroke-width="2"/>
    <line x1="35" y1="50" x2="50" y2="50" stroke="white" stroke-width="2"/>
    
    <!-- Connection Nodes -->
    <circle cx="0" cy="10" r="3" fill="url(#accentGradient)"/>
    <circle cx="0" cy="30" r="3" fill="url(#accentGradient)"/>
    <circle cx="0" cy="50" r="3" fill="url(#accentGradient)"/>
    <circle cx="50" cy="10" r="3" fill="url(#accentGradient)"/>
    <circle cx="50" cy="30" r="3" fill="url(#accentGradient)"/>
    <circle cx="50" cy="50" r="3" fill="url(#accentGradient)"/>
  </g>
  
  <!-- Accent Bar -->
  <rect x="10" y="85" width="80" height="5" rx="2.5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Reflective Highlight -->
  <path d="M10,10 L90,10 L90,30 Q50,40 10,30 Z" fill="white" opacity="0.1" clip-path="url(#logoClip)"/>
</svg>
