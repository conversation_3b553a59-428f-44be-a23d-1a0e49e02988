"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.writeToTrackingBuffer = writeToTrackingBuffer;
const TYPE = {
  QUERY_NOTIFICATIONS: 1,
  TXN_DESCRIPTOR: 2,
  TRACE_ACTIVITY: 3
};
const TXNDESCRIPTOR_HEADER_DATA_LEN = 4 + 8;
const TXNDESCRIPTOR_HEADER_LEN = 4 + 2 + TXNDESCRIPTOR_HEADER_DATA_LEN;

function writeToTrackingBuffer(buffer, txnDescriptor, outstandingRequestCount) {
  buffer.writeUInt32LE(0);
  buffer.writeUInt32LE(TXNDESCRIPTOR_HEADER_LEN);
  buffer.writeUInt16LE(TYPE.TXN_DESCRIPTOR);
  buffer.writeBuffer(txnDescriptor);
  buffer.writeUInt32LE(outstandingRequestCount);
  const data = buffer.data;
  data.writeUInt32LE(data.length, 0);
  return buffer;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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