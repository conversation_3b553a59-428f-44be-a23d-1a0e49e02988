/* Modern Style Enhancements */
:root {
    --primary-color: #1976d2;
    --primary-dark: #0d47a1;
    --primary-light: #63a4ff;
    --accent-color: #4caf50;
    --accent-dark: #388e3c;
    --accent-light: #81c784;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-light: #f5f5f5;
    --bg-white: #ffffff;
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.15);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --font-family: 'Inter', 'Arial', sans-serif;
}

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base Styles Enhancement */
body {
    font-family: var(--font-family);
    background-color: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Content Enhancement */
.main-content {
    padding: 40px 0 60px;
}

/* Search Section Enhancement */
.search-section {
    background-color: var(--bg-white);
    padding: 40px 30px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.search-section h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 25px;
    color: var(--text-primary);
}

.search-bar {
    display: flex;
    max-width: 700px;
    margin: 0 auto 20px;
    box-shadow: var(--shadow-sm);
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.search-bar:focus-within {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.search-bar input {
    flex: 1;
    padding: 16px 20px;
    border: 1px solid #e0e0e0;
    border-right: none;
    border-radius: var(--radius-md) 0 0 var(--radius-md);
    font-size: 16px;
    outline: none;
    transition: border-color var(--transition-normal);
}

.search-bar input:focus {
    border-color: var(--primary-light);
}

.search-btn {
    padding: 16px 25px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-btn:hover {
    background-color: var(--accent-dark);
}

.search-tagline {
    font-size: 16px;
    color: var(--text-secondary);
    margin-top: 15px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Featured Jobs Section Enhancement */
.featured-jobs {
    background-color: var(--bg-white);
    padding: 30px;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
    padding-left: 15px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 24px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: color var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-all:hover {
    color: var(--primary-dark);
}

.view-all i {
    font-size: 12px;
    transition: transform var(--transition-normal);
}

.view-all:hover i {
    transform: translateX(3px);
}

/* Filter Bar Enhancement */
.filter-bar {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    overflow-x: auto;
    padding-bottom: 10px;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-light) #eee;
}

.filter-bar::-webkit-scrollbar {
    height: 6px;
}

.filter-bar::-webkit-scrollbar-track {
    background: #eee;
    border-radius: 10px;
}

.filter-bar::-webkit-scrollbar-thumb {
    background-color: var(--primary-light);
    border-radius: 10px;
}

.filter-label {
    margin-right: 15px;
    font-size: 14px;
    color: var(--text-secondary);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-option {
    padding: 8px 16px;
    background-color: #f0f0f0;
    border-radius: var(--radius-xl);
    font-size: 14px;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    border: 1px solid transparent;
}

.filter-option:hover {
    background-color: #e0e0e0;
}

.filter-option.active {
    background-color: var(--primary-color);
    color: white;
}

/* Job Cards Enhancement */
.job-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.job-card {
    border: 1px solid #eee;
    border-radius: var(--radius-md);
    padding: 20px;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    display: flex;
    align-items: center;
    background-color: var(--bg-white);
    position: relative;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: transparent;
}

.job-logo {
    width: 70px;
    height: 70px;
    object-fit: contain;
    margin-right: 20px;
    border-radius: var(--radius-sm);
    padding: 5px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
}

.job-info {
    flex: 1;
}

.job-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
    transition: color var(--transition-normal);
}

.job-card:hover .job-title {
    color: var(--primary-color);
}

.job-company {
    font-size: 15px;
    color: var(--text-secondary);
    margin-bottom: 10px;
    font-weight: 500;
}

.job-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 13px;
    color: var(--text-light);
}

.job-detail {
    display: flex;
    align-items: center;
    gap: 5px;
}

.job-detail i {
    color: var(--primary-color);
}

.job-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12px;
    margin-left: 15px;
}

.job-salary {
    font-size: 18px;
    font-weight: 700;
    color: var(--accent-color);
    background-color: rgba(76, 175, 80, 0.1);
    padding: 5px 12px;
    border-radius: var(--radius-xl);
}

.job-save {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color var(--transition-normal);
    padding: 5px;
}

.job-save:hover {
    color: #f44336;
}

.job-save i {
    font-size: 16px;
}

/* Benefits Section */
.benefits-section {
    margin-bottom: 40px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.benefit-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.benefit-icon {
    font-size: 36px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.benefit-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.benefit-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer Enhancement */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 50px 0 30px;
    margin-top: 60px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 40px;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h3 {
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
}

.footer-section p {
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.8;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    color: white;
    font-size: 18px;
    transition: opacity var(--transition-normal), transform var(--transition-normal);
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.social-links a:hover {
    opacity: 1;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    opacity: 0.7;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .search-section h1 {
        font-size: 28px;
    }
    
    .search-bar {
        flex-direction: column;
        box-shadow: none;
    }
    
    .search-bar input {
        border-radius: var(--radius-md);
        border: 1px solid #e0e0e0;
        box-shadow: var(--shadow-sm);
    }
    
    .search-btn {
        border-radius: var(--radius-md);
        margin-top: 10px;
        justify-content: center;
    }
    
    .job-card {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .job-logo {
        margin-bottom: 15px;
        margin-right: 0;
    }
    
    .job-actions {
        align-items: flex-start;
        margin-top: 15px;
        margin-left: 0;
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .job-list {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .job-card {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .job-logo {
        margin-bottom: 15px;
        margin-right: 0;
    }
    
    .job-actions {
        align-items: flex-start;
        margin-top: 15px;
        margin-left: 0;
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (min-width: 1025px) {
    .job-list {
        grid-template-columns: 1fr;
    }
}
