/* Horizontal Blue Header Styles */
:root {
    --primary-blue: #1976d2;
    --text-white: #ffffff;
}

body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

.horizontal-header {
    background-color: var(--primary-blue);
    padding: 10px 0;
    color: var(--text-white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo-container img {
    height: 32px;
    width: auto;
    margin-right: 10px;
}

.logo-text {
    font-size: 20px;
    font-weight: bold;
    color: var(--text-white);
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: 20px;
}

.nav-link {
    color: var(--text-white);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.nav-link i {
    margin-left: 5px;
    font-size: 12px;
}

/* Mobile styles */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-menu {
        margin-top: 10px;
        width: 100%;
    }

    .nav-list {
        flex-direction: column;
        width: 100%;
    }

    .nav-item {
        margin: 5px 0;
        margin-left: 0;
    }
}
