/* Advanced Search Styles */
.advanced-search-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.advanced-search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #00a550, #52d296);
}

.advanced-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.advanced-search-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    display: flex;
    align-items: center;
}

.advanced-search-title i {
    margin-right: 10px;
    color: #00a550;
    font-size: 22px;
}

.advanced-search-toggle {
    background-color: transparent;
    border: none;
    color: #00a550;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.advanced-search-toggle:hover {
    color: #008040;
}

.advanced-search-toggle i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.advanced-search-toggle.active i {
    transform: rotate(180deg);
}

.advanced-search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.search-input-group {
    flex: 1;
    min-width: 200px;
    position: relative;
}

.search-input-group label {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
}

.search-input-group input,
.search-input-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

.search-input-group input:focus,
.search-input-group select:focus {
    border-color: #00a550;
    box-shadow: 0 0 0 3px rgba(0, 165, 80, 0.1);
    outline: none;
    background-color: #fff;
}

.search-input-group i {
    position: absolute;
    top: 38px;
    left: 15px;
    color: #777;
}

.search-input-group input[type="text"],
.search-input-group input[type="search"] {
    padding-left: 40px;
}

/* Other Location Input Animation */
#otherLocationGroup {
    transition: all 0.3s ease;
    overflow: hidden;
    margin-top: 0;
}

#otherLocationGroup.show {
    margin-top: 15px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-input-group select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23777" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.search-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.search-btn {
    background-color: #00a550;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-btn:hover {
    background-color: #008040;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 165, 80, 0.2);
}

.search-btn i {
    margin-right: 8px;
}

.reset-btn {
    background-color: transparent;
    color: #555;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background-color: #f5f5f5;
    color: #333;
}

.advanced-options {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    display: none;
}

.advanced-options.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.advanced-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.filter-tag {
    background-color: #f0f7f4;
    border: 1px solid #d0e9df;
    border-radius: 20px;
    padding: 6px 15px;
    font-size: 13px;
    color: #00a550;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.filter-tag i {
    margin-left: 5px;
    font-size: 12px;
    cursor: pointer;
}

.filter-tag:hover {
    background-color: #e0f2ea;
}

.search-results-info {
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}

.search-results-info strong {
    color: #00a550;
    font-weight: 600;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive styles */
@media (max-width: 768px) {
    .advanced-search-form {
        flex-direction: column;
    }

    .search-input-group {
        width: 100%;
    }

    .search-actions {
        flex-direction: column;
    }

    .advanced-options-grid {
        grid-template-columns: 1fr;
    }
}
