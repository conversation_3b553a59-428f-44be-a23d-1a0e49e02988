{"cells": [{"cell_type": "markdown", "id": "e3ce5517-3842-48bb-8bcd-4f0c2801941f", "metadata": {}, "source": ["<p style=\"text-align:center\">\n", "    <a href=\"https://skills.network\" target=\"_blank\">\n", "    <img src=\"https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/assets/logos/SN_web_lightmode.png\" width=\"200\" alt=\"Skills Network Logo\">\n", "    </a>\n", "</p>\n"]}, {"cell_type": "markdown", "id": "dce782b3-f9c2-4734-8b51-9a384a162c9a", "metadata": {}, "source": ["# **Exception Handling**\n", "\n", "Estimated time needed: **15** minutes\n", "    \n", "\n", "## Objectives\n", "\n", "After completing this lab you will be able to:\n", "\n", "* Understand exceptions    \n", "* Handle the exceptions\n"]}, {"cell_type": "markdown", "id": "6a2c0e0a-9943-4789-a3ef-e5de6e89d51e", "metadata": {}, "source": ["## Table of Contents\n"]}, {"cell_type": "markdown", "id": "8987c81f-ec66-4f14-b49b-3daf1dfa8812", "metadata": {}, "source": ["* What is an Exception?\n", "* Exception Handling\n"]}, {"cell_type": "markdown", "id": "ee3a7264-aa3b-49ea-bb79-1c0d1bcf1b9a", "metadata": {}, "source": ["----\n"]}, {"cell_type": "markdown", "id": "0ca97c19-9ebb-4dfa-9dda-07d630628390", "metadata": {}, "source": ["## What is an Exception?\n"]}, {"cell_type": "markdown", "id": "7453596c-34bc-40af-846b-e81041f3a558", "metadata": {}, "source": ["In this section you will learn about what an exception is and see examples of them.\n"]}, {"cell_type": "markdown", "id": "2b9aa089-98e7-4eb0-b588-fb955d481a89", "metadata": {}, "source": ["### Definition\n"]}, {"cell_type": "markdown", "id": "5a54d9df-1fe6-40e0-8a21-d89c02efb388", "metadata": {}, "source": ["An exception is an error that occurs during the execution of code. This error causes the code to raise an exception and if not prepared to handle it will halt the execution of the code.\n"]}, {"cell_type": "markdown", "id": "cc02ecf0-32b7-4af7-a3cd-12d6ca2d7365", "metadata": {}, "source": ["### Examples\n"]}, {"cell_type": "markdown", "id": "cd763f93-83d7-4516-9569-a9f85ef8b4d3", "metadata": {}, "source": ["Run each piece of code and observe the exception raised\n"]}, {"cell_type": "code", "execution_count": null, "id": "87f4e8f2-c879-43c5-8c12-1224978f03b4", "metadata": {}, "outputs": [], "source": ["1/0"]}, {"cell_type": "markdown", "id": "7eb1681b-346f-4396-8438-8bf9bf49750c", "metadata": {}, "source": ["<code>ZeroDivisionError</code> occurs when you try to divide by zero.\n"]}, {"cell_type": "code", "execution_count": null, "id": "f4887415-8a19-498e-9df2-3e2c48157209", "metadata": {}, "outputs": [], "source": ["y = a + 5"]}, {"cell_type": "markdown", "id": "5216dcd8-4dcd-42b8-9a03-3ab4c2f58a60", "metadata": {}, "source": ["<code>NameError</code> -- in this case, it means that you tried to use the variable a when it was not defined.\n"]}, {"cell_type": "code", "execution_count": null, "id": "12cc6066-3fd6-4ea3-a6fe-81eb3a7b1205", "metadata": {}, "outputs": [], "source": ["a = [1, 2, 3]\n", "a[10]"]}, {"cell_type": "markdown", "id": "544ba735-a45a-4484-9282-0af8a5c353f2", "metadata": {}, "source": ["<code>IndexError</code> -- in this case, it occured because you tried to access data from a list using an index that does not exist for this list.\n"]}, {"cell_type": "markdown", "id": "5c6e0d9f-e0fd-4281-a7db-f1252e155921", "metadata": {}, "source": ["There are many more exceptions that are built into Python, here is a list of them https://docs.python.org/3/library/exceptions.html\n"]}, {"cell_type": "markdown", "id": "9afc2b7d-c1ad-4972-a366-fa01fa2a9fbd", "metadata": {}, "source": ["## Exception Handling\n"]}, {"cell_type": "markdown", "id": "76169e7c-d03e-4937-b30f-90cb9adcd19a", "metadata": {}, "source": ["In this section you will learn how to handle exceptions. You will understand how to make your program perform specified tasks instead of halting code execution when an exception is encountered.\n"]}, {"cell_type": "markdown", "id": "c2bc0845-fc1b-4cf4-9fb3-e3e247a41fe3", "metadata": {}, "source": ["### Try Except\n"]}, {"cell_type": "markdown", "id": "31023533-37ef-45b3-a771-94f3c57d4aa1", "metadata": {}, "source": ["A <code>try except</code> will allow you to execute code that might raise an exception and in the case of any exception or a specific one we can handle or catch the exception and execute specific code. This will allow us to continue the execution of our program even if there is an exception.\n", "\n", "Python tries to execute the code in the <code>try</code> block. In this case if there is any exception raised by the code in the <code>try</code> block, it will be caught and the code block in the <code>except</code> block will be executed. After that, the code that comes <em>after</em> the try except will be executed.\n"]}, {"cell_type": "code", "execution_count": null, "id": "69887421-969e-41ca-9e53-7b28ee38f202", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except:\n", "    # code to execute if there is an exception\n", "    \n", "# code that will still execute if there is an exception"]}, {"cell_type": "markdown", "id": "772cb691-a87e-4289-a1c9-6e1d878af471", "metadata": {}, "source": ["### Try Except Example\n"]}, {"cell_type": "markdown", "id": "69df7a84-2138-4ee5-bdd6-c3b6d649a98c", "metadata": {}, "source": ["In this example we are trying to divide a number given by the user, save the outcome in the variable <code>a</code>, and then we would like to print the result of the operation. When taking user input and dividing a number by it there are a couple of exceptions that can be raised. For example if we divide by zero. Try running the following block of code with <code>b</code> as a number. An exception will only be raised if <code>b</code> is zero.\n"]}, {"cell_type": "code", "execution_count": null, "id": "5fc4522c-d4aa-46c3-8e08-3313417aad56", "metadata": {}, "outputs": [], "source": ["a = 1\n", "\n", "try:\n", "    b = int(input(\"Please enter a number to divide a\"))\n", "    a = a/b\n", "    print(\"Success a=\",a)\n", "except:\n", "    print(\"There was an error\")\n", "        \n"]}, {"cell_type": "markdown", "id": "0de42528-2b3b-40d1-8f33-5a90d47ff88c", "metadata": {}, "source": ["### Try Except Specific\n"]}, {"cell_type": "markdown", "id": "f2e782cc-9fc0-4ba5-955e-ee1d95412996", "metadata": {}, "source": ["A specific <code>try except</code> allows you to catch certain exceptions and also execute certain code depending on the exception. This is useful if you do not want to deal with some exceptions and the execution should halt. It can also help you find errors in your code that you might not be aware of. Furthermore, it can help you differentiate responses to different exceptions. In this case, the code after the try except might not run depending on the error.\n"]}, {"cell_type": "markdown", "id": "d192a6b3-90f1-4455-a53c-df9087ccd9dc", "metadata": {}, "source": ["<b>Do not run, just to illustrate:</b>\n"]}, {"cell_type": "code", "execution_count": null, "id": "01a357c8-2143-4305-9191-9059c4a991a2", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "    # code to execute if there is an exception of the given types\n", "    \n", "# code that will execute if there is no exception or a one that we are handling"]}, {"cell_type": "code", "execution_count": null, "id": "54a265b6-c117-4275-ab5f-23bb3879bdd5", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except ZeroDivisionError:\n", "    # code to execute if there is a ZeroDivisionError\n", "except NameError:\n", "    # code to execute if there is a NameError\n", "    \n", "# code that will execute if there is no exception or a one that we are handling"]}, {"cell_type": "markdown", "id": "2c9a0dd4-12d0-47ff-bddf-f0456198b3c6", "metadata": {}, "source": ["You can also have an empty <code>except</code> at the end to catch an unexpected exception:\n"]}, {"cell_type": "markdown", "id": "0488823d-9915-40c0-a696-a2a6aa8592cc", "metadata": {}, "source": ["<b>Do not run, just to illustrate:</b>\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e2790b8-0cd4-4274-9e7d-1f3e5adb600b", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except ZeroDivisionError:\n", "    # code to execute if there is a ZeroDivisionError\n", "except NameError:\n", "    # code to execute if there is a NameError\n", "except:\n", "    # code to execute if ther is any exception\n", "    \n", "# code that will execute if there is no exception or a one that we are handling"]}, {"cell_type": "markdown", "id": "d49604b7-a9e1-4d75-8d80-4437a3d5a71f", "metadata": {}, "source": ["### Try Except Specific Example\n"]}, {"cell_type": "markdown", "id": "71393f3a-d0be-44b3-8684-3a1a83d19258", "metadata": {}, "source": ["This is the same example as above, but now we will add differentiated messages depending on the exception, letting the user know what is wrong with the input.\n"]}, {"cell_type": "code", "execution_count": null, "id": "4a3ec1a4-d2a8-4eee-8ce4-28d5af044262", "metadata": {}, "outputs": [], "source": ["a = 1\n", "\n", "try:\n", "    b = int(input(\"Please enter a number to divide a\"))\n", "    a = a/b\n", "    print(\"Success a=\",a)\n", "except ZeroDivisionError:\n", "    print(\"The number you provided cant divide 1 because it is 0\")\n", "except ValueError:\n", "    print(\"You did not provide a number\")\n", "except:\n", "    print(\"Something went wrong\")\n", "        \n"]}, {"cell_type": "markdown", "id": "703798e4-e388-4852-b035-5e6e7770301f", "metadata": {}, "source": ["### Try Except <PERSON><PERSON> and <PERSON>\n"]}, {"cell_type": "markdown", "id": "c8167c06-e06e-4dcf-bc42-22a974727daf", "metadata": {}, "source": ["<code>else</code> allows one to check if there was no exception when executing the try block. This is useful when we want to execute something only if there were no errors.\n"]}, {"cell_type": "markdown", "id": "24de9b62-53c2-4ffc-be08-1a4d4d99fc20", "metadata": {}, "source": ["<b>do not run, just to illustrate</b>\n"]}, {"cell_type": "code", "execution_count": null, "id": "d28f5ab2-12d8-41e6-a475-4d80f1b5e0bd", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except ZeroDivisionError:\n", "    # code to execute if there is a ZeroDivisionError\n", "except NameError:\n", "    # code to execute if there is a NameError\n", "except:\n", "    # code to execute if ther is any exception\n", "else:\n", "    # code to execute if there is no exception\n", "    \n", "# code that will execute if there is no exception or a one that we are handling"]}, {"cell_type": "markdown", "id": "a7f70677-f83f-4937-8ded-14f130b0946e", "metadata": {}, "source": ["<code>finally</code> allows us to always execute something even if there is an exception or not. This is usually used to signify the end of the try except.\n"]}, {"cell_type": "code", "execution_count": null, "id": "d72f42d3-48c7-457a-8bd3-78a1f8577d7c", "metadata": {}, "outputs": [], "source": ["# potential code before try catch\n", "\n", "try:\n", "    # code to try to execute\n", "except ZeroDivisionError:\n", "    # code to execute if there is a ZeroDivisionError\n", "except NameError:\n", "    # code to execute if there is a NameError\n", "except:\n", "    # code to execute if ther is any exception\n", "else:\n", "    # code to execute if there is no exception\n", "finally:\n", "    # code to execute at the end of the try except no matter what\n", "    \n", "# code that will execute if there is no exception or a one that we are handling"]}, {"cell_type": "markdown", "id": "7d3ac648-2ec1-4563-afb5-7537e88ec8f5", "metadata": {}, "source": ["### Try Except <PERSON><PERSON> and Finally Example\n"]}, {"cell_type": "markdown", "id": "3926364e-9a00-4263-bc91-323795d71445", "metadata": {}, "source": ["You might have noticed that even if there is an error the value of <code>a</code> is always printed. Let's use the <code>else</code> and print the value of <code>a</code> only if there is no error.\n"]}, {"cell_type": "code", "execution_count": null, "id": "22ccff74-a80e-4751-8e64-bd3642a571c2", "metadata": {}, "outputs": [], "source": ["a = 1\n", "\n", "try:\n", "    b = int(input(\"Please enter a number to divide a\"))\n", "    a = a/b\n", "except ZeroDivisionError:\n", "    print(\"The number you provided cant divide 1 because it is 0\")\n", "except ValueError:\n", "    print(\"You did not provide a number\")\n", "except:\n", "    print(\"Something went wrong\")\n", "else:\n", "    print(\"success a=\",a)"]}, {"cell_type": "markdown", "id": "32d34382-e3ec-400d-918f-7fd6d993da33", "metadata": {}, "source": ["Now lets let the user know that we are done processing their answer. Using the <code>finally</code>, let's add a print.\n"]}, {"cell_type": "code", "execution_count": null, "id": "de1828d1-4470-4bed-b3aa-e82eeee56f1f", "metadata": {}, "outputs": [], "source": ["a = 1\n", "\n", "try:\n", "    b = int(input(\"Please enter a number to divide a\"))\n", "    a = a/b\n", "except ZeroDivisionError:\n", "    print(\"The number you provided cant divide 1 because it is 0\")\n", "except ValueError:\n", "    print(\"You did not provide a number\")\n", "except:\n", "    print(\"Something went wrong\")\n", "else:\n", "    print(\"success a=\",a)\n", "finally:\n", "    print(\"Processing Complete\")"]}, {"cell_type": "markdown", "id": "3232c3f5-ac00-45a7-a1ee-390d5378cb34", "metadata": {}, "source": ["<center>\n", "    \n", "    \n", "# Practice Exercises \n", "\n", "</center>\n"]}, {"cell_type": "markdown", "id": "7119dee5-2672-4b09-ab6e-6cabf105227d", "metadata": {}, "source": ["## Exercise 1: Handling ZeroDivisionError\n"]}, {"cell_type": "markdown", "id": "1aac5ead-5565-403a-b26f-f98f2baaea32", "metadata": {}, "source": ["Imagine you have two numbers and want to determine what happens when you divide one number by the other. To do this, you need to create a Python function called `safe_divide.` You give this function two numbers, a `'numerator'` and a `'denominator'`. The 'numerator' is the number you want to divide, and the `'denominator'` is the number you want to divide by. Use the user input method of Python to take the values.\n", "\n", "The function should be able to do the division for you and give you the result. But here's the catch: if you try to divide by zero (which is not allowed in math), the function should be smart enough to catch that and tell you that it's not possible to divide by zero. Instead of showing an error, it should return None, which means 'nothing' or 'no value', and print `\"Error: Cannot divide by Zero.`\n"]}, {"cell_type": "code", "execution_count": 19, "id": "0fde953b-80b2-4833-b6d3-189d89fab570", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Result: 2.0\n"]}], "source": ["def safe_divide(numerator, denominator):\n", "    if denominator == 0:\n", "        print(\"Error: Cannot divide by Zero\")\n", "        return None\n", "    return numerator / denominator\n", "num = 10 \n", "den = 5\n", "result = safe_divide(num, den)\n", "if result is not None:\n", "    print (\"Result:\" , result)\n", "\n", "    "]}, {"cell_type": "markdown", "id": "97e1bc64-2fa8-41fb-823a-58f8dbd84676", "metadata": {}, "source": ["**Note:- Practice handling exceptions by trying different input types like using integers, strings, zero, negative values, or other data types.**\n"]}, {"cell_type": "markdown", "id": "6c5f519d-e1ce-4116-b9a7-db0155044b62", "metadata": {}, "source": ["<details><summary>Click here for the hints</summary>\n", "\n", "```python\n", "Follow these: -\n", "* Define a function to perform the division and use two arguments.\n", "* Use the try-except block to handle ZeroDivisionError.\n", "* Return the result of the division if no error occurs.\n", "* Return None if division by zero occurs.\n", "* take user input for numerator and denominator values.\n", "* call and print your function with the user inputs.\n", "**Note:- Test with different inputs to validate error handling.**\n", "```\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "68c7dd8d-5140-46e3-8fe0-82c00f58bd06", "metadata": {}, "source": ["\n", "<details><summary>Click here for the solution</summary>\n", "\n", "```python\n", "\n", "def safe_divide(numerator,denominator):\n", "    try:\n", "        result = numerator / denominator\n", "        return result\n", "    except ZeroDivisionError:\n", "        print(\"Error: Cannot divide by zero.\")\n", "        return None\n", "# Test case\n", "numerator=int(input(\"Enter the numerator value:-\"))\n", "denominator=int(input(\"Enter the denominator value:-\"))\n", "print(safe_divide(numerator,denominator)) \n", "```\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "e5db3bb0-fce5-491e-8425-5e7f80f73f2f", "metadata": {}, "source": ["## Exercise 2: Handling ValueError\n"]}, {"cell_type": "markdown", "id": "ef3ec7de-ce90-485c-adba-f60e55c55d37", "metadata": {}, "source": ["Imagine you have a number and want to calculate its square root. To do this, you need to create a Python function. You give this function one number, `'number1'`.\n", "\n", "The function should generate the square root value if you provide a positive integer or float value as input. However, the function should be clever enough to detect the mistake if you enter a negative value. It should kindly inform you with a message saying, `'Invalid input! Please enter a positive integer or a float value.`\n"]}, {"cell_type": "code", "execution_count": 26, "id": "0e45c2be-ab6e-4ac3-b5e2-6b329bca55e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Square root: 2.0\n"]}], "source": ["import math\n", "def safe_sqrt(number1):\n", "    if number1 < 0:\n", "        print (\"Invalid input! Please enter a positive integer or a float value\")\n", "        return None \n", "    return math.sqrt(number1)\n", "number1 = 4\n", "result = safe_sqrt(number1)\n", "if result is not None:\n", "    print (\"Square root:\" , result)\n"]}, {"cell_type": "markdown", "id": "5a48f7c4-fb63-493d-8947-1773d9c52027", "metadata": {}, "source": ["**Note:- Practice handling exceptions by trying different input types like using integers, strings, zero, negative values, or other data types.**\n"]}, {"cell_type": "markdown", "id": "ff4d51da-54c4-4470-84b4-f177dda3d2cb", "metadata": {}, "source": ["<details><summary>Click here for the hints</summary>\n", "\n", "```python\n", "Follow these:-\n", "* Define a function to perform square root of the argument. \n", "* Use try-except block for error handling.\n", "* Use `sqrt()` function from the `math` package to calculate the square root. Catch ValueError and display the error message.\"\n", "* Take user input of the value, number1. \n", "* Test with negative numbers to validate error handling.\n", "```\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "961c4d1d-5d6a-4b01-b047-d97b338511b4", "metadata": {}, "source": ["<details><summary>Click here for the solution</summary>\n", "\n", "```python\n", "import math\n", "\n", "def perform_calculation(number1):\n", "    try:\n", "        result = math.sqrt(number1)\n", "        print(f\"Result: {result}\")\n", "    except ValueError:\n", "        print(\"Error: Invalid input! Please enter a positive integer or a float value.\")\n", "# Test case\n", "number1=float(input(\"Enter the number:-\"))\n", "perform_calculation(number1)\n", "```\n", "\n", "**Note:- Test with different inputs to validate error handling.**\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "35f62284-f97c-414f-993d-4b21abf9f144", "metadata": {}, "source": ["## Exercise 3: Handling Generic Exceptions\n"]}, {"cell_type": "markdown", "id": "4ae7d568-f137-4d21-a585-1e7a2f7c4e09", "metadata": {}, "source": ["Imagine you have a number and want to perform a complex mathematical task. The calculation requires dividing the value of the input argument `\"num\"` by the difference between `\"num\"` and 5, and the result has to be stored in a variable called `\"result\"`.\n", "\n", "You have to define a function so that it can perform that complex mathematical task. The function should handle any potential errors that occur during the calculation. To do this, you can use a try-except block. If any exception arises during the calculation, it should catch the error using the generic exception class `\"Exception\" as \"e\"`. When an exception occurs, the function should display `\"An error occurred during calculation.`\n"]}, {"cell_type": "code", "execution_count": 34, "id": "8581832c-b7a6-40aa-8e27-4e77608a49a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An error occurred during calculation\n"]}], "source": ["def complex_task(num):\n", "    try:\n", "        result = num / (num - 5)\n", "        return result\n", "    except  Exception as e :\n", "        print(\"An error occurred during calculation\")\n", "        return None\n", "num = 5\n", "result = complex_task(num)\n", "if result is not None:\n", "        print(\"Result:\" , result)\n", "\n"]}, {"cell_type": "markdown", "id": "039fce83-d7d3-40e6-a043-288fe0ddd76e", "metadata": {}, "source": ["**Note:- Practice handling exceptions by trying different input types like using integers, strings, zero, negative values, or other data types.**\n"]}, {"cell_type": "markdown", "id": "cbaf79b6-c554-4c1c-8c94-e5bd2491abe1", "metadata": {}, "source": ["<details><summary>Click here for the hints</summary>\n", "\n", "```python\n", "Follow these:-\n", "* Define a function for the complex calculation and pass any argument.\n", "* Use a try-except block for error handling.\n", "* Perform the calculation and store the result in \"result.\"\n", "* Catch any exceptions using Exception as e.\n", "* Display \"An error occurred during calculation.\" when an exception is caught.\n", "* take user input\n", "* Call the defined function with the user input\n", "**Note:- Test with different inputs to validate error handling.**\n", "```\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "45c918a4-56a4-4743-aa10-890ab641ecc6", "metadata": {}, "source": ["<details><summary>Click here for the solution</summary>\n", "\n", "```python\n", "def complex_calculation(num):\n", "    try:\n", "        result = num / (num - 5)\n", "        print (f\"Result: {result}\")\n", "    except Exception as e:\n", "        print(\"An error occurred during calculation.\")\n", "# Test case\n", "user_input = float(input(\"Enter a number: \"))\n", "complex_calculation(user_input)\n", "```\n", "\n", "</details>\n"]}, {"cell_type": "markdown", "id": "444fc714-ada3-46c1-920a-7b6791e0d9f0", "metadata": {}, "source": ["## Authors\n"]}, {"cell_type": "markdown", "id": "3ac06795-871c-4258-aa58-365f5f540a6d", "metadata": {}, "source": ["<a href=\"https://www.linkedin.com/in/joseph-s-50398b136/\" target=\"_blank\"><PERSON></a>\n"]}, {"cell_type": "markdown", "id": "ed80f2d7-7766-4cf9-8283-90a82438d5fe", "metadata": {}, "source": ["## <h3 align=\"center\"> © IBM Corporation 2023. All rights reserved. <h3/>\n", "\n", "<!-- ## Change Log\n", "\n", "|  Date (YYYY-MM-DD) |  Version | Changed By  |  Change Description |\n", "|---|---|---|---|\n", "| 2023-11-02 | 2.2 | <PERSON><PERSON><PERSON><PERSON><PERSON> | Updated instructions |\n", "| 2023-08-01 | 2.1   | <PERSON><PERSON><PERSON> | Added optional practice section |\n", "| 2020-09-02  | 2.0  | <PERSON><PERSON><PERSON> | Template updates to the file|\n", "|   |   |   |   | --!>\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "dcd380ba-fc1e-41ce-b36b-570319d96c88", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "prev_pub_hash": "abb9917dfd7a8c219ca8f58ab7002967472e0fef9004ead21334c695bee6a161"}, "nbformat": 4, "nbformat_minor": 4}