<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <filter id="logoShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="5" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="5" fill="url(#logoGradient)" filter="url(#logoShadow)"/>
  
  <!-- Grid Pattern -->
  <g opacity="0.1">
    <line x1="10" y1="30" x2="90" y2="30" stroke="white" stroke-width="0.5"/>
    <line x1="10" y1="50" x2="90" y2="50" stroke="white" stroke-width="0.5"/>
    <line x1="10" y1="70" x2="90" y2="70" stroke="white" stroke-width="0.5"/>
    <line x1="30" y1="10" x2="30" y2="90" stroke="white" stroke-width="0.5"/>
    <line x1="50" y1="10" x2="50" y2="90" stroke="white" stroke-width="0.5"/>
    <line x1="70" y1="10" x2="70" y2="90" stroke="white" stroke-width="0.5"/>
  </g>
  
  <!-- Main Icon: Stylized J and F letters -->
  <g transform="translate(25, 25)">
    <!-- J letter -->
    <path d="M0,0 L15,0 L15,35 C15,40 10,45 5,45 L0,45 L0,35 L5,35 C7,35 7,33 7,32 L7,0 Z" fill="white"/>
    
    <!-- F letter -->
    <path d="M25,0 L50,0 L50,8 L33,8 L33,18 L45,18 L45,26 L33,26 L33,45 L25,45 Z" fill="white"/>
    
    <!-- Accent dot -->
    <circle cx="42" cy="40" r="5" fill="url(#accentGradient)"/>
  </g>
  
  <!-- Decorative Elements -->
  <rect x="10" y="10" width="80" height="5" fill="url(#accentGradient)" opacity="0.8"/>
  <rect x="10" y="85" width="80" height="5" fill="url(#accentGradient)" opacity="0.8"/>
  
  <!-- Reflective Highlight -->
  <path d="M15,15 L85,15 L85,25 Q50,35 15,25 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
</svg>
