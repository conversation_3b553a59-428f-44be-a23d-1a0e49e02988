/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #cfecfe;
    color: #333;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #054288, #4A90E2);
    padding: 10px 0;
    position: sticky;
    top: 0;
    z-index: 90;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: background 0.3s ease;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.1);
}

.logo-text {
    display: flex;
    flex-direction: column;
    margin-left: 15px;
}

.logo-text span {
    color: #facc15;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 1px 2px rgba(0, 0, 0, 0.1);
}

.logo-text .est {
    font-size: 12px;
    color: #e0e0e0;
}

.nav {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-links {
    display: flex;
    gap: 25px;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    font-size: 16px;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-links a.active {
    background: linear-gradient(135deg, #facc15, #eab308);
    color: #1f2937;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 50%;
    height: 3px;
    background: #fff;
    border-radius: 2px;
}

.nav-buttons {
    display: flex;
    gap: 10px;
}

.nav-buttons a, .nav-buttons button {
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.nav-buttons .login-btn {
    background: linear-gradient(135deg, #fff, #e0e0e0);
    color: #4A90E2;
}

.nav-buttons .login-btn:hover {
    background: linear-gradient(135deg, #e0e0e0, #d1d5db);
    transform: translateY(-2px);
}

.nav-buttons .register-btn {
    background: linear-gradient(135deg, #facc15, #eab308);
    color: #1f2937;
}

.nav-buttons .register-btn:hover {
    background: linear-gradient(135deg, #eab308, #ca8a04);
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    background: url('https://images.unsplash.com/photo-1516321310766-78f1e79f7696') center/cover no-repeat;
    position: relative;
    color: #fff;
    text-align: center;
    padding: 80px 0;
    min-height: 350px;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.7), rgba(59, 130, 246, 0.7));
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 50px;
    margin-bottom: 25px;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-bar {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 1.2s ease-out;
}

.search-bar input {
    width: 450px;
    padding: 14px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-bar input:focus {
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
    background: #fff;
}

.search-btn {
    background: linear-gradient(135deg, #facc15, #eab308);
    color: #1f2937;
    padding: 14px 28px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: linear-gradient(135deg, #eab308, #ca8a04);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.search-btn i {
    margin-right: 8px;
}

/* Filters */
.filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    animation: fadeInUp 1.4s ease-out;
}

.filters select {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.filters select:hover {
    background: #fff;
    transform: translateY(-2px);
}

.filters select:focus {
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
}

/* Featured Jobs Section */
.featured-jobs {
    padding: 50px 0;
    background: #f5f7fa;
    border-radius: 15px;
    margin: 30px 0 60px 0; /* Tăng margin-bottom để tạo khoảng cách lớn hơn */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
}

.featured-jobs h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 40px;
    color: #4A90E2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.job-list {
    max-height: 600px;
    overflow-y: auto;
    padding: 0 10px;
}

.job-list::-webkit-scrollbar {
    width: 8px;
}

.job-list::-webkit-scrollbar-thumb {
    background: #4A90E2;
    border-radius: 4px;
}

.job-list::-webkit-scrollbar-track {
    background: #e0e0e0;
}

.job-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.job-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #fff, #f9fafb);
}

.job-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #4A90E2;
    transition: width 0.3s ease;
}

.job-item:hover::before {
    width: 8px;
}

.job-item img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-right: 20px;
    border-radius: 8px;
}

.job-info {
    flex: 1;
}

.job-info h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 8px;
}

.job-info .company {
    font-size: 15px;
    color: #666;
    margin-bottom: 8px;
}

.job-info .level {
    font-size: 13px;
    color: #999;
    margin-bottom: 8px;
}

.job-info .skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.job-info .skills span {
    background: #e0e0e0;
    color: #333;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 13px;
    transition: background 0.3s ease;
}

.job-info .skills span:hover {
    background: #4A90E2;
    color: #fff;
}

.job-info .details {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 14px;
    color: #00a550;
}

.job-info .details i {
    color: #00a550;
    margin-right: 6px;
}

.job-info .details .days-left {
    color: #00a550;
}

.job-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.job-actions .salary {
    background: linear-gradient(135deg, #4A90E2, #3b82f6);
    color: #fff;
    padding: 8px 15px;
    border-radius: 15px;
    font-size: 14px;
}

.job-actions .apply-btn {
    background: linear-gradient(135deg, #28a745, #22c55e);
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-actions .apply-btn:hover {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.job-actions .favorite {
    color: #ccc;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.job-actions .favorite:hover {
    color: #ff4d4f;
    transform: scale(1.2);
}

.job-tag {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #ff4d4f;
    color: #fff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 13px;
}

/* Benefits Section */
.benefits {
    padding: 50px 0;
    background: linear-gradient(to bottom, #f9f9f9, #e8ecef);
}

.benefits .container {
    display: flex;
    justify-content: space-between;
    gap: 25px;
}

.benefit-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
    width: 23%;
    text-align: center;
    transition: all 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #fff, #f9fafb);
}

.benefit-card h3 {
    font-size: 22px;
    color: #4A90E2;
    margin-bottom: 12px;
}

.benefit-card p {
    font-size: 15px;
    color: #666;
}

/* Employer Page */
.employer-page {
    padding: 50px 0;
    background: #f5f7fa;
    border-radius: 15px;
    margin: 30px 0;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
}

.employer-page h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 40px;
    color: #4A90E2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.employer-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
}

.tab-btn {
    padding: 12px 25px;
    background: #e0e0e0;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: linear-gradient(135deg, #4A90E2, #3b82f6);
    color: #fff;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #4A90E2, #3b82f6);
    color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tab-content {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease-out;
}

.tab-content h3 {
    color: #4A90E2;
    font-size: 24px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    font-size: 16px;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    background: #fff;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.profile-list {
    margin-top: 25px;
}

.profile-item {
    background: #f9f9f9;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.profile-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profile-item h4 {
    color: #333;
    font-size: 20px;
    margin-bottom: 8px;
}

.profile-item p {
    color: #666;
    font-size: 15px;
    margin-bottom: 8px;
}

.profile-item .modal-btn {
    padding: 10px 20px;
    font-size: 14px;
}

/* Profile Page */
.profile-page {
    padding: 50px 0;
    background: linear-gradient(to bottom, #f5f7fa, #e8ecef);
    border-radius: 15px;
    margin: 30px 0;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.profile-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #4A90E2, #facc15);
}

.profile-page h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 40px;
    color: #4A90E2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    animation: fadeIn 0.5s ease-out;
}

.profile-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 25px;
}

.profile-actions .modal-btn {
    padding: 12px 25px;
    font-size: 16px;
}

.profile-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
}

.profile-card {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, #fff, #f9fafb);
}

.profile-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #facc15;
    transition: width 0.3s ease;
}

.profile-card:hover::before {
    width: 8px;
}

.profile-card h3 {
    font-size: 22px;
    color: #333;
    margin-bottom: 12px;
}

.profile-card p {
    font-size: 15px;
    color: #666;
    margin-bottom: 8px;
}

.profile-card .skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.profile-card .skills span {
    background: #e0e0e0;
    color: #333;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 13px;
    transition: background 0.3s ease;
}

.profile-card .skills span:hover {
    background: #4A90E2;
    color: #fff;
}

.profile-card .actions {
    display: flex;
    gap: 15px;
}

.profile-card .actions button {
    padding: 10px 20px;
    font-size: 14px;
}

.cv-details {
    margin-top: 25px;
}

.cv-details h4 {
    font-size: 20px;
    color: #4A90E2;
    margin-bottom: 12px;
}

.cv-details p {
    font-size: 15px;
    color: #666;
    margin-bottom: 8px;
}

/* Modal Overrides for Profile/CV */
.profile-modal .modal-content {
    max-width: 650px;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #054288, #3b82f6);
    color: #fff;
    padding: 30px 0 20px;
    border-top: 4px solid #2563eb;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://www.transparenttextures.com/patterns/cubes.png');
    opacity: 0.1;
    z-index: 1;
}

.footer .container {
    position: relative;
    z-index: 2;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.logo-icon {
    background: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.logo-icon i {
    color: #054288;
    font-size: 20px;
}

.footer-section h4 {
    font-size: 18px;
    margin-bottom: 12px;
    position: relative;
    display: inline-block;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: #facc15;
    border-radius: 2px;
}

.footer-section p {
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
}

.footer-section a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section a:hover {
    color: #facc15;
    padding-left: 5px;
}

.footer-job-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.footer-stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    flex: 1;
    min-width: 100px;
    transition: all 0.3s ease;
}

.footer-stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.footer-stat-item i {
    font-size: 20px;
    color: #facc15;
    margin-bottom: 6px;
}

.footer-stat-item span {
    display: block;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 3px;
}

.footer-stat-item p {
    font-size: 14px;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.8);
}

.social-links {
    display: flex;
    gap: 12px;
    margin-top: 15px;
}

.social-links a {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: #054288;
    background: #facc15;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-newsletter {
    margin-top: 15px;
}

.footer-newsletter h5 {
    font-size: 16px;
    margin-bottom: 10px;
}

.newsletter-form {
    display: flex;
}

.newsletter-form input {
    flex: 1;
    padding: 10px 12px;
    border: none;
    border-radius: 20px 0 0 20px;
    font-size: 13px;
    outline: none;
}

.newsletter-form button {
    background: #facc15;
    color: #054288;
    border: none;
    padding: 0 15px;
    border-radius: 0 20px 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background: #eab308;
}

.footer-app-download {
    margin-top: 15px;
}

.footer-app-download h5 {
    font-size: 16px;
    margin-bottom: 10px;
}

.app-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.app-button {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.app-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.app-button i {
    font-size: 20px;
    margin-right: 8px;
}

.app-button-text {
    display: flex;
    flex-direction: column;
}

.app-button-text span {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
}

.app-button-text strong {
    font-size: 14px;
}

.footer-bottom {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.footer-bottom p {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-bottom-links {
    display: flex;
    gap: 15px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-bottom-links a:hover {
    color: #facc15;
}

.footer-job-illustration {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 150px;
    opacity: 0.2;
    z-index: 1;
}

/* Modal Styles (From Provided CSS, Adjusted) */
.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    justify-content: center;
    align-items: center;
    overflow: auto;
    padding: 20px;
}

.modal-content {
    background-color: #fff;
    padding: 40px 30px;
    border-radius: 15px;
    width: 100%;
    max-width: 450px;
    position: relative;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    animation: fadeSlideIn 0.4s ease-out;
    border: 1px solid rgba(74, 144, 226, 0.1);
}

@keyframes fadeSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.2s ease;
}

.close:hover {
    color: #ff4d4f;
    transform: rotate(90deg);
}

.modal-content h2 {
    color: #4A90E2;
    text-align: center;
    font-size: 28px;
    margin-bottom: 25px;
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    font-size: 16px;
    outline: none;
    background-color: #f9fafb;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    background-color: #fff;
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #a5b4fc;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.modal-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #4A90E2, #3b82f6);
    color: #fff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(74, 144, 226, 0.3);
}

.modal-btn:hover {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 144, 226, 0.4);
}

.modal-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(74, 144, 226, 0.2);
}

.modal-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: #6b7280;
}

.modal-link a {
    color: #4A90E2;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.modal-link a:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Input Validation Styles */
.form-group input:invalid[required]:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input:valid {
    border-color: #10b981;
}

/* Responsive Modal Adjustments */
@media (max-width: 480px) {
    .modal-content {
        padding: 20px;
        max-width: 90%;
    }

    .modal-content h2 {
        font-size: 24px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 14px;
        padding: 10px 12px;
    }

    .modal-btn {
        padding: 12px;
        font-size: 14px;
    }
}

/* General Responsive Adjustments */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 36px;
    }

    .search-bar input {
        width: 100%;
    }

    .filters {
        flex-direction: column;
        align-items: center;
    }

    .benefits .container {
        flex-direction: column;
        align-items: center;
    }

    .benefit-card {
        width: 100%;
        max-width: 400px;
    }

    .profile-list {
        grid-template-columns: 1fr;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Profile & CV Section */
.profile-cv-wrapper {
    padding: 60px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.profile-cv-header {
    text-align: center;
    margin-bottom: 50px;
}

.profile-cv-title {
    font-size: 36px;
    color: #4A90E2;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.profile-cv-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #4A90E2, #006241);
    border-radius: 2px;
}

.profile-cv-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.profile-cv-card {
    background: #ffffff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
}

.profile-cv-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #006241 0%, #00513a 100%);
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h1v1H0V0zm3 3h1v1H3V3zm3 3h1v1H6V6zm3 3h1v1H9V9zm3 3h1v1h-1v-1zm3 3h1v1h-1v-1z" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.1;
}

.card-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.profile-cv-card:hover .card-icon {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
}

.card-icon i {
    font-size: 28px;
    color: #ffffff;
}

.card-title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.card-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    line-height: 1.6;
}

.card-content {
    padding: 30px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0 0 25px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    color: #4a5568;
}

.feature-item i {
    color: #006241;
    margin-right: 12px;
    font-size: 16px;
}

.card-button {
    background: linear-gradient(135deg, #006241 0%, #00513a 100%);
    color: #ffffff;
    width: 100%;
    padding: 14px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.card-button:hover {
    background: linear-gradient(135deg, #00513a 0%, #004130 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 98, 65, 0.3);
}

.search-section {
    max-width: 1200px;
    margin: 40px auto 0;
    padding: 30px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.search-box {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
}

.search-input-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 18px;
}

.search-input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #006241;
    box-shadow: 0 0 0 3px rgba(0, 98, 65, 0.1);
}

.search-button {
    background: #006241;
    color: #ffffff;
    padding: 0 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-button:hover {
    background: #00513a;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 98, 65, 0.2);
}

.filter-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-button {
    flex: 1;
    min-width: 180px;
    padding: 12px 20px;
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.filter-button:hover {
    border-color: #006241;
    color: #006241;
    background: #f0f9f6;
}

.filter-button i:first-child {
    margin-right: 8px;
}

.filter-button i:last-child {
    font-size: 12px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .profile-cv-grid {
        grid-template-columns: 1fr;
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .profile-cv-title {
        font-size: 30px;
    }

    .search-box {
        flex-direction: column;
    }

    .search-button {
        width: 100%;
        padding: 15px;
    }

    .filter-button {
        min-width: 100%;
    }
}

@media (max-width: 480px) {
    .profile-cv-title {
        font-size: 24px;
    }

    .card-header {
        padding: 20px;
    }

    .card-content {
        padding: 20px;
    }

    .card-icon {
        width: 60px;
        height: 60px;
    }

    .card-icon i {
        font-size: 24px;
    }
}