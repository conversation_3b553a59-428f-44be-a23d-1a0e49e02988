const express = require('express');
const router = express.Router();
const Job = require('../models/Job');
const { auth, employerAuth } = require('../middleware/auth');

// @route   GET /api/jobs
// @desc    Get all jobs with pagination
// @access  Public
router.get('/', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    const isFeatured = req.query.featured === 'true' ? true : (req.query.featured === 'false' ? false : null);
    
    const jobs = await Job.getAll(limit, offset, isFeatured);
    
    res.status(200).json({ jobs });
  } catch (error) {
    console.error('Get jobs error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh sách công việc' });
  }
});

// @route   GET /api/jobs/:id
// @desc    Get job by ID
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    
    const job = await Job.getById(jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Không tìm thấy công việc' });
    }
    
    res.status(200).json({ job });
  } catch (error) {
    console.error('Get job error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy thông tin công việc' });
  }
});

// @route   GET /api/jobs/search
// @desc    Search jobs
// @access  Public
router.get('/search', async (req, res) => {
  try {
    const { keyword, location, jobType, categoryId } = req.query;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    
    const jobs = await Job.search({
      keyword,
      location,
      jobType,
      categoryId: categoryId ? parseInt(categoryId) : null,
      limit,
      offset
    });
    
    res.status(200).json({ jobs });
  } catch (error) {
    console.error('Search jobs error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi tìm kiếm công việc' });
  }
});

// @route   POST /api/jobs/apply/:id
// @desc    Apply for a job
// @access  Private
router.post('/apply/:id', auth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    const userId = req.user.id;
    const { coverLetter, cvPath } = req.body;
    
    const result = await Job.apply(jobId, userId, { coverLetter, cvPath });
    
    res.status(201).json(result);
  } catch (error) {
    console.error('Apply job error:', error);
    res.status(400).json({ error: error.message || 'Lỗi khi ứng tuyển công việc' });
  }
});

// @route   POST /api/jobs
// @desc    Create a new job
// @access  Private (Employer only)
router.post('/', employerAuth, async (req, res) => {
  try {
    const {
      title,
      companyId,
      description,
      requirements,
      benefits,
      location,
      salaryMin,
      salaryMax,
      isNegotiable,
      jobType,
      experienceLevel,
      skills,
      isFeatured,
      deadline,
      categories
    } = req.body;
    
    // Validate input
    if (!title || !companyId || !description) {
      return res.status(400).json({ error: 'Vui lòng điền đầy đủ thông tin bắt buộc' });
    }
    
    const job = await Job.create({
      title,
      companyId,
      description,
      requirements,
      benefits,
      location,
      salaryMin,
      salaryMax,
      isNegotiable,
      jobType,
      experienceLevel,
      skills,
      isFeatured,
      deadline,
      categories
    });
    
    res.status(201).json({ job });
  } catch (error) {
    console.error('Create job error:', error);
    res.status(400).json({ error: error.message || 'Lỗi khi tạo công việc mới' });
  }
});

// @route   PUT /api/jobs/:id
// @desc    Update a job
// @access  Private (Employer only)
router.put('/:id', employerAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    const {
      title,
      description,
      requirements,
      benefits,
      location,
      salaryMin,
      salaryMax,
      isNegotiable,
      jobType,
      experienceLevel,
      skills,
      isFeatured,
      deadline,
      categories
    } = req.body;
    
    // Validate input
    if (!title || !description) {
      return res.status(400).json({ error: 'Vui lòng điền đầy đủ thông tin bắt buộc' });
    }
    
    const job = await Job.update(jobId, {
      title,
      description,
      requirements,
      benefits,
      location,
      salaryMin,
      salaryMax,
      isNegotiable,
      jobType,
      experienceLevel,
      skills,
      isFeatured,
      deadline,
      categories
    });
    
    res.status(200).json({ job });
  } catch (error) {
    console.error('Update job error:', error);
    res.status(400).json({ error: error.message || 'Lỗi khi cập nhật công việc' });
  }
});

// @route   DELETE /api/jobs/:id
// @desc    Delete a job
// @access  Private (Employer only)
router.delete('/:id', employerAuth, async (req, res) => {
  try {
    const jobId = parseInt(req.params.id);
    
    await Job.delete(jobId);
    
    res.status(200).json({ message: 'Xóa công việc thành công' });
  } catch (error) {
    console.error('Delete job error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi xóa công việc' });
  }
});

// @route   GET /api/jobs/categories
// @desc    Get job categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = await Job.getCategories();
    
    res.status(200).json({ categories });
  } catch (error) {
    console.error('Get job categories error:', error);
    res.status(500).json({ error: error.message || 'Lỗi khi lấy danh mục công việc' });
  }
});

module.exports = router;
