const mongoose = require('mongoose');

const cvTemplateCategorySchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        required: true
    },
    icon: {
        type: String
    },
    slug: {
        type: String,
        unique: true,
        trim: true
    },
    order: {
        type: Number,
        default: 0
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Cập nhật thời gian khi cập nhật danh mục
cvTemplateCategorySchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Tìm kiếm danh mục theo tên
cvTemplateCategorySchema.statics.findByName = async function(name) {
    try {
        const regex = new RegExp(name, 'i');
        return await this.find({ name: regex });
    } catch (error) {
        console.error('Lỗi khi tìm danh mục theo tên:', error);
        throw error;
    }
};

// Lấy tất cả danh mục đang hoạt động
cvTemplateCategorySchema.statics.getAllActive = async function() {
    try {
        return await this.find({ isActive: true }).sort({ order: 1, name: 1 });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách danh mục đang hoạt động:', error);
        throw error;
    }
};

module.exports = mongoose.model('CVTemplateCategory', cvTemplateCategorySchema, 'cv_template_categories');
