/* Hero Section */
.hero {
    background-color: #3b82f6; /* <PERSON><PERSON><PERSON> x<PERSON>h dương đồng nhất */
    position: relative;
    color: #fff;
    text-align: center;
    padding: 60px 0 80px;
    min-height: 400px;
}

.hero .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.hero p {
    font-size: 18px;
    margin-bottom: 32px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}

/* Search Form */
.search-form {
    max-width: 900px;
    margin: 0 auto;
}

.search-bar {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar input {
    flex: 1;
    max-width: 600px;
    padding: 14px 20px;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-bar input:focus {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-btn {
    background-color: #facc15;
    color: #1f2937;
    padding: 14px 28px;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.search-btn:hover {
    background-color: #eab308;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.search-btn i {
    margin-right: 8px;
}

/* Filters */
.filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.filter-item {
    flex: 1;
    max-width: 200px;
}

.filters select {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 15px;
    background-color: #fff;
    color: #333;
    cursor: pointer;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.filters select:hover {
    background-color: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.filters select:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Stats Section */
.stats {
    background-color: #fff;
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.stats-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.stat-item {
    flex: 1;
    min-width: 200px;
}

.stat-number {
    font-size: 48px;
    font-weight: bold;
    color: #4A90E2;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 18px;
    color: #666;
}

/* Featured Jobs Section */
.featured-jobs-home {
    padding: 60px 0;
    background-color: #f5f7fa;
}

.section-title {
    text-align: center;
    font-size: 36px;
    margin-bottom: 40px;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background-color: #4A90E2;
    margin: 15px auto 0;
    border-radius: 2px;
}

.featured-jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.job-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.job-card-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.job-card-logo {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-right: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
    padding: 5px;
}

.job-card-company {
    flex: 1;
}

.job-card-company h3 {
    font-size: 18px;
    margin-bottom: 5px;
    color: #333;
}

.job-card-company p {
    font-size: 14px;
    color: #666;
}

.job-card-body {
    padding: 20px;
}

.job-card-details {
    margin-bottom: 15px;
}

.job-card-details p {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.job-card-details i {
    width: 20px;
    margin-right: 10px;
    color: #4A90E2;
}

.job-card-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.job-card-skills span {
    background-color: #f0f7ff;
    color: #4A90E2;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.job-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f9f9f9;
}

.job-card-salary {
    font-weight: bold;
    color: #28a745;
}

.job-card-apply {
    background-color: #4A90E2;
    color: #fff;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.job-card-apply:hover {
    background-color: #3A78C2;
}

.view-all-btn {
    display: block;
    width: 200px;
    margin: 0 auto;
    padding: 12px 0;
    background-color: transparent;
    color: #4A90E2;
    border: 2px solid #4A90E2;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background-color: #4A90E2;
    color: #fff;
    transform: translateY(-2px);
}

/* Categories Section */
.categories {
    padding: 60px 0;
    background-color: #fff;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-card:hover {
    background-color: #4A90E2;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.category-card:hover i,
.category-card:hover h3,
.category-card:hover p {
    color: #fff;
}

.category-card i {
    font-size: 40px;
    color: #4A90E2;
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.category-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    transition: color 0.3s ease;
}

.category-card p {
    font-size: 14px;
    color: #666;
    transition: color 0.3s ease;
}

/* Benefits Section */
.benefits {
    padding: 60px 0;
    background: linear-gradient(to right, #f0f7ff, #e6f2ff);
}

.benefits-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
}

.benefit-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    width: calc(25% - 30px);
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-card i {
    font-size: 40px;
    color: #4A90E2;
    margin-bottom: 20px;
}

.benefit-card h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
}

.benefit-card p {
    font-size: 14px;
    color: #666;
}

/* Testimonials Section */
.testimonials {
    padding: 60px 0;
    background-color: #f5f7fa;
}

.testimonials-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.testimonial-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    width: calc(33.333% - 30px);
    position: relative;
}

.testimonial-card::before {
    content: '\201C';
    font-size: 80px;
    color: #f0f0f0;
    position: absolute;
    top: 20px;
    left: 20px;
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-content {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
}

.testimonial-author-info h4 {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.testimonial-author-info p {
    font-size: 14px;
    color: #666;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #4A90E2, #3A78C2);
    color: #fff;
    text-align: center;
}

.cta h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #fff;
}

.cta p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.cta-btn {
    padding: 15px 30px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.cta-btn-primary {
    background-color: #facc15;
    color: #1f2937;
}

.cta-btn-primary:hover {
    background-color: #eab308;
    transform: translateY(-2px);
}

.cta-btn-secondary {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.cta-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .benefit-card {
        width: calc(50% - 30px);
    }

    .testimonial-card {
        width: calc(50% - 30px);
    }

    .filter-item {
        max-width: 180px;
    }
}

@media (max-width: 992px) {
    .hero h1 {
        font-size: 38px;
    }

    .hero p {
        font-size: 17px;
    }

    .filter-item {
        max-width: 160px;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 50px 0 70px;
    }

    .hero h1 {
        font-size: 32px;
    }

    .hero p {
        font-size: 16px;
        margin-bottom: 25px;
    }

    .search-bar {
        flex-direction: column;
    }

    .search-bar input {
        max-width: 100%;
        width: 100%;
    }

    .search-btn {
        width: 100%;
        margin-top: 10px;
    }

    .filters {
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-item {
        flex: 0 0 calc(50% - 5px);
        max-width: calc(50% - 5px);
    }

    .featured-jobs-grid {
        grid-template-columns: 1fr;
    }

    .benefit-card {
        width: 100%;
    }

    .testimonial-card {
        width: 100%;
    }

    .cta-buttons {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 40px 0 60px;
    }

    .hero h1 {
        font-size: 28px;
    }

    .hero p {
        font-size: 15px;
        margin-bottom: 20px;
    }

    .filter-item {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .stat-item {
        width: 100%;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .search-btn {
        padding: 12px 20px;
        font-size: 15px;
    }

    .search-bar input {
        padding: 12px 15px;
        font-size: 15px;
    }

    .filters select {
        padding: 10px 15px;
        font-size: 14px;
    }
}
