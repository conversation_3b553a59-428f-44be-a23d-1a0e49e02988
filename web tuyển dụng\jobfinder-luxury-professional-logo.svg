<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#064e3b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <clipPath id="roundedRect">
      <rect x="10" y="10" width="80" height="80" rx="15" />
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect x="10" y="10" width="80" height="80" rx="15" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Decorative Pattern -->
  <g opacity="0.1">
    <circle cx="20" cy="20" r="2" fill="white"/>
    <circle cx="30" cy="20" r="2" fill="white"/>
    <circle cx="40" cy="20" r="2" fill="white"/>
    <circle cx="50" cy="20" r="2" fill="white"/>
    <circle cx="60" cy="20" r="2" fill="white"/>
    <circle cx="70" cy="20" r="2" fill="white"/>
    <circle cx="80" cy="20" r="2" fill="white"/>
    
    <circle cx="20" cy="30" r="2" fill="white"/>
    <circle cx="30" cy="30" r="2" fill="white"/>
    <circle cx="40" cy="30" r="2" fill="white"/>
    <circle cx="50" cy="30" r="2" fill="white"/>
    <circle cx="60" cy="30" r="2" fill="white"/>
    <circle cx="70" cy="30" r="2" fill="white"/>
    <circle cx="80" cy="30" r="2" fill="white"/>
    
    <circle cx="20" cy="40" r="2" fill="white"/>
    <circle cx="30" cy="40" r="2" fill="white"/>
    <circle cx="40" cy="40" r="2" fill="white"/>
    <circle cx="50" cy="40" r="2" fill="white"/>
    <circle cx="60" cy="40" r="2" fill="white"/>
    <circle cx="70" cy="40" r="2" fill="white"/>
    <circle cx="80" cy="40" r="2" fill="white"/>
    
    <circle cx="20" cy="50" r="2" fill="white"/>
    <circle cx="30" cy="50" r="2" fill="white"/>
    <circle cx="40" cy="50" r="2" fill="white"/>
    <circle cx="50" cy="50" r="2" fill="white"/>
    <circle cx="60" cy="50" r="2" fill="white"/>
    <circle cx="70" cy="50" r="2" fill="white"/>
    <circle cx="80" cy="50" r="2" fill="white"/>
    
    <circle cx="20" cy="60" r="2" fill="white"/>
    <circle cx="30" cy="60" r="2" fill="white"/>
    <circle cx="40" cy="60" r="2" fill="white"/>
    <circle cx="50" cy="60" r="2" fill="white"/>
    <circle cx="60" cy="60" r="2" fill="white"/>
    <circle cx="70" cy="60" r="2" fill="white"/>
    <circle cx="80" cy="60" r="2" fill="white"/>
    
    <circle cx="20" cy="70" r="2" fill="white"/>
    <circle cx="30" cy="70" r="2" fill="white"/>
    <circle cx="40" cy="70" r="2" fill="white"/>
    <circle cx="50" cy="70" r="2" fill="white"/>
    <circle cx="60" cy="70" r="2" fill="white"/>
    <circle cx="70" cy="70" r="2" fill="white"/>
    <circle cx="80" cy="70" r="2" fill="white"/>
    
    <circle cx="20" cy="80" r="2" fill="white"/>
    <circle cx="30" cy="80" r="2" fill="white"/>
    <circle cx="40" cy="80" r="2" fill="white"/>
    <circle cx="50" cy="80" r="2" fill="white"/>
    <circle cx="60" cy="80" r="2" fill="white"/>
    <circle cx="70" cy="80" r="2" fill="white"/>
    <circle cx="80" cy="80" r="2" fill="white"/>
  </g>
  
  <!-- Main Icon: Magnifying Glass with Briefcase -->
  <g transform="translate(25, 30)">
    <!-- Magnifying Glass -->
    <circle cx="15" cy="15" r="13" fill="none" stroke="white" stroke-width="3"/>
    <line x1="24" y1="24" x2="32" y2="32" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Glass Interior -->
    <circle cx="15" cy="15" r="10" fill="url(#accentGradient)" opacity="0.15"/>
    
    <!-- Briefcase -->
    <g transform="translate(35, 5)">
      <rect x="0" y="5" width="20" height="15" rx="3" fill="white"/>
      <rect x="5" y="0" width="10" height="5" rx="1" fill="white"/>
      <rect x="9" y="0" width="2" height="20" fill="url(#goldGradient)" opacity="0.8"/>
      <rect x="3" y="10" width="14" height="2" rx="1" fill="url(#bgGradient)" opacity="0.7"/>
      <rect x="3" y="14" width="14" height="2" rx="1" fill="url(#bgGradient)" opacity="0.7"/>
    </g>
    
    <!-- Glass Reflection -->
    <path d="M10,10 Q15,18 20,10" stroke="white" stroke-width="1.5" fill="none" opacity="0.8"/>
  </g>
  
  <!-- Border Accent -->
  <rect x="15" y="15" width="70" height="70" rx="10" fill="none" stroke="url(#accentGradient)" stroke-width="1" opacity="0.5"/>
  
  <!-- Reflective Highlight -->
  <path d="M10,10 L90,10 L90,30 Q50,40 10,30 Z" fill="white" opacity="0.1" clip-path="url(#roundedRect)"/>
</svg>
