<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tìm kiếm việc làm - JobFinder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/header-footer.css">
    <link rel="stylesheet" href="css/advanced-search.css">
</head>
<body>
    <!-- Header -->
    <div id="header-container"></div>

    <!-- Advanced Search Page -->
    <div class="search-container">
        <div class="search-header">
            <h1>Tìm kiếm việc làm</h1>
            <p>Khám phá hàng ngàn c<PERSON> hội việc làm phù hợp với kỹ năng và mong muốn của bạn</p>
        </div>

        <div class="search-form">
            <div class="search-bar">
                <div class="search-input-container">
                    <i class="fas fa-search"></i>
                    <input type="text" class="search-input" id="search-input" placeholder="Nhập từ khóa, vị trí, công ty...">
                </div>
                <button type="submit" class="search-btn" id="search-btn">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
            </div>

            <div class="basic-filters">
                <div class="filter-select">
                    <select id="location-filter">
                        <option value="">Địa điểm</option>
                        <option value="Hà Nội">Hà Nội</option>
                        <option value="Hồ Chí Minh">Hồ Chí Minh</option>
                        <option value="Đà Nẵng">Đà Nẵng</option>
                        <option value="Hải Phòng">Hải Phòng</option>
                        <option value="Cần Thơ">Cần Thơ</option>
                    </select>
                </div>

                <div class="filter-select">
                    <select id="salary-filter">
                        <option value="">Mức lương</option>
                        <option value="0-5000000">Dưới 5 triệu</option>
                        <option value="5000000-10000000">5 - 10 triệu</option>
                        <option value="10000000-20000000">10 - 20 triệu</option>
                        <option value="20000000-30000000">20 - 30 triệu</option>
                        <option value="30000000">Trên 30 triệu</option>
                    </select>
                </div>

                <div class="filter-select">
                    <select id="experience-filter">
                        <option value="">Kinh nghiệm</option>
                        <option value="0">Chưa có kinh nghiệm</option>
                        <option value="1">1 năm</option>
                        <option value="2">2 năm</option>
                        <option value="3-5">3 - 5 năm</option>
                        <option value="5">Trên 5 năm</option>
                    </select>
                </div>

                <div class="filter-select">
                    <select id="job-type-filter">
                        <option value="">Loại hình</option>
                        <option value="Toàn thời gian">Toàn thời gian</option>
                        <option value="Bán thời gian">Bán thời gian</option>
                        <option value="Thực tập">Thực tập</option>
                        <option value="Freelance">Freelance</option>
                        <option value="Remote">Remote</option>
                    </select>
                </div>

                <button id="reset-filter" class="reset-btn">Đặt lại</button>
            </div>
        </div>

        <div class="advanced-filters">
            <div class="advanced-filters-header">
                <h2>Bộ lọc nâng cao</h2>
            </div>

            <div class="filter-groups">
                <div class="filter-group">
                    <h3 class="filter-group-title">Ngành nghề</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="category-it" value="IT">
                            <span class="custom-checkbox"></span>
                            <label for="category-it">Công nghệ thông tin</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="category-finance" value="Finance">
                            <span class="custom-checkbox"></span>
                            <label for="category-finance">Tài chính - Ngân hàng</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="category-marketing" value="Marketing">
                            <span class="custom-checkbox"></span>
                            <label for="category-marketing">Marketing</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="category-sales" value="Sales">
                            <span class="custom-checkbox"></span>
                            <label for="category-sales">Bán hàng</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="category-engineering" value="Engineering">
                            <span class="custom-checkbox"></span>
                            <label for="category-engineering">Kỹ thuật</label>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <h3 class="filter-group-title">Cấp bậc</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="level-intern" value="Intern">
                            <span class="custom-checkbox"></span>
                            <label for="level-intern">Thực tập sinh</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="level-fresher" value="Fresher">
                            <span class="custom-checkbox"></span>
                            <label for="level-fresher">Mới tốt nghiệp</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="level-junior" value="Junior">
                            <span class="custom-checkbox"></span>
                            <label for="level-junior">Nhân viên</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="level-senior" value="Senior">
                            <span class="custom-checkbox"></span>
                            <label for="level-senior">Trưởng nhóm / Quản lý</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="level-manager" value="Manager">
                            <span class="custom-checkbox"></span>
                            <label for="level-manager">Giám đốc</label>
                        </div>
                    </div>
                </div>

                <div class="filter-group">
                    <h3 class="filter-group-title">Kỹ năng</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="skill-javascript" value="JavaScript">
                            <span class="custom-checkbox"></span>
                            <label for="skill-javascript">JavaScript</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="skill-java" value="Java">
                            <span class="custom-checkbox"></span>
                            <label for="skill-java">Java</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="skill-python" value="Python">
                            <span class="custom-checkbox"></span>
                            <label for="skill-python">Python</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="skill-react" value="React">
                            <span class="custom-checkbox"></span>
                            <label for="skill-react">React</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="skill-nodejs" value="Node.js">
                            <span class="custom-checkbox"></span>
                            <label for="skill-nodejs">Node.js</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="filter-actions">
                <button class="clear-btn" id="clear-advanced-filters">Xóa bộ lọc</button>
                <button class="apply-btn" id="apply-advanced-filters">Áp dụng</button>
            </div>
        </div>

        <div class="job-results">
            <div class="job-list" id="job-list">
                <!-- Jobs will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Modals -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/jobs.js"></script>
    <script>
        // JavaScript for custom checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize custom checkboxes
            const checkboxes = document.querySelectorAll('.checkbox-item input[type="checkbox"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // Additional functionality can be added here if needed
                });
            });
            
            // Reset filters button
            const resetBtn = document.getElementById('reset-filter');
            resetBtn.addEventListener('click', function() {
                document.getElementById('search-input').value = '';
                document.getElementById('location-filter').selectedIndex = 0;
                document.getElementById('salary-filter').selectedIndex = 0;
                document.getElementById('experience-filter').selectedIndex = 0;
                document.getElementById('job-type-filter').selectedIndex = 0;
            });
            
            // Clear advanced filters button
            const clearAdvancedBtn = document.getElementById('clear-advanced-filters');
            clearAdvancedBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            });
            
            // Apply advanced filters button
            const applyAdvancedBtn = document.getElementById('apply-advanced-filters');
            applyAdvancedBtn.addEventListener('click', function() {
                // Implementation for applying filters would go here
                // This would typically involve filtering the job list based on selected criteria
                console.log('Advanced filters applied');
            });
        });
    </script>
</body>
</html>
