const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const auth = (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Không có token xác thực' });
    }
    
    const token = authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'Không có token xác thực' });
    }
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Add user from payload to request
    req.user = decoded;
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({ error: 'Token không hợp lệ' });
  }
};

// Middleware to check if user is admin
const adminAuth = (req, res, next) => {
  auth(req, res, () => {
    if (req.user && req.user.role === 'admin') {
      next();
    } else {
      return res.status(403).json({ error: 'Không có quyền truy cập' });
    }
  });
};

// Middleware to check if user is employer
const employerAuth = (req, res, next) => {
  auth(req, res, () => {
    if (req.user && (req.user.role === 'employer' || req.user.role === 'admin')) {
      next();
    } else {
      return res.status(403).json({ error: 'Không có quyền truy cập' });
    }
  });
};

module.exports = {
  auth,
  adminAuth,
  employerAuth
};
