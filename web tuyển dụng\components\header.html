<!-- <PERSON><PERSON> Blue Header -->
<header class="horizontal-header">
    <div class="header-container">
        <div class="logo-container">
            <img src="images/jobfinder-logo.svg" alt="JobFinder Logo">
            <div class="logo-text">JobFinder</div>
        </div>

        <nav class="nav-menu" id="navMenu">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="index.html" class="nav-link" id="nav-home">Trang chủ</a>
                </li>
                <li class="nav-item">
                    <a href="job-search.html" class="nav-link" id="nav-jobs">Việc làm</a>
                </li>
                <li class="nav-item">
                    <a href="profile.html" class="nav-link" id="nav-profile">Hồ sơ & CV</a>
                </li>
                <li class="nav-item">
                    <a href="employer.html" class="nav-link" id="nav-employer">Nh<PERSON> tuyển dụng</a>
                </li>
                <li class="nav-item">
                    <a href="login.html" class="nav-link" id="nav-login">Đăng nhập</a>
                </li>
                <li class="nav-item">
                    <a href="register.html" class="nav-link" id="nav-register">Đăng ký</a>
                </li>
                <li class="nav-item" id="account-dropdown">
                    <a href="#" class="nav-link" id="nav-account">Tài khoản <i class="fas fa-chevron-down"></i></a>
                </li>
            </ul>
        </nav>
    </div>
</header>

<script>
    // Set active nav link based on current page
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname;

        if (currentPage.includes('index.html') || currentPage === '/' || currentPage.endsWith('/')) {
            document.getElementById('nav-home').classList.add('active');
        } else if (currentPage.includes('job-search.html')) {
            document.getElementById('nav-jobs').classList.add('active');
        } else if (currentPage.includes('profile.html')) {
            document.getElementById('nav-profile').classList.add('active');
        } else if (currentPage.includes('employer.html')) {
            document.getElementById('nav-employer').classList.add('active');
        } else if (currentPage.includes('login.html')) {
            document.getElementById('nav-login').classList.add('active');
        } else if (currentPage.includes('register.html')) {
            document.getElementById('nav-register').classList.add('active');
        }

        // Check if user is logged in
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        const navLogin = document.getElementById('nav-login');
        const navRegister = document.getElementById('nav-register');
        const accountDropdown = document.getElementById('account-dropdown');

        if (isLoggedIn) {
            navLogin.parentElement.style.display = 'none';
            navRegister.parentElement.style.display = 'none';
            accountDropdown.style.display = 'block';
        } else {
            navLogin.parentElement.style.display = 'block';
            navRegister.parentElement.style.display = 'block';
            accountDropdown.style.display = 'none';
        }
    });
</script>
