// Authentication JavaScript file

// API URL
const API_URL = 'http://localhost:5000/api';

// DOM Elements
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const logoutBtn = document.getElementById('logout-btn');
const authLinks = document.querySelectorAll('.auth-link');
const userLinks = document.querySelectorAll('.user-link');
const userNameDisplay = document.getElementById('user-name');

// Check if user is logged in
function checkAuth() {
    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user'));
    
    if (token && user) {
        // Show user-specific elements
        authLinks.forEach(link => link.style.display = 'none');
        userLinks.forEach(link => link.style.display = 'block');
        
        // Display user name
        if (userNameDisplay) {
            userNameDisplay.textContent = user.name;
        }
        
        return true;
    } else {
        // Show auth links
        authLinks.forEach(link => link.style.display = 'block');
        userLinks.forEach(link => link.style.display = 'none');
        
        return false;
    }
}

// Register user
async function registerUser(userData) {
    try {
        const response = await fetch(`${API_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Save token and user data
            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));
            
            // Update UI
            checkAuth();
            
            // Close modal
            const modal = document.getElementById('register-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
            
            // Show success message
            alert('Đăng ký thành công!');
            
            return true;
        } else {
            throw new Error(data.message || 'Đăng ký thất bại');
        }
    } catch (error) {
        console.error('Register error:', error);
        alert(error.message);
        return false;
    }
}

// Login user
async function loginUser(credentials) {
    try {
        const response = await fetch(`${API_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(credentials)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Save token and user data
            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));
            
            // Update UI
            checkAuth();
            
            // Close modal
            const modal = document.getElementById('login-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
            
            // Show success message
            alert('Đăng nhập thành công!');
            
            return true;
        } else {
            throw new Error(data.message || 'Đăng nhập thất bại');
        }
    } catch (error) {
        console.error('Login error:', error);
        alert(error.message);
        return false;
    }
}

// Logout user
function logoutUser() {
    // Clear local storage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Update UI
    checkAuth();
    
    // Redirect to home page if not already there
    if (window.location.pathname !== '/' && window.location.pathname !== '/index.html') {
        window.location.href = '/';
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
    // Check authentication status
    checkAuth();
    
    // Register form submission
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const role = document.getElementById('register-role').value;
            
            const userData = {
                name,
                email,
                password,
                role
            };
            
            await registerUser(userData);
        });
    }
    
    // Login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            const credentials = {
                email,
                password
            };
            
            await loginUser(credentials);
        });
    }
    
    // Logout button click
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            logoutUser();
        });
    }
});

// Export functions for use in other scripts
window.authFunctions = {
    checkAuth,
    registerUser,
    loginUser,
    logoutUser
};
