/* Modern Home Page Styles */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --primary-lightest: #dbeafe;

    /* Secondary Colors */
    --secondary-color: #0ea5e9;
    --secondary-dark: #0284c7;
    --secondary-light: #38bdf8;

    /* Accent Colors */
    --accent-color: #8b5cf6;
    --accent-dark: #7c3aed;
    --accent-light: #a78bfa;

    /* Success Colors */
    --success-color: #10b981;
    --success-dark: #059669;
    --success-light: #34d399;

    /* Warning Colors */
    --warning-color: #f59e0b;
    --warning-dark: #d97706;
    --warning-light: #fbbf24;

    /* Neutral Colors */
    --text-dark: #111827;
    --text-medium: #374151;
    --text-light: #6b7280;
    --bg-white: #ffffff;
    --bg-light: #f9fafb;
    --bg-lighter: #f3f4f6;
    --border-color: #e5e7eb;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Hero Section */
.hero {
    position: relative;
    color: var(--bg-white);
    text-align: center;
    padding: 80px 0 100px;
    min-height: 550px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 0.8;
}

.hero .container {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.hero-content {
    margin-bottom: 2.5rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.search-container {
    margin: 0 auto;
    width: 100%;
    max-width: 1100px;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    font-weight: 400;
    opacity: 0.9;
}

.hero-description {
    margin-top: 2.5rem;
    font-size: 1.25rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    font-weight: 400;
    opacity: 0.9;
    text-align: center;
    padding: 0.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: var(--radius-lg);
    display: inline-block;
}

/* Search Form */
.search-form {
    max-width: 900px;
    margin: 0 auto;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-form-large {
    max-width: 1000px;
    padding: 2rem;
    margin: 0 auto;
}

.search-bar {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
}

.search-bar-large {
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.search-bar input {
    flex: 1;
    max-width: 600px;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: var(--radius-full);
    font-size: 1rem;
    outline: none;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    background-color: var(--bg-white);
    color: var(--text-dark);
}

.search-bar-large input {
    max-width: 700px;
    padding: 1.25rem 1.75rem;
    font-size: 1.125rem;
}

.search-bar input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), var(--shadow-md);
}

.search-btn {
    background-color: var(--warning-color);
    color: var(--text-dark);
    padding: 1rem 1.75rem;
    border: none;
    border-radius: var(--radius-full);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.search-bar-large .search-btn {
    padding: 1.25rem 2rem;
    font-size: 1.125rem;
}

.search-btn:hover {
    background-color: var(--warning-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.search-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.search-btn i {
    font-size: 1.125rem;
}

/* Filters */
.filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.filter-item {
    flex: 1;
    max-width: 200px;
}

.search-form-large .filter-item {
    max-width: 220px;
}

.filters select {
    width: 100%;
    padding: 0.875rem 1.25rem;
    border: none;
    border-radius: var(--radius-full);
    font-size: 0.9375rem;
    background-color: var(--bg-white);
    color: var(--text-medium);
    cursor: pointer;
    outline: none;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

.search-form-large .filters select {
    padding: 1rem 1.5rem;
    font-size: 1rem;
}

.filters select:hover {
    background-color: var(--bg-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.filters select:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), var(--shadow-md);
}

/* Stats Section */
.stats {
    background-color: var(--bg-white);
    padding: 5rem 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.stats-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
}

.stat-item {
    flex: 1;
    min-width: 200px;
    padding: 1.5rem;
    background-color: var(--bg-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    line-height: 1;
}

.stat-label {
    font-size: 1.125rem;
    color: var(--text-medium);
    font-weight: 500;
}

/* Featured Jobs Section */
.featured-jobs-home {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.section-title {
    text-align: center;
    font-size: 2.25rem;
    margin-bottom: 3rem;
    color: var(--text-dark);
    position: relative;
    font-weight: 700;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    margin: 1rem auto 0;
    border-radius: var(--radius-full);
}

.featured-jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.job-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-lightest);
}

.job-card-header {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.job-card-logo {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-right: 1rem;
    border-radius: var(--radius-md);
    background-color: var(--bg-light);
    padding: 0.5rem;
    border: 1px solid var(--border-color);
}

.job-card-company {
    flex: 1;
}

.job-card-company h3 {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
    color: var(--text-dark);
    font-weight: 600;
}

.job-card-company p {
    font-size: 0.875rem;
    color: var(--text-light);
}

.job-card-body {
    padding: 1.5rem;
}

.job-card-body h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-weight: 600;
}

.job-card-details {
    margin-bottom: 1rem;
}

.job-card-details p {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.job-card-details i {
    width: 1.25rem;
    margin-right: 0.625rem;
    color: var(--primary-color);
}

.job-card-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
}

.job-card-skills span {
    background-color: var(--primary-lightest);
    color: var(--primary-dark);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.job-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: var(--bg-lighter);
    border-top: 1px solid var(--border-color);
}

.job-card-salary {
    font-weight: 600;
    color: var(--success-color);
    font-size: 1rem;
}

.job-card-apply {
    background-color: var(--primary-color);
    color: var(--bg-white);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-normal);
}

.job-card-apply:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.view-all-btn {
    display: block;
    width: 200px;
    margin: 0 auto;
    padding: 0.875rem 0;
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-full);
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition-normal);
}

.view-all-btn:hover {
    background-color: var(--primary-color);
    color: var(--bg-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Categories Section */
.categories {
    padding: 5rem 0;
    background-color: var(--bg-white);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-lg);
    padding: 2rem 1.5rem;
    text-align: center;
    transition: var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--border-color);
}

.category-card:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: transparent;
}

.category-card:hover i,
.category-card:hover h3,
.category-card:hover p {
    color: var(--bg-white);
}

.category-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: var(--transition-normal);
}

.category-card h3 {
    font-size: 1.125rem;
    margin-bottom: 0.625rem;
    color: var(--text-dark);
    font-weight: 600;
    transition: var(--transition-normal);
}

.category-card p {
    font-size: 0.875rem;
    color: var(--text-light);
    transition: var(--transition-normal);
}

/* Benefits Section */
.benefits {
    padding: 5rem 0;
    background: linear-gradient(to right, var(--primary-lightest), #f0f9ff);
}

.benefits-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-lightest);
}

.benefit-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.25rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.benefit-card h3 {
    font-size: 1.25rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.benefit-card p {
    font-size: 0.9375rem;
    color: var(--text-light);
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.testimonials-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    position: relative;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-lightest);
}

.testimonial-card::before {
    content: '\201C';
    font-size: 5rem;
    color: var(--primary-lightest);
    position: absolute;
    top: 1.25rem;
    left: 1.25rem;
    font-family: Georgia, serif;
    line-height: 1;
    z-index: 0;
}

.testimonial-content {
    font-size: 1rem;
    color: var(--text-medium);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-author img {
    width: 3.125rem;
    height: 3.125rem;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 1rem;
    border: 2px solid var(--primary-lightest);
}

.testimonial-author-info h4 {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.testimonial-author-info p {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* CTA Section */
.cta {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--bg-white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.05" d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,213.3C672,192,768,128,864,128C960,128,1056,192,1152,213.3C1248,235,1344,213,1392,202.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 0.8;
}

.cta .container {
    position: relative;
    z-index: 2;
}

.cta h2 {
    font-size: 2.25rem;
    margin-bottom: 1.25rem;
    color: var(--bg-white);
    font-weight: 700;
}

.cta p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.25rem;
}

.cta-btn {
    padding: 1rem 2rem;
    border-radius: var(--radius-full);
    font-size: 1rem;
    font-weight: 600;
    transition: var(--transition-normal);
}

.cta-btn-primary {
    background-color: var(--warning-color);
    color: var(--text-dark);
    box-shadow: var(--shadow-md);
}

.cta-btn-primary:hover {
    background-color: var(--warning-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.cta-btn-secondary {
    background-color: transparent;
    color: var(--bg-white);
    border: 2px solid var(--bg-white);
}

.cta-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .testimonial-card {
        width: 100%;
    }

    .filter-item {
        max-width: 180px;
    }
}

@media (max-width: 992px) {
    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.125rem;
    }

    .filter-item {
        max-width: 160px;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 4rem 0 5rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .search-bar {
        flex-direction: column;
    }

    .search-bar input {
        max-width: 100%;
        width: 100%;
    }

    .search-btn {
        width: 100%;
        margin-top: 0.625rem;
    }

    .filters {
        flex-wrap: wrap;
        gap: 0.625rem;
    }

    .filter-item {
        flex: 0 0 calc(50% - 0.3125rem);
        max-width: calc(50% - 0.3125rem);
    }

    .featured-jobs-grid {
        grid-template-columns: 1fr;
    }

    .benefits-container {
        grid-template-columns: 1fr;
    }

    .testimonials-container {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
    }

    .section-title {
        font-size: 1.75rem;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 3rem 0 4rem;
    }

    .hero h1 {
        font-size: 1.75rem;
    }

    .hero p {
        font-size: 0.9375rem;
        margin-bottom: 1.5rem;
    }

    .filter-item {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .search-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9375rem;
    }

    .search-bar input {
        padding: 0.75rem 1rem;
        font-size: 0.9375rem;
    }

    .filters select {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }

    .section-title {
        font-size: 1.5rem;
    }
}
