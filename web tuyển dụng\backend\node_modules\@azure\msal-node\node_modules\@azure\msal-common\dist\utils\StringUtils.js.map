{"version": 3, "file": "StringUtils.js", "sources": ["../../src/utils/StringUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { DecodedAuthToken } from \"../account/DecodedAuthToken\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\n\r\n/**\r\n * @hidden\r\n */\r\nexport class StringUtils {\r\n\r\n    /**\r\n     * decode a JWT\r\n     *\r\n     * @param authToken\r\n     */\r\n    static decodeAuthToken(authToken: string): DecodedAuthToken {\r\n        if (StringUtils.isEmpty(authToken)) {\r\n            throw ClientAuthError.createTokenNullOrEmptyError(authToken);\r\n        }\r\n        const tokenPartsRegex = /^([^\\.\\s]*)\\.([^\\.\\s]+)\\.([^\\.\\s]*)$/;\r\n        const matches = tokenPartsRegex.exec(authToken);\r\n        if (!matches || matches.length < 4) {\r\n            throw ClientAuthError.createTokenParsingError(`Given token is malformed: ${JSON.stringify(authToken)}`);\r\n        }\r\n        const crackedToken: DecodedAuthToken = {\r\n            header: matches[1],\r\n            JWSPayload: matches[2],\r\n            JWSSig: matches[3]\r\n        };\r\n        return crackedToken;\r\n    }\r\n\r\n    /**\r\n     * Check if a string is empty.\r\n     *\r\n     * @param str\r\n     */\r\n    static isEmpty(str?: string): boolean {\r\n        return (typeof str === \"undefined\" || !str || 0 === str.length);\r\n    }\r\n\r\n    /**\r\n     * Check if stringified object is empty\r\n     * @param strObj \r\n     */\r\n    static isEmptyObj(strObj?: string): boolean {\r\n        if (strObj && !StringUtils.isEmpty(strObj)) {\r\n            try {\r\n                const obj = JSON.parse(strObj);\r\n                return Object.keys(obj).length === 0;\r\n            } catch (e) {}\r\n        }\r\n        return true;\r\n    }\r\n\r\n    static startsWith(str: string, search: string): boolean {\r\n        return str.indexOf(search) === 0;\r\n    }\r\n\r\n    static endsWith(str: string, search: string): boolean {\r\n        return (str.length >= search.length) && (str.lastIndexOf(search) === (str.length - search.length));\r\n    }\r\n\r\n    /**\r\n     * Parses string into an object.\r\n     *\r\n     * @param query\r\n     */\r\n    static queryStringToObject<T>(query: string): T {\r\n        const obj: {} = {};\r\n        const params = query.split(\"&\");\r\n        const decode = (s: string) => decodeURIComponent(s.replace(/\\+/g, \" \"));\r\n        params.forEach((pair) => {\r\n            if (pair.trim()) {\r\n                const [key, value] = pair.split(/=(.+)/g, 2); // Split on the first occurence of the '=' character\r\n                if (key && value) {\r\n                    obj[decode(key)] = decode(value);\r\n                }\r\n            }\r\n        });\r\n        return obj as T;\r\n    }\r\n\r\n    /**\r\n     * Trims entries in an array.\r\n     *\r\n     * @param arr\r\n     */\r\n    static trimArrayEntries(arr: Array<string>): Array<string> {\r\n        return arr.map(entry => entry.trim());\r\n    }\r\n\r\n    /**\r\n     * Removes empty strings from array\r\n     * @param arr\r\n     */\r\n    static removeEmptyStringsFromArray(arr: Array<string>): Array<string> {\r\n        return arr.filter(entry => {\r\n            return !StringUtils.isEmpty(entry);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Attempts to parse a string into JSON\r\n     * @param str\r\n     */\r\n    static jsonParseHelper<T>(str: string): T | null {\r\n        try {\r\n            return JSON.parse(str) as T;\r\n        } catch (e) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Tests if a given string matches a given pattern, with support for wildcards and queries.\r\n     * @param pattern Wildcard pattern to string match. Supports \"*\" for wildcards and \"?\" for queries\r\n     * @param input String to match against\r\n     */\r\n    static matchPattern(pattern: string, input: string): boolean {\r\n        /**\r\n         * Wildcard support: https://stackoverflow.com/a/3117248/4888559\r\n         * Queries: replaces \"?\" in string with escaped \"\\?\" for regex test\r\n         */\r\n        const regex: RegExp = new RegExp(pattern.replace(/\\\\/g, \"\\\\\\\\\").replace(/\\*/g, \"[^ ]*\").replace(/\\?/g, \"\\\\\\?\")); // eslint-disable-line security/detect-non-literal-regexp\r\n\r\n        return regex.test(input);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAKH;;AAEG;AACH,IAAA,WAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,WAAA,GAAA;KAwHC;AAtHG;;;;AAIG;IACI,WAAe,CAAA,eAAA,GAAtB,UAAuB,SAAiB,EAAA;AACpC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAChC,YAAA,MAAM,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;AAChE,SAAA;QACD,IAAM,eAAe,GAAG,sCAAsC,CAAC;QAC/D,IAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,YAAA,MAAM,eAAe,CAAC,uBAAuB,CAAC,4BAA6B,GAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAG,CAAC,CAAC;AAC3G,SAAA;AACD,QAAA,IAAM,YAAY,GAAqB;AACnC,YAAA,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAClB,YAAA,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;AACtB,YAAA,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;SACrB,CAAC;AACF,QAAA,OAAO,YAAY,CAAC;KACvB,CAAA;AAED;;;;AAIG;IACI,WAAO,CAAA,OAAA,GAAd,UAAe,GAAY,EAAA;AACvB,QAAA,QAAQ,OAAO,GAAG,KAAK,WAAW,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE;KACnE,CAAA;AAED;;;AAGG;IACI,WAAU,CAAA,UAAA,GAAjB,UAAkB,MAAe,EAAA;QAC7B,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACxC,IAAI;gBACA,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AACxC,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AACjB,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAEM,IAAA,WAAA,CAAA,UAAU,GAAjB,UAAkB,GAAW,EAAE,MAAc,EAAA;QACzC,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpC,CAAA;AAEM,IAAA,WAAA,CAAA,QAAQ,GAAf,UAAgB,GAAW,EAAE,MAAc,EAAA;AACvC,QAAA,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;KACtG,CAAA;AAED;;;;AAIG;IACI,WAAmB,CAAA,mBAAA,GAA1B,UAA8B,KAAa,EAAA;QACvC,IAAM,GAAG,GAAO,EAAE,CAAC;QACnB,IAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,IAAM,MAAM,GAAG,UAAC,CAAS,EAAK,EAAA,OAAA,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA,EAAA,CAAC;AACxE,QAAA,MAAM,CAAC,OAAO,CAAC,UAAC,IAAI,EAAA;AAChB,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;AACP,gBAAA,IAAA,KAAe,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,EAArC,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAA2B,EAAA,CAAA,CAAA,CAAA,CAAC;gBAC7C,IAAI,GAAG,IAAI,KAAK,EAAE;oBACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACpC,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,GAAQ,CAAC;KACnB,CAAA;AAED;;;;AAIG;IACI,WAAgB,CAAA,gBAAA,GAAvB,UAAwB,GAAkB,EAAA;AACtC,QAAA,OAAO,GAAG,CAAC,GAAG,CAAC,UAAA,KAAK,EAAA,EAAI,OAAA,KAAK,CAAC,IAAI,EAAE,CAAZ,EAAY,CAAC,CAAC;KACzC,CAAA;AAED;;;AAGG;IACI,WAA2B,CAAA,2BAAA,GAAlC,UAAmC,GAAkB,EAAA;AACjD,QAAA,OAAO,GAAG,CAAC,MAAM,CAAC,UAAA,KAAK,EAAA;AACnB,YAAA,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;IACI,WAAe,CAAA,eAAA,GAAtB,UAA0B,GAAW,EAAA;QACjC,IAAI;AACA,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAM,CAAC;AAC/B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACI,IAAA,WAAA,CAAA,YAAY,GAAnB,UAAoB,OAAe,EAAE,KAAa,EAAA;AAC9C;;;AAGG;AACH,QAAA,IAAM,KAAK,GAAW,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAEhH,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC5B,CAAA;IACL,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA;;;;"}