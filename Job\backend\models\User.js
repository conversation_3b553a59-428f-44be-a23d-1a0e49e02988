const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true
    },
    password: {
        type: String,
        required: true
    },
    fullName: {
        type: String,
        required: true,
        trim: true
    },
    phone: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        enum: ['candidate', 'employer'],
        default: 'candidate'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    lastLoginAt: {
        type: Date
    }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    
    try {
        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
};

// Register a new user
userSchema.statics.register = async function(userData) {
    try {
        const user = new this(userData);
        await user.save();
        
        // Generate JWT token
        const token = jwt.sign(
            { id: user._id, email: user.email, role: user.role },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        return { user, token };
    } catch (error) {
        console.error('User registration error:', error);
        throw error;
    }
};

// Login user
userSchema.statics.login = async function(email, password) {
    try {
        const user = await this.findOne({ email });
        
        if (!user) {
            throw new Error('Email hoặc mật khẩu không đúng');
        }
        
        // Check password
        const isMatch = await user.comparePassword(password);
        
        if (!isMatch) {
            throw new Error('Email hoặc mật khẩu không đúng');
        }
        
        // Remove password from user object
        user.password = undefined;
        
        // Generate JWT token
        const token = jwt.sign(
            { id: user._id, email: user.email, role: user.role },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN }
        );
        
        return { user, token };
    } catch (error) {
        console.error('User login error:', error);
        throw error;
    }
};

// Get user by ID
userSchema.statics.getById = async function(userId) {
    try {
        const user = await this.findById(userId);
        return user;
    } catch (error) {
        console.error('Get user error:', error);
        throw error;
    }
};

// Update user
userSchema.statics.update = async function(userId, userData) {
    try {
        const user = await this.findById(userId);
        
        if (!user) {
            throw new Error('Người dùng không tồn tại');
        }
        
        user.fullName = userData.fullName;
        user.phone = userData.phone || null;
        await user.save();
        
        return user;
    } catch (error) {
        console.error('Update user error:', error);
        throw error;
    }
};

// Change password
userSchema.statics.changePassword = async function(userId, oldPassword, newPassword) {
    try {
        const user = await this.findById(userId);
        
        if (!user) {
            throw new Error('Người dùng không tồn tại');
        }
        
        // Check old password
        const isMatch = await user.comparePassword(oldPassword);
        
        if (!isMatch) {
            throw new Error('Mật khẩu cũ không đúng');
        }
        
        // Hash new password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(newPassword, salt);
        
        // Update password
        user.password = hashedPassword;
        await user.save();
        
        return true;
    } catch (error) {
        console.error('Change password error:', error);
        throw error;
    }
};

module.exports = mongoose.model('User', userSchema);
